# Sideband Out Xtor Specification

## 1. Overview

This document specifies the design for a `sideband_out` transactor (Xtor) for use in emulation environments. The Xtor's primary purpose is to capture signal changes (events) from the Device Under Test (DUT) and transmit them to the software side without any validation or gating logic.

The design consists of a Bus Functional Model (BFM) implemented in SystemVerilog and a software-side Bridge. This specification focuses on the BFM and its interaction with the software Bridge via the DPI-C interface.

## 2. Core Features

- **Vector-Based Event Capture**: The BFM detects changes on the entire sideband_in vector and sends the complete vector to software.
- **Parameterized Signal Width**: The number of sideband signals the BFM can monitor is a configurable Verilog `parameter`.
- **Unified Delay Configuration**: All signals share a single configurable delay value that can be set by software.
- **Dual-Mode Operation**: The BFM supports both **blocking** and **non-blocking** DPI-C calls for software notification, making it compatible with both RTL simulators and hardware emulators.
- **Software-Side Bit Analysis**: The software side receives the complete signal vector and determines which individual bits have changed.

## 3. Detailed Logic and Behavior

### 3.1. Vector-Based State Machine

The BFM implements a single state machine that monitors the entire sideband_in vector. The state machine is simplified to capture vector changes as a whole.

```mermaid
stateDiagram-v2
    direction LR
    [*] --> IDLE

    IDLE --> DELAY: on sideband_in vector change
    note on link: Store new vector value

    DELAY --> SEND: when delay_counter == 0

    SEND --> IDLE: on DPI-C call completion
    note on link: Call h2s_notify(vector_value)

    DELAY --> DELAY: on sideband_in vector change
    note on link: Queue new vector value
```

### 3.2. Operational Flow

1.  **Trigger**: Any change on the `sideband_in` vector immediately triggers the state machine. Both the old value and new value of the entire `sideband_in` vector are stored together in a circular FIFO queue.
2.  **FIFO Management**: The FIFO stores paired values (old_vector, new_vector) in each entry. If the FIFO is full when a new vector change occurs, the oldest entry is overwritten (circular buffer behavior). This ensures the most recent vector transitions are preserved when the change rate exceeds the FIFO capacity.
3.  **Delay**: The state machine transitions to `DELAY` state and loads the unified delay value into a countdown timer. Additional vector changes during the delay period are stored in the FIFO according to the overwrite policy.
4.  **Notification**: Once the delay counter reaches zero, the state machine transitions to `SEND`. The BFM calls the appropriate DPI-C notification function, passing both the old vector value and new vector value.
5.  **Queue Processing**: After sending one event, if there are more queued vector transitions, the state machine immediately processes the next event with the configured delay.
6.  **Software Processing**: The software side receives both old and new vector values and can immediately determine which individual bits have changed using XOR operation: `changed_bits = old_vector ^ new_vector`.

## 4. BFM Module Interface

```systemverilog
module sideband_out_bfm #(
    parameter INSTANCE_ID = 0,
    parameter NUM_SIGNALS = 32,
    parameter DELAY_WIDTH = 8,  // Allows for a max delay of 255 cycles
    parameter QUEUE_DEPTH = 16  // FIFO depth for each channel to prevent signal loss
) (
    input logic clk,
    input logic rst_n,

    // From DUT - only sideband signals, no valid signals
    input logic [NUM_SIGNALS-1:0] sideband_in
);

// ... DPI-C and internal logic ...

endmodule
```

## 5. DPI-C Interface

### 5.1. `import` Functions (Hardware to Software)

These functions are implemented in the C/C++ Bridge and called by the BFM.

-   **`h2s_sideband_init(input int id, input int num_signals)`**
    -   Called once at the beginning of the simulation.
    -   Passes static configuration from the BFM to the software.
    -   The software side is expected to call `s2h_set_mode` and `s2h_set_delay` from within this function's context.

-   **`h2s_sideband_notify_vector_nb(input int id, input bit [NUM_SIGNALS-1:0] old_vector, input bit [NUM_SIGNALS-1:0] new_vector)`**
    -   **Non-blocking** version for use in emulation.
    -   Declared with `(* is_nonblocking_dpi = 1 *)`.
    -   Notifies the software of a vector change, passing both the old and new sideband_in vector values.

-   **`h2s_sideband_notify_vector_b(input int id, input bit [NUM_SIGNALS-1:0] old_vector, input bit [NUM_SIGNALS-1:0] new_vector)`**
    -   **Blocking** version for use in simulation.
    -   Notifies the software of a vector change, passing both the old and new sideband_in vector values.

### 5.2. `export` Functions (Software to Hardware)

These functions are implemented in the BFM as Verilog `task`s and can be called by the software.

-   **`s2h_set_mode(input int mode)`**
    -   Called by software during initialization to select the operational mode.
    -   `mode = 0`: Non-blocking (default)
    -   `mode = 1`: Blocking

-   **`s2h_set_delay(input int delay_val)`**
    -   Called by software to set the unified delay in clock cycles for all channels.
    -   This delay applies to all sideband signals uniformly.

## 6. Key Design Considerations

### 6.1. FIFO-Based Vector Transition Buffering

To handle high-frequency vector changes while maintaining area efficiency:

- A single circular FIFO queue stores vector transitions (old_vector, new_vector pairs) with configurable depth (`QUEUE_DEPTH` parameter)
- Each FIFO entry is double-width to accommodate both old and new vector values
- The FIFO depth is independent of the delay value, making it area-predictable
- When the FIFO is full and a new vector change occurs, the oldest entry is overwritten
- This design prioritizes the most recent vector transitions when the change rate exceeds FIFO capacity
- The FIFO depth should be chosen based on expected vector change patterns and area constraints

### 6.2. Area vs. Performance Trade-off

- **FIFO Depth**: A smaller FIFO depth reduces area but may lose some vector transitions during high-activity periods
- **Overwrite Policy**: When FIFO overflows, newer vector transitions overwrite older ones, ensuring the most recent transitions are preserved
- **Recommended FIFO Depth**: Typically 4-16 entries should be sufficient for most applications, balancing area and functionality
- **Vector Width Impact**: FIFO width scales with 2×NUM_SIGNALS parameter (double-width for old+new values), affecting total area

### 6.3. Unified Delay Configuration

- All channels share a single delay value that can be configured by software
- This simplifies the interface and reduces configuration complexity
- The delay can be changed dynamically during simulation if needed
- The delay value is independent of FIFO depth, allowing flexible configuration

### 6.4. Software-Side Bit Change Detection

The software side receives both old and new vector values, making bit change detection straightforward:

- **Direct Comparison**: Software receives both old_vector and new_vector in the same DPI-C call
- **Bit-Level Analysis**: Software can immediately use XOR operation to identify changed bits: `changed_bits = old_vector ^ new_vector`
- **No State Maintenance**: Software doesn't need to maintain previous vector state
- **Individual Bit Processing**: Software can iterate through changed bits and process each one individually
- **Flexibility**: This approach allows software to implement custom filtering, grouping, or priority handling

### 6.5. Mode Selection

- **Non-blocking mode**: Suitable for emulation environments where DPI-C calls should not block the hardware simulation
- **Blocking mode**: Suitable for RTL simulation where synchronization between hardware and software is required

## 7. Implementation Plan

With the specification finalized, the following implementation plan will be executed.

<update_todo_list>
<todos>
[x] 确认并最终确定 `sideband_out` xtor 的详细需求
[x] 将最终需求写入 `spec.md` (已更新为简化版本)
[ ] 设计 BFM 的 Verilog/SystemVerilog 模块接口和参数
[ ] 设计 BFM 内部逻辑 (实现向量检测状态机、循环FIFO队列和统一延迟计数器)
[ ] 编写 BFM 的 RTL 代码 (`sideband_out_bfm.sv`)
[ ] 设计并实现 DPI-C 接口的 C++ 头文件 (`sideband_out_dpi.h`)
[ ] 编写用于测试的伪 Bridge (C/C++)，实现 `h2s_*` 函数
[ ] 编写 SystemVerilog Testbench，包含 DUT 伪模型和 BFM 实例化
[ ] 在 Testbench 中通过 `s2h_*` 函数配置 BFM
[ ] 编写测试序列，覆盖 blocking/non-blocking 模式、统一延迟、高频向量变化和FIFO溢出场景
</todos>
</update_todo_list>