# Sideband Out Xtor Specification

## 1. Overview

This document specifies the design for a `sideband_out` transactor (Xtor) for use in emulation environments. The Xtor's primary purpose is to capture signal changes (events) from the Device Under Test (DUT) and report them to a software model.

The design consists of a Bus Functional Model (BFM) implemented in SystemVerilog and a software-side Bridge. This specification focuses on the BFM and its interaction with the software Bridge via the DPI-C interface.

## 2. Core Features

- **Edge-Sensitive Event Capture**: The BFM detects both rising and falling edges on its input signals.
- **Parameterized Signal Width**: The number of sideband signals the BFM can monitor is a configurable Verilog `parameter`.
- **Independent Channels**: Each sideband signal and its corresponding `valid` signal operate as an independent channel with its own state.
- **Gated and Delayed Transmission**: The transmission of a signal event is a two-step process:
    1.  It must be gated by a `valid` signal.
    2.  It is then delayed by a configurable number of clock cycles before being sent to the software.
- **Dual-Mode Operation**: The BFM supports both **blocking** and **non-blocking** DPI-C calls for software notification, making it compatible with both RTL simulators and hardware emulators.

## 3. Detailed Logic and Behavior

### 3.1. Per-Channel State Machine

The BFM implements an independent state machine for each sideband channel `i`.

```mermaid
stateDiagram-v2
    direction LR
    [*] --> IDLE

    IDLE --> WAIT_VALID: on sideband_in[i] edge
    note on link: Store new value of sideband_in[i]

    WAIT_VALID --> DELAY: on sideband_valid[i] rising edge
    note on link: Load delay_counter = configured_delay[i]

    DELAY --> SEND: when delay_counter == 0

    SEND --> IDLE: on DPI-C call completion
    note on link: Call h2s_notify(i, stored_value)
```

### 3.2. Operational Flow

1.  **Trigger**: A rising or falling edge on `sideband_in[i]` arms the channel's state machine, which transitions from `IDLE` to `WAIT_VALID`. The new value of `sideband_in[i]` is stored internally.
2.  **Gate**: A **rising edge** on the corresponding `sideband_valid[i]` signal gates the event. The state machine transitions from `WAIT_VALID` to `DELAY`. The channel's configured delay value is loaded into a countdown timer.
3.  **Delay**: The state machine remains in the `DELAY` state for the configured number of clock cycles. A delay of `0` causes the state machine to transition to `SEND` in the clock cycle immediately following the `valid` edge.
4.  **Notification**: Once the delay counter reaches zero, the state machine transitions to `SEND`. The BFM calls the appropriate DPI-C notification function, passing the channel index (`i`) and the stored signal value.

## 4. BFM Module Interface

```systemverilog
module sideband_out_bfm #(
    parameter INSTANCE_ID = 0,
    parameter NUM_SIGNALS = 32,
    parameter DELAY_WIDTH = 8  // Allows for a max delay of 255 cycles
) (
    input logic clk,
    input logic rst_n,

    // From DUT
    input logic [NUM_SIGNALS-1:0] sideband_in,
    input logic [NUM_SIGNALS-1:0] sideband_valid
);

// ... DPI-C and internal logic ...

endmodule
```

## 5. DPI-C Interface

### 5.1. `import` Functions (Hardware to Software)

These functions are implemented in the C/C++ Bridge and called by the BFM.

-   **`h2s_sideband_init(input int id, input int num_signals)`**
    -   Called once at the beginning of the simulation.
    -   Passes static configuration from the BFM to the software.
    -   The software side is expected to call `s2h_set_mode` from within this function's context.

-   **`h2s_sideband_notify_change_nb(input int id, input int signal_idx, input bit signal_val)`**
    -   **Non-blocking** version for use in emulation.
    -   Declared with `(* is_nonblocking_dpi = 1 *)`.
    -   Notifies the software of a signal change.

-   **`h2s_sideband_notify_change_b(input int id, input int signal_idx, input bit signal_val)`**
    -   **Blocking** version for use in simulation.
    -   Notifies the software of a signal change and waits for the function to return.

### 5.2. `export` Functions (Software to Hardware)

These functions are implemented in the BFM as Verilog `task`s and can be called by the software.

-   **`s2h_set_mode(input int mode)`**
    -   Called by software during initialization to select the operational mode.
    -   `mode = 0`: Non-blocking (default)
    -   `mode = 1`: Blocking

-   **`s2h_set_delay(input int signal_idx, input int delay_val)`**
    -   Called by software to set the delay in clock cycles for a specific channel.

## 6. Plan

With the specification finalized, the following implementation plan will be executed.

<update_todo_list>
<todos>
[x] 确认并最终确定 `sideband_out` xtor 的详细需求
[x] 将最终需求写入 `spec.md`
[-] 设计 BFM 的 Verilog/SystemVerilog 模块接口和参数
[ ] 设计 BFM 内部逻辑 (实现 `NUM_SIGNALS` 并行的状态机和延迟计数器)
[ ] 编写 BFM 的 RTL 代码 (`sideband_out_bfm.sv`)
[ ] 设计并实现 DPI-C 接口的 C++ 头文件 (`sideband_out_dpi.h`)
[ ] 编写用于测试的伪 Bridge (C/C++)，实现 `h2s_*` 函数
[ ] 编写 SystemVerilog Testbench，包含 DUT 伪模型和 BFM 实例化
[ ] 在 Testbench 中通过 `s2h_*` 函数配置 BFM
[ ] 编写测试序列，覆盖 blocking/non-blocking 模式、不同延迟和并发信号
</todos>
</update_todo_list>