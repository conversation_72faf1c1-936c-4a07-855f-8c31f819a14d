//
// sideband_out_bfm.sv
//
// Bus Functional Model for Sideband Out signals.
//
// This BFM captures edge-sensitive signal changes from the DUT,
// applies a configurable unified delay, and then reports the event
// to a software model via DPI-C. Uses circular FIFO to handle
// high-frequency signal changes with overwrite policy.
//

module sideband_out_bfm #(
    parameter INSTANCE_ID = 0,
    parameter NUM_SIGNALS = 32,
    parameter DELAY_WIDTH = 8,
    parameter QUEUE_DEPTH = 16  // Renamed from FIFO_DEPTH for consistency with spec
) (
    input bit clk,
    input bit rst_n,

    // From DUT - only sideband signals, no valid signals
    input bit [NUM_SIGNALS-1:0] sideband_in
);

//----------------------------------------------------------------
// DPI-C Interface
//----------------------------------------------------------------

// Import functions (Hardware to Software)
import "DPI-C" context function void h2s_sideband_init(
    input int id,
    input int num_signals
);

(* is_nonblocking_dpi = 1 *)
import "DPI-C" function void h2s_sideband_notify_change_nb(
    input int id,
    input int signal_idx,
    input bit signal_val
);

import "DPI-C" function void h2s_sideband_notify_change_b(
    input int id,
    input int signal_idx,
    input bit signal_val
);

// Export functions (Software to Hardware)
export "DPI-C" function s2h_set_mode;
export "DPI-C" function s2h_set_delay;

//----------------------------------------------------------------
// Internal State and Logic
//----------------------------------------------------------------

// Unified delay configuration for all signals
bit [DELAY_WIDTH-1:0] configured_delay;

// Shadow register for edge detection on sideband_in
bit [NUM_SIGNALS-1:0] sideband_in_d;

// State machine for processing events from the FIFO
typedef enum logic [1:0] {
    PROC_IDLE,
    PROC_DELAY,
    PROC_NOTIFY
} processor_state_e;

processor_state_e   proc_state;
bit [DELAY_WIDTH-1:0] delay_counter;

// Mode selection (0=non-blocking, 1=blocking)
bit is_blocking_mode;

//----------------------------------------------------------------
// Pipelining FIFO for event buffering
//----------------------------------------------------------------

// Event structure for FIFO
localparam SIGNAL_IDX_WIDTH = $clog2(NUM_SIGNALS);
typedef struct packed {
    logic [SIGNAL_IDX_WIDTH-1:0] index;
    logic                        value;
} event_t;

event_t             current_event;

// FIFO signals
logic fifo_wr_en;
logic fifo_rd_en;
logic fifo_full;
logic fifo_empty;
event_t fifo_wr_data;
event_t fifo_rd_data;

// FIFO instance
fifo #(
    .DATA_WIDTH($bits(event_t)),
    .DEPTH(QUEUE_DEPTH)
) event_fifo (
    .clk(clk),
    .rst_n(rst_n),
    .wr_en(fifo_wr_en),
    .wr_data(fifo_wr_data),
    .full(fifo_full),
    .rd_en(fifo_rd_en),
    .rd_data(fifo_rd_data),
    .empty(fifo_empty)
);

//----------------------------------------------------------------
// DPI-C Export Implementations
//----------------------------------------------------------------

function void s2h_set_mode(input int mode);
    is_blocking_mode = (mode == 1);
endfunction

function void s2h_set_delay(input int delay_val);
    configured_delay = delay_val;
endfunction

//----------------------------------------------------------------
// Main Logic
//----------------------------------------------------------------

// Initialization call at the start of simulation
initial begin
    h2s_sideband_init(INSTANCE_ID, NUM_SIGNALS);
end

//----------------------------------------------------------------
// Event Detection and Enqueue Logic
//----------------------------------------------------------------
bit [NUM_SIGNALS-1:0] sideband_edge;
assign sideband_edge = sideband_in ^ sideband_in_d;

// Priority encoder to handle multiple simultaneous edges
logic [SIGNAL_IDX_WIDTH-1:0] edge_priority_idx;
logic edge_found;

always_comb begin
    edge_found = 1'b0;
    edge_priority_idx = '0;

    // Find the first (lowest index) signal with an edge
    for (int i = 0; i < NUM_SIGNALS; i++) begin
        if (sideband_edge[i] && !edge_found) begin
            edge_found = 1'b1;
            edge_priority_idx = i;
        end
    end
end

always_ff @(posedge clk) begin
    if (!rst_n) begin
        sideband_in_d <= '0;
        fifo_wr_en    <= 1'b0;
    end else begin
        sideband_in_d <= sideband_in;
        fifo_wr_en    <= 1'b0; // Default to not writing

        // Detect edges on sideband_in and enqueue events
        // With circular FIFO, we always write when there's an edge
        // Priority encoder ensures only one event per cycle, but all edges
        // will eventually be captured in subsequent cycles due to edge detection
        if (edge_found) begin
            fifo_wr_en   <= 1'b1;
            fifo_wr_data <= event_t'{index: edge_priority_idx, value: sideband_in[edge_priority_idx]};
        end
    end
end

//----------------------------------------------------------------
// Event Processing and Dequeue Logic
//----------------------------------------------------------------
always_ff @(posedge clk) begin
    // Synchronous Reset
    if (!rst_n) begin
        proc_state       <= PROC_IDLE;
        delay_counter    <= '0;
        fifo_rd_en       <= 1'b0;
        is_blocking_mode <= 1'b0; // Default to non-blocking
        configured_delay <= '0;   // Default delay is 0
    end else begin
        // Default assignments
        fifo_rd_en <= 1'b0;

        case (proc_state)
            PROC_IDLE: begin
                if (!fifo_empty) begin
                    fifo_rd_en    <= 1'b1;
                    current_event <= fifo_rd_data;
                    if (configured_delay == 0) begin
                        proc_state <= PROC_NOTIFY;
                    end else begin
                        proc_state    <= PROC_DELAY;
                        delay_counter <= configured_delay - 1;
                    end
                end
            end

            PROC_DELAY: begin
                if (delay_counter == 0) begin
                    proc_state <= PROC_NOTIFY;
                end else begin
                    delay_counter <= delay_counter - 1;
                end
            end

            PROC_NOTIFY: begin
                if (is_blocking_mode) begin
                    h2s_sideband_notify_change_b(INSTANCE_ID, current_event.index, current_event.value);
                end else begin
                    h2s_sideband_notify_change_nb(INSTANCE_ID, current_event.index, current_event.value);
                end
                proc_state <= PROC_IDLE;
            end

            default: begin
                proc_state <= PROC_IDLE;
            end
        endcase
    end
end

endmodule