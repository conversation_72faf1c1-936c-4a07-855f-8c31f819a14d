//
// fifo.sv
//
// A generic, parameterized circular FIFO implementation with overwrite capability.
// When full, new writes overwrite the oldest entries.
//

module fifo #(
    parameter DATA_WIDTH = 32,
    parameter DEPTH      = 8
) (
    input  bit                  clk,
    input  bit                  rst_n,

    // Write interface - always accepts writes (circular overwrite)
    input  bit                  wr_en,
    input  bit [DATA_WIDTH-1:0] wr_data,
    output bit                  full,

    // Read interface
    input  bit                  rd_en,
    output bit [DATA_WIDTH-1:0] rd_data,
    output bit                  empty
);

    localparam ADDR_WIDTH = $clog2(DEPTH);

    // Memory array and pointers
    logic [DATA_WIDTH-1:0] mem [DEPTH-1:0];
    logic [ADDR_WIDTH-1:0] wr_ptr;
    logic [ADDR_WIDTH-1:0] rd_ptr;
    logic [ADDR_WIDTH:0]   count; // One extra bit to distinguish full from empty

    //----------------------------------------------------------------
    // Logic
    //----------------------------------------------------------------

    assign full = (count == DEPTH);
    assign empty = (count == 0);
    assign rd_data = mem[rd_ptr];

    always_ff @(posedge clk) begin
        if (!rst_n) begin
            wr_ptr <= '0;
            rd_ptr <= '0;
            count  <= '0;
        end else begin
            // Write operation - always accepts writes (circular overwrite when full)
            if (wr_en) begin
                mem[wr_ptr] <= wr_data;
                wr_ptr <= (wr_ptr == DEPTH - 1) ? '0 : wr_ptr + 1;

                // If FIFO is full and we're writing, advance read pointer (overwrite oldest)
                if (full) begin
                    rd_ptr <= (rd_ptr == DEPTH - 1) ? '0 : rd_ptr + 1;
                end
            end

            // Read operation
            if (rd_en && !empty) begin
                rd_ptr <= (rd_ptr == DEPTH - 1) ? '0 : rd_ptr + 1;
            end

            // Update count - circular overwrite logic
            if (wr_en && !full && !(rd_en && !empty)) begin
                count <= count + 1;
            end else if (!(wr_en) && rd_en && !empty) begin
                count <= count - 1;
            end
            // When full and writing, count stays the same (overwrite mode)
            // When both read and write occur simultaneously, count stays the same
        end
    end

endmodule