/*
 * vector_processing_example.cpp
 * 
 * Example showing how software side can process vector changes
 * and detect individual bit changes.
 */

#include <iostream>
#include <vector>
#include <bitset>

class VectorChangeProcessor {
private:
    unsigned int previous_vector;
    bool first_vector;
    
public:
    VectorChangeProcessor() : previous_vector(0), first_vector(true) {}
    
    void process_vector_change(unsigned int new_vector) {
        std::cout << "Received vector: 0x" << std::hex << new_vector 
                  << " (binary: " << std::bitset<8>(new_vector) << ")" << std::endl;
        
        if (first_vector) {
            std::cout << "  First vector received" << std::endl;
            first_vector = false;
        } else {
            // Calculate which bits changed
            unsigned int changed_bits = new_vector ^ previous_vector;
            
            if (changed_bits == 0) {
                std::cout << "  No bits changed" << std::endl;
            } else {
                std::cout << "  Changed bits: 0x" << std::hex << changed_bits 
                          << " (binary: " << std::bitset<8>(changed_bits) << ")" << std::endl;
                
                // Process each changed bit
                for (int bit = 0; bit < 8; bit++) {
                    if (changed_bits & (1U << bit)) {
                        int old_val = (previous_vector >> bit) & 1;
                        int new_val = (new_vector >> bit) & 1;
                        std::cout << "    Bit " << bit << ": " << old_val 
                                  << " -> " << new_val << std::endl;
                        
                        // Custom processing for specific bits
                        handle_bit_change(bit, old_val, new_val);
                    }
                }
            }
        }
        
        previous_vector = new_vector;
        std::cout << std::endl;
    }
    
private:
    void handle_bit_change(int bit_index, int old_value, int new_value) {
        // Example: Custom handling for different bits
        switch (bit_index) {
            case 0:
                std::cout << "      -> Interrupt enable bit changed" << std::endl;
                break;
            case 1:
                std::cout << "      -> Status flag bit changed" << std::endl;
                break;
            case 2:
            case 3:
                std::cout << "      -> Control bit " << bit_index << " changed" << std::endl;
                break;
            default:
                std::cout << "      -> General purpose bit " << bit_index << " changed" << std::endl;
                break;
        }
        
        // Example: Trigger actions based on bit changes
        if (bit_index == 0 && new_value == 1) {
            std::cout << "      -> Enabling interrupt handling" << std::endl;
        } else if (bit_index == 1 && new_value == 0) {
            std::cout << "      -> Clearing status flag" << std::endl;
        }
    }
};

// Example usage demonstrating the vector processing
int main() {
    VectorChangeProcessor processor;
    
    std::cout << "=== Vector Change Processing Example ===" << std::endl;
    std::cout << std::endl;
    
    // Simulate a sequence of vector changes
    std::vector<unsigned int> test_vectors = {
        0x00,  // Initial state
        0x01,  // Set bit 0
        0x03,  // Set bit 1 (bit 0 remains set)
        0x02,  // Clear bit 0 (bit 1 remains set)
        0x0A,  // Set bit 3, clear bit 1, set bit 0
        0x00   // Clear all bits
    };
    
    for (unsigned int vector : test_vectors) {
        processor.process_vector_change(vector);
    }
    
    std::cout << "=== End of Example ===" << std::endl;
    
    return 0;
}

/*
 * Expected output:
 * 
 * === Vector Change Processing Example ===
 * 
 * Received vector: 0x0 (binary: 00000000)
 *   First vector received
 * 
 * Received vector: 0x1 (binary: 00000001)
 *   Changed bits: 0x1 (binary: 00000001)
 *     Bit 0: 0 -> 1
 *       -> Interrupt enable bit changed
 *       -> Enabling interrupt handling
 * 
 * Received vector: 0x3 (binary: 00000011)
 *   Changed bits: 0x2 (binary: 00000010)
 *     Bit 1: 0 -> 1
 *       -> Status flag bit changed
 * 
 * Received vector: 0x2 (binary: 00000010)
 *   Changed bits: 0x1 (binary: 00000001)
 *     Bit 0: 1 -> 0
 *       -> Interrupt enable bit changed
 * 
 * Received vector: 0xa (binary: 00001010)
 *   Changed bits: 0x8 (binary: 00001000)
 *     Bit 3: 0 -> 1
 *       -> Control bit 3 changed
 * 
 * Received vector: 0x0 (binary: 00000000)
 *   Changed bits: 0xa (binary: 00001010)
 *     Bit 1: 1 -> 0
 *       -> Status flag bit changed
 *       -> Clearing status flag
 *     Bit 3: 1 -> 0
 *       -> Control bit 3 changed
 * 
 * === End of Example ===
 */
