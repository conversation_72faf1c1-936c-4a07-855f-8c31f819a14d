/*
 * vector_processing_example.cpp
 * 
 * Example showing how software side can process vector changes
 * and detect individual bit changes.
 */

#include <iostream>
#include <vector>
#include <bitset>

class VectorTransitionProcessor {
public:
    VectorTransitionProcessor() {}

    void process_vector_transition(unsigned int old_vector, unsigned int new_vector) {
        std::cout << "Received vector transition: 0x" << std::hex << old_vector
                  << " -> 0x" << new_vector << std::endl;
        std::cout << "  Old: " << std::bitset<8>(old_vector)
                  << " New: " << std::bitset<8>(new_vector) << std::endl;

        // Calculate which bits changed - hardware provides both values!
        unsigned int changed_bits = old_vector ^ new_vector;

        if (changed_bits == 0) {
            std::cout << "  No bits changed (should not happen)" << std::endl;
        } else {
            std::cout << "  Changed bits: 0x" << std::hex << changed_bits
                      << " (binary: " << std::bitset<8>(changed_bits) << ")" << std::endl;

            // Process each changed bit
            for (int bit = 0; bit < 8; bit++) {
                if (changed_bits & (1U << bit)) {
                    int old_val = (old_vector >> bit) & 1;
                    int new_val = (new_vector >> bit) & 1;
                    std::cout << "    Bit " << bit << ": " << old_val
                              << " -> " << new_val << std::endl;

                    // Custom processing for specific bits
                    handle_bit_change(bit, old_val, new_val);
                }
            }
        }

        std::cout << std::endl;
    }
    
private:
    void handle_bit_change(int bit_index, int old_value, int new_value) {
        // Example: Custom handling for different bits
        switch (bit_index) {
            case 0:
                std::cout << "      -> Interrupt enable bit changed" << std::endl;
                break;
            case 1:
                std::cout << "      -> Status flag bit changed" << std::endl;
                break;
            case 2:
            case 3:
                std::cout << "      -> Control bit " << bit_index << " changed" << std::endl;
                break;
            default:
                std::cout << "      -> General purpose bit " << bit_index << " changed" << std::endl;
                break;
        }
        
        // Example: Trigger actions based on bit changes
        if (bit_index == 0 && new_value == 1) {
            std::cout << "      -> Enabling interrupt handling" << std::endl;
        } else if (bit_index == 1 && new_value == 0) {
            std::cout << "      -> Clearing status flag" << std::endl;
        }
    }
};

// Example usage demonstrating the vector transition processing
int main() {
    VectorTransitionProcessor processor;

    std::cout << "=== Vector Transition Processing Example ===" << std::endl;
    std::cout << std::endl;

    // Simulate a sequence of vector transitions (old -> new pairs)
    std::vector<std::pair<unsigned int, unsigned int>> test_transitions = {
        {0x00, 0x01},  // Set bit 0
        {0x01, 0x03},  // Set bit 1 (bit 0 remains set)
        {0x03, 0x02},  // Clear bit 0 (bit 1 remains set)
        {0x02, 0x0A},  // Set bit 3, clear bit 1, set bit 0
        {0x0A, 0x00}   // Clear all bits
    };

    for (auto& transition : test_transitions) {
        processor.process_vector_transition(transition.first, transition.second);
    }
    
    std::cout << "=== End of Example ===" << std::endl;
    
    return 0;
}

/*
 * Expected output:
 *
 * === Vector Transition Processing Example ===
 *
 * Received vector transition: 0x0 -> 0x1
 *   Old: 00000000 New: 00000001
 *   Changed bits: 0x1 (binary: 00000001)
 *     Bit 0: 0 -> 1
 *       -> Interrupt enable bit changed
 *       -> Enabling interrupt handling
 *
 * Received vector transition: 0x1 -> 0x3
 *   Old: 00000001 New: 00000011
 *   Changed bits: 0x2 (binary: 00000010)
 *     Bit 1: 0 -> 1
 *       -> Status flag bit changed
 *
 * Received vector transition: 0x3 -> 0x2
 *   Old: 00000011 New: 00000010
 *   Changed bits: 0x1 (binary: 00000001)
 *     Bit 0: 1 -> 0
 *       -> Interrupt enable bit changed
 *
 * Received vector transition: 0x2 -> 0xa
 *   Old: 00000010 New: 00001010
 *   Changed bits: 0x8 (binary: 00001000)
 *     Bit 3: 0 -> 1
 *       -> Control bit 3 changed
 *
 * Received vector transition: 0xa -> 0x0
 *   Old: 00001010 New: 00000000
 *   Changed bits: 0xa (binary: 00001010)
 *     Bit 1: 1 -> 0
 *       -> Status flag bit changed
 *       -> Clearing status flag
 *     Bit 3: 1 -> 0
 *       -> Control bit 3 changed
 *
 * === End of Example ===
 */
