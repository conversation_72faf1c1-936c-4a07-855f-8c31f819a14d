#include "sideband_out_dpi.h"
#include <cstdio>
#include <vector>
#include <chrono>
#include <thread>

// Logger class for blocking mode vector testing
class SidebandVectorBlockingLogger {
public:
    struct VectorEvent {
        unsigned int vector_val;
        std::chrono::steady_clock::time_point timestamp;
        std::vector<int> changed_bits;
    };

    void log_vector_event(int id, unsigned int vector_val) {
        auto now = std::chrono::steady_clock::now();
        printf("[SW Bridge] Blocking vector event from BFM ID %d: 0x%08X\n", id, vector_val);

        // Detect which bits changed compared to previous vector
        std::vector<int> changed_bits;
        if (!events.empty()) {
            unsigned int prev_vector = events.back().vector_val;
            unsigned int changed_mask = vector_val ^ prev_vector;

            // Find all changed bit positions
            for (int i = 0; i < 32; i++) {
                if (changed_mask & (1U << i)) {
                    changed_bits.push_back(i);
                    int new_val = (vector_val >> i) & 1;
                    int old_val = (prev_vector >> i) & 1;
                    printf("[SW Bridge]   Bit %d: %d -> %d\n", i, old_val, new_val);
                }
            }
        }

        events.push_back({vector_val, now, changed_bits});

        // Simulate some processing time to demonstrate blocking behavior
        std::this_thread::sleep_for(std::chrono::milliseconds(10));

        printf("[SW Bridge] Finished processing vector event\n");
    }

    void print_summary() {
        printf("\n[SW Bridge] === Blocking Vector Test Summary ===\n");
        printf("Total vector events received: %zu\n", events.size());

        int total_bit_changes = 0;
        for (const auto& event : events) {
            total_bit_changes += event.changed_bits.size();
        }
        printf("Total bit changes detected: %d\n", total_bit_changes);

        if (events.size() > 1) {
            printf("\nEvent timing analysis:\n");
            for (size_t i = 1; i < events.size(); i++) {
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    events[i].timestamp - events[i-1].timestamp);
                printf("  Gap between event %zu and %zu: %ld ms\n",
                       i-1, i, duration.count());
            }
        }
        printf("=====================================\n\n");
    }

private:
    std::vector<VectorEvent> events;
};

// Global instance of our logger
static SidebandVectorBlockingLogger logger;


//----------------------------------------------------------------
// DPI-C Import Function Implementations
//----------------------------------------------------------------

void h2s_sideband_init(int id, int num_signals) {
    printf("[SW Bridge] Initializing for BFM ID %d with %d signals.\n", id, num_signals);
    printf("[SW Bridge] This test uses BLOCKING mode.\n");

    // --- Configuration ---
    // 1. Set the mode to blocking for this test.
    printf("[SW Bridge] Setting mode to blocking (1).\n");
    s2h_set_mode(1);

    // 2. Set a moderate delay
    int delay = 3;
    printf("[SW Bridge] Setting unified delay to %d cycles.\n", delay);
    s2h_set_delay(delay);
}

void h2s_sideband_notify_vector_nb(int id, unsigned int vector_val) {
    // This should not be called in blocking mode
    printf("[SW Bridge] WARNING: Non-blocking notification received in blocking test!\n");
    logger.log_vector_event(id, vector_val);
}

void h2s_sideband_notify_vector_b(int id, unsigned int vector_val) {
    // This is the primary function for the blocking test.
    logger.log_vector_event(id, vector_val);
}

// Add a cleanup function to print summary when simulation ends
__attribute__((destructor))
void cleanup() {
    logger.print_summary();
}
