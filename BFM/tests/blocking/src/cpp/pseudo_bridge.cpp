#include "sideband_out_dpi.h"
#include <cstdio>
#include <vector>
#include <chrono>
#include <thread>

// Logger class for blocking mode testing
class SidebandBlockingLogger {
public:
    struct Event {
        int signal_idx;
        int signal_val;
        std::chrono::steady_clock::time_point timestamp;
    };

    void log_event(int id, int idx, int val) {
        auto now = std::chrono::steady_clock::now();
        printf("[SW Bridge] Blocking event from BFM ID %d: Signal %d -> %d\n", id, idx, val);
        
        events.push_back({idx, val, now});
        
        // Simulate some processing time to demonstrate blocking behavior
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        printf("[SW Bridge] Finished processing event for Signal %d\n", idx);
    }

    void print_summary() {
        printf("\n[SW Bridge] === Blocking Test Summary ===\n");
        printf("Total events received: %zu\n", events.size());
        
        if (events.size() > 1) {
            printf("\nEvent timing analysis:\n");
            for (size_t i = 1; i < events.size(); i++) {
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    events[i].timestamp - events[i-1].timestamp);
                printf("  Gap between event %zu and %zu: %ld ms\n", 
                       i-1, i, duration.count());
            }
        }
        printf("===============================\n\n");
    }

private:
    std::vector<Event> events;
};

// Global instance of our logger
static SidebandBlockingLogger logger;


//----------------------------------------------------------------
// DPI-C Import Function Implementations
//----------------------------------------------------------------

void h2s_sideband_init(int id, int num_signals) {
    printf("[SW Bridge] Initializing for BFM ID %d with %d signals.\n", id, num_signals);
    printf("[SW Bridge] This test uses BLOCKING mode.\n");

    // --- Configuration ---
    // 1. Set the mode to blocking for this test.
    printf("[SW Bridge] Setting mode to blocking (1).\n");
    s2h_set_mode(1);

    // 2. Set a moderate delay
    int delay = 3;
    printf("[SW Bridge] Setting unified delay to %d cycles.\n", delay);
    s2h_set_delay(delay);
}

void h2s_sideband_notify_change_nb(int id, int signal_idx, unsigned char signal_val) {
    // This should not be called in blocking mode
    printf("[SW Bridge] WARNING: Non-blocking notification received in blocking test!\n");
    logger.log_event(id, signal_idx, signal_val);
}

void h2s_sideband_notify_change_b(int id, int signal_idx, unsigned char signal_val) {
    // This is the primary function for the blocking test.
    logger.log_event(id, signal_idx, signal_val);
}

// Add a cleanup function to print summary when simulation ends
__attribute__((destructor))
void cleanup() {
    logger.print_summary();
}
