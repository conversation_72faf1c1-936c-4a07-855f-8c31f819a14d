#ifndef SIDEBAND_OUT_DPI_H
#define SIDEBAND_OUT_DPI_H

#include "svdpi.h"

#ifdef __cplusplus
extern "C" {
#endif

//----------------------------------------------------------------
// Import functions (Hardware to Software)
// These are implemented in the C++ bridge and called by the BFM.
//----------------------------------------------------------------

/**
 * @brief Initializes the software bridge with static info from the BFM.
 *
 * @param id The unique instance ID of the BFM.
 * @param num_signals The number of sideband signals this BFM instance handles.
 */
void h2s_sideband_init(
    int id,
    int num_signals
);

/**
 * @brief Notifies the software of a signal change (non-blocking).
 *
 * @param id The unique instance ID of the BFM.
 * @param signal_idx The index of the signal that changed.
 * @param signal_val The new value of the signal (0 or 1).
 */
void h2s_sideband_notify_change_nb(
    int id,
    int signal_idx,
    unsigned char signal_val
);

/**
 * @brief Notifies the software of a signal change (blocking).
 *
 * @param id The unique instance ID of the BFM.
 * @param signal_idx The index of the signal that changed.
 * @param signal_val The new value of the signal (0 or 1).
 */
void h2s_sideband_notify_change_b(
    int id,
    int signal_idx,
    unsigned char signal_val
);


//----------------------------------------------------------------
// Export functions (Software to Hardware)
// These are implemented in the BFM and called by the C++ bridge.
//----------------------------------------------------------------

/**
 * @brief Sets the operational mode of the BFM.
 *
 * @param mode 0 for non-blocking, 1 for blocking.
 */
extern void s2h_set_mode(int mode);

/**
 * @brief Sets the unified delay in clock cycles for all signal channels.
 *
 * @param delay_val The number of clock cycles to delay for all signals.
 */
extern void s2h_set_delay(int delay_val);


#ifdef __cplusplus
} // extern "C"
#endif

#endif // SIDEBAND_OUT_DPI_H
