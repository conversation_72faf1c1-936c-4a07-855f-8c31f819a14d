`timescale 1ns/1ps

module tb_sideband_out;

//----------------------------------------------------------------
// Parameters
//----------------------------------------------------------------
localparam CLK_PERIOD    = 10; // 10ns clock period
localparam NUM_SIGNALS   = 8;  // Test with a smaller number of signals
localparam DELAY_WIDTH   = 8;
localparam QUEUE_DEPTH   = 16; // FIFO depth for testing
localparam INSTANCE_ID   = 0;

//----------------------------------------------------------------
// Signals
//----------------------------------------------------------------
bit clk;
bit rst_n;

bit [NUM_SIGNALS-1:0] sideband_in;

//----------------------------------------------------------------
// Clock and Reset Generation
//----------------------------------------------------------------
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

initial begin
    rst_n = 1'b0;
    # (CLK_PERIOD * 5);
    rst_n = 1'b1;
end

//----------------------------------------------------------------
// DUT Instantiation
//----------------------------------------------------------------
sideband_out_bfm #(
    .INSTANCE_ID(INSTANCE_ID),
    .NUM_SIGNALS(NUM_SIGNALS),
    .DELAY_WIDTH(DELAY_WIDTH),
    .QUEUE_DEPTH(QUEUE_DEPTH)
) u_bfm (
    .clk(clk),
    .rst_n(rst_n),
    .sideband_in(sideband_in)
);

//----------------------------------------------------------------
// Test Sequence
//----------------------------------------------------------------
initial begin
    // Wait for reset to de-assert
    @(posedge rst_n);
    @(posedge clk);

    $display("Test Started: Blocking mode with unified delay");

    // --- Test Case 1: Single signal edge ---
    $display("@%0t TC1: Triggering signal 1 (0->1)", $time);
    sideband_in[1] <= 1'b0;
    @(posedge clk);
    sideband_in[1] <= 1'b1; // Rising edge
    @(posedge clk);

    // Wait for the delayed notification
    #(CLK_PERIOD * 10);

    // --- Test Case 2: Sequential edges ---
    $display("@%0t TC2: Sequential edges on different signals", $time);
    sideband_in[2] <= 1'b0;
    @(posedge clk);
    sideband_in[2] <= 1'b1; // Rising edge
    @(posedge clk);
    @(posedge clk);
    sideband_in[3] <= 1'b0;
    @(posedge clk);
    sideband_in[3] <= 1'b1; // Rising edge
    @(posedge clk);

    #(CLK_PERIOD * 15);

    // --- Test Case 3: Verify blocking behavior ---
    $display("@%0t TC3: Testing blocking DPI-C behavior", $time);
    sideband_in[4] <= 1'b0;
    @(posedge clk);
    sideband_in[4] <= 1'b1;
    @(posedge clk);

    #(CLK_PERIOD * 20);

    $display("Test Finished");
    $finish;
end

`ifdef DUMP_FSDB
initial begin
    $fsdbDumpvars(0, tb_sideband_out);
    $fsdbDumpon;
    #10000;
    $fsdbDumpoff;
end
`endif

endmodule
