# Makefile for sideband_out BFM simulation
# Supports VCS and Xcelium, with separate test suites.

# --- Test Suite Selection ---
# Use 'make TEST_SUITE=blocking' or 'make TEST_SUITE=nonblocking'. Default is nonblocking.
TEST_SUITE ?= nonblocking
DUMP_FSDB ?= 1

# General Settings
CXX = g++
INCLUDES_PATH = $(TEST_SUITE)/src/cpp
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -I$(INCLUDES_PATH)

# Source Files
RTL_SOURCES = ../src/sideband_out_bfm.sv ../src/fifo.sv
TB_SOURCES = $(TEST_SUITE)/src/sv/tb_sideband_out.sv
ALL_SV_SOURCES = $(RTL_SOURCES) $(TB_SOURCES)

DPI_CPP_SOURCES = $(TEST_SUITE)/src/cpp/pseudo_bridge.cpp
SVLIB_NAME = $(TEST_SUITE)/pseudo_bridge_$(TEST_SUITE).so

# Output Directories
SIM_DIR = $(TEST_SUITE)/sim
VCS_DIR = $(SIM_DIR)/vcs
XLM_DIR = $(SIM_DIR)/xlm

# VCS/Xcelium Settings
VCS_HOME ?= $(shell which vcs 2>/dev/null | sed 's|/bin/vcs||')
XRUN_HOME ?= $(shell which xrun 2>/dev/null | sed 's|/bin/xrun||')

VCS_INCLUDE = $(VCS_HOME)/include
SHARED_FLAGS = -fPIC -shared -g -lpthread

# Debug options
VCD = 0
ifeq ($(VCD), 1)
	DUMP_VCD = +define+DUMP_VCD
else
	DUMP_VCD =
endif

ifeq ($(DUMP_FSDB), 1)
	DUMP_FSDB = +define+DUMP_FSDB
else
	DUMP_FSDB =
endif

DEBUG_HW = 1
ifeq ($(DEBUG_HW), 1)
	DEBUG_HW_FLAG = +define+DEBUG_BFM
else
	DEBUG_HW_FLAG =
endif

HW_DEFINE_OPTS = $(DUMP_VCD) $(DEBUG_HW_FLAG) $(DUMP_FSDB)

# Targets
.PHONY: all clean vcs xlm vcs_comp vcs_run vcs_grun xlm_comp xlm_run xlm_grun cleanall create_dirs help

all: help

# Create simulation directories
create_dirs:
	@echo "Creating simulation directories..."
	@mkdir -p $(VCS_DIR) $(XLM_DIR)

# DPI library compilation
$(SVLIB_NAME): $(DPI_CPP_SOURCES) create_dirs
	@echo "Building DPI shared library for $(TEST_SUITE): $(SVLIB_NAME)..."
	$(CXX) $(CXXFLAGS) $(SHARED_FLAGS) -I$(VCS_INCLUDE) $(DPI_CPP_SOURCES) -o $(SVLIB_NAME)

# VCS targets
vcs: vcs_run

vcs_comp: create_dirs $(SVLIB_NAME)
	@echo "Compiling for VCS ($(TEST_SUITE))..."
	cd $(VCS_DIR) && \
	vcs -full64 -sverilog -debug_access+all -timescale=1ns/1ps \
		-kdb -lca +vpi \
		$(HW_DEFINE_OPTS) \
		+incdir+../../../../src \
		+incdir+../../../$(TEST_SUITE)/src/sv \
		../../../../src/sideband_out_bfm.sv \
		../../../../src/fifo.sv \
		../../../$(TB_SOURCES) \
		-top tb_sideband_out \
		-o simv \
		-l vcs_comp.log \
		-CFLAGS "-g -I../../../$(INCLUDES_PATH)"

vcs_run: vcs_comp
	@echo "Running VCS simulation ($(TEST_SUITE))..."
	cd $(VCS_DIR) && \
	./simv -sv_lib ../../../$(basename $(SVLIB_NAME)) \
		-l vcs.log

vcs_grun: vcs_comp
	@echo "Running VCS simulation with GUI ($(TEST_SUITE))..."
	cd $(VCS_DIR) && \
	./simv -gui -sv_lib ../../../$(basename $(SVLIB_NAME)) \
		-l vcs_gui.log

# Xcelium targets
xlm: xlm_run

xlm_comp: create_dirs $(SVLIB_NAME)
	@echo "Compiling for Xcelium ($(TEST_SUITE))..."
	cd $(XLM_DIR) && \
	xrun -c -sv -access +rwc -linedebug \
		$(HW_DEFINE_OPTS) \
		+incdir+../../../../src \
		+incdir+../../../$(TEST_SUITE)/src/sv \
		../../../../src/sideband_out_bfm.sv \
		../../../$(TB_SOURCES) \
		-top tb_sideband_out \
		-status -log xrun_compile.log

xlm_run: xlm_comp
	@echo "Running Xcelium simulation ($(TEST_SUITE))..."
	cd $(XLM_DIR) && \
	xrun -R -sv_lib ../../../$(SVLIB_NAME) \
		-status -l xrun_sim.log

xlm_grun: xlm_comp
	@echo "Running Xcelium simulation with GUI ($(TEST_SUITE))..."
	cd $(XLM_DIR) && \
	xrun -R -gui -sv_lib ../../../$(SVLIB_NAME) \
		-status -l xrun_gui.log

# Clean targets
clean:
	@echo "Cleaning build artifacts for $(TEST_SUITE)..."
	rm -rf $(SVLIB_NAME)
	rm -rf $(SIM_DIR)

cleanall:
	@echo "Cleaning all generated files..."
	rm -rf blocking nonblocking

# Help target
help:
	@echo "Sideband Out BFM Makefile"
	@echo ""
	@echo "Usage: make [target] [TEST_SUITE=nonblocking|blocking]"
	@echo "Default TEST_SUITE is 'nonblocking'."
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Show this help message (default)"
	@echo "  $(SVLIB_NAME)    - Build the DPI-C shared library"
	@echo ""
	@echo "VCS Targets:"
	@echo "  vcs          - Compile and run simulation with VCS"
	@echo "  vcs_comp     - Compile design with VCS"
	@echo "  vcs_run      - Run simulation with VCS"
	@echo "  vcs_grun     - Run simulation with VCS and GUI"
	@echo ""
	@echo "Xcelium Targets:"
	@echo "  xlm          - Compile and run simulation with Xcelium"
	@echo "  xlm_comp     - Compile design with Xcelium"
	@echo "  xlm_run      - Run simulation with Xcelium"
	@echo "  xlm_grun     - Run simulation with Xcelium and GUI"
	@echo ""
	@echo "Management Targets:"
	@echo "  create_dirs  - Create simulation directories"
	@echo "  clean        - Remove build artifacts for the current TEST_SUITE"
	@echo "  cleanall     - Remove all generated files for all test suites"
	@echo "  help         - Show this help message"
