`timescale 1ns/1ps

module tb_sideband_out;

//----------------------------------------------------------------
// Parameters
//----------------------------------------------------------------
localparam CLK_PERIOD    = 10; // 10ns clock period
localparam NUM_SIGNALS   = 8;  // Test with a smaller number of signals
localparam DELAY_WIDTH   = 8;
localparam QUEUE_DEPTH   = 16; // FIFO depth for testing
localparam INSTANCE_ID   = 0;

//----------------------------------------------------------------
// Signals
//----------------------------------------------------------------
bit clk;
bit rst_n;

bit [NUM_SIGNALS-1:0] sideband_in;

//----------------------------------------------------------------
// Clock and Reset Generation
//----------------------------------------------------------------
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

initial begin
    rst_n = 1'b0;
    # (CLK_PERIOD * 5);
    rst_n = 1'b1;
end

//----------------------------------------------------------------
// DUT Instantiation
//----------------------------------------------------------------
sideband_out_bfm #(
    .INSTANCE_ID(INSTANCE_ID),
    .NUM_SIGNALS(NUM_SIGNALS),
    .DELAY_WIDTH(DELAY_WIDTH),
    .QUEUE_DEPTH(QUEUE_DEPTH)
) u_bfm (
    .clk(clk),
    .rst_n(rst_n),
    .sideband_in(sideband_in)
);

//----------------------------------------------------------------
// Test Sequence
//----------------------------------------------------------------
initial begin
    // Wait for reset to de-assert
    @(posedge rst_n);
    @(posedge clk);

    $display("Test Started: Non-blocking mode with vector-based notifications");

    // --- Test Case 1: Single bit change ---
    // The C++ bridge will have configured a unified delay of 5 cycles.
    $display("@%0t TC1: Changing bit 1 (0->1)", $time);
    sideband_in <= 8'h00;
    @(posedge clk);
    sideband_in <= 8'h02; // Set bit 1
    @(posedge clk);

    // Wait for the delayed notification
    #(CLK_PERIOD * 10);

    // --- Test Case 2: Multiple sequential changes ---
    $display("@%0t TC2: Sequential bit changes", $time);
    sideband_in <= 8'h02; // Start with bit 1 set
    @(posedge clk);
    sideband_in <= 8'h06; // Set bit 2 as well (bits 1,2 = 1)
    @(posedge clk);
    sideband_in <= 8'h04; // Clear bit 1, keep bit 2 (bit 2 = 1)
    @(posedge clk);
    sideband_in <= 8'h00; // Clear all bits
    @(posedge clk);

    #(CLK_PERIOD * 15);

    // --- Test Case 3: Multiple bits changing simultaneously ---
    $display("@%0t TC3: Multiple bits changing simultaneously", $time);
    sideband_in <= 8'h00;
    @(posedge clk);
    sideband_in <= 8'h0F; // Set bits 0,1,2,3 simultaneously
    @(posedge clk);
    sideband_in <= 8'hF0; // Change to bits 4,5,6,7
    @(posedge clk);

    #(CLK_PERIOD * 10);

    $display("Test Finished");
    $finish;
end

`ifdef DUMP_FSDB
initial begin
    $fsdbDumpvars(0, tb_sideband_out);
    $fsdbDumpon;
    #10000;
    $fsdbDumpoff;
end
`endif

endmodule