`timescale 1ns/1ps

module tb_sideband_out;

//----------------------------------------------------------------
// Parameters
//----------------------------------------------------------------
localparam CLK_PERIOD    = 10; // 10ns clock period
localparam NUM_SIGNALS   = 8;  // Test with a smaller number of signals
localparam DELAY_WIDTH   = 8;
localparam INSTANCE_ID   = 0;

//----------------------------------------------------------------
// Signals
//----------------------------------------------------------------
bit clk;
bit rst_n;

bit [NUM_SIGNALS-1:0] sideband_in;
bit [NUM_SIGNALS-1:0] sideband_valid;

//----------------------------------------------------------------
// Clock and Reset Generation
//----------------------------------------------------------------
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

initial begin
    rst_n = 1'b0;
    # (CLK_PERIOD * 5);
    rst_n = 1'b1;
end

//----------------------------------------------------------------
// DUT Instantiation
//----------------------------------------------------------------
sideband_out_bfm #(
    .INSTANCE_ID(INSTANCE_ID),
    .NUM_SIGNALS(NUM_SIGNALS),
    .DELAY_WIDTH(DELAY_WIDTH)
) u_bfm (
    .clk(clk),
    .rst_n(rst_n),
    .sideband_in(sideband_in),
    .sideband_valid(sideband_valid)
);

//----------------------------------------------------------------
// Test Sequence
//----------------------------------------------------------------
initial begin
    // Wait for reset to de-assert
    @(posedge rst_n);
    @(posedge clk);

    $display("Test Started: Non-blocking mode");

    // --- Test Case 1: Trigger signal 1, then valid ---
    // The C++ bridge will have configured a delay of 5 for this signal.
    $display("@%0t TC1: Triggering signal 1", $time);
    sideband_in[1] <= 1'b0;
    @(posedge clk);
    sideband_in[1] <= 1'b1; // Edge created
    @(posedge clk);
    $display("@%0t TC1: Asserting valid for signal 1", $time);
    sideband_valid[1] <= 1'b0;
    @(posedge clk);
    sideband_valid[1] <= 1'b1; // Edge created
    @(posedge clk);
    @(posedge clk);
    sideband_valid[1] <= 1'b0; // Edge created

    // Wait for some time to observe the delayed notification
    #(CLK_PERIOD * 10);

    // --- Test Case 2: Trigger signal 2 (no delay) ---
    $display("@%0t TC2: Triggering signal 2", $time);
    sideband_in[2] <= 1'b1;
    @(posedge clk);
    @(posedge clk);
    sideband_in[2] <= 1'b0;
    @(posedge clk);
    $display("@%0t TC2: Asserting valid for signal 2", $time);
    sideband_valid[2] <= 1'b0;
    @(posedge clk);
    @(posedge clk);
    sideband_valid[2] <= 1'b1;
    @(posedge clk);
    @(posedge clk);
    sideband_valid[2] <= 1'b0;

    #(CLK_PERIOD * 10);

    // --- Test Case 3: Trigger signal and valid simultaneously ---
    $display("@%0t TC3: Triggering signal 3 and valid simultaneously", $time);
    sideband_in[3] <= 1'b1;
    sideband_valid[3] <= 1'b0;
    @(posedge clk);
    @(posedge clk);
    sideband_in[3] <= 1'b0;
    sideband_valid[3] <= 1'b1;
    @(posedge clk);
    @(posedge clk);
    sideband_valid[3] <= 1'b0;

    #(CLK_PERIOD * 20);

    $display("Test Finished");
    $finish;
end

`ifdef DUMP_FSDB
initial begin
    $fsdbDumpvars(0, tb_sideband_out);
    $fsdbDumpon;
    #10000;
    $fsdbDumpoff;
end
`endif

endmodule