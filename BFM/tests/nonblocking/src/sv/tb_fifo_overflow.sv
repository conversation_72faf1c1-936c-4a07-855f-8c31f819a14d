`timescale 1ns/1ps

module tb_fifo_overflow;

//----------------------------------------------------------------
// Parameters
//----------------------------------------------------------------
localparam CLK_PERIOD    = 10; // 10ns clock period
localparam NUM_SIGNALS   = 8;  // Test with a smaller number of signals
localparam DELAY_WIDTH   = 8;
localparam QUEUE_DEPTH   = 4;  // Small FIFO to easily test overflow
localparam INSTANCE_ID   = 0;

//----------------------------------------------------------------
// Signals
//----------------------------------------------------------------
bit clk;
bit rst_n;

bit [NUM_SIGNALS-1:0] sideband_in;

//----------------------------------------------------------------
// Clock and Reset Generation
//----------------------------------------------------------------
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

initial begin
    rst_n = 1'b0;
    # (CLK_PERIOD * 5);
    rst_n = 1'b1;
end

//----------------------------------------------------------------
// DUT Instantiation
//----------------------------------------------------------------
sideband_out_bfm #(
    .INSTANCE_ID(INSTANCE_ID),
    .NUM_SIGNALS(NUM_SIGNALS),
    .DELAY_WIDTH(DELAY_WIDTH),
    .QUEUE_DEPTH(QUEUE_DEPTH)
) u_bfm (
    .clk(clk),
    .rst_n(rst_n),
    .sideband_in(sideband_in)
);

//----------------------------------------------------------------
// Test Sequence
//----------------------------------------------------------------
initial begin
    // Wait for reset to de-assert
    @(posedge rst_n);
    @(posedge clk);

    $display("Test Started: Vector FIFO Overflow Test");
    $display("FIFO Depth: %0d", QUEUE_DEPTH);

    // --- Test Case 1: Fill FIFO beyond capacity with vector changes ---
    $display("@%0t TC1: Generating rapid vector changes to overflow FIFO", $time);

    // Generate more vector changes than FIFO can hold
    // With QUEUE_DEPTH=4 and a large delay, we should see overflow behavior
    for (int i = 0; i < QUEUE_DEPTH + 3; i++) begin
        $display("@%0t Generating vector change %0d: 0x%02X", $time, i, i & 8'hFF);
        sideband_in <= i & 8'hFF; // Different vector pattern each cycle
        @(posedge clk);
    end

    $display("@%0t Generated %0d vector changes, FIFO should overflow", $time, QUEUE_DEPTH + 3);

    // Wait for events to be processed
    #(CLK_PERIOD * 50);

    // --- Test Case 2: Rapid vector pattern changes ---
    $display("@%0t TC2: Rapid vector pattern changes", $time);

    for (int cycle = 0; cycle < 10; cycle++) begin
        // Generate different vector patterns each cycle
        case (cycle % 4)
            0: sideband_in <= 8'h0F; // Lower nibble
            1: sideband_in <= 8'hF0; // Upper nibble
            2: sideband_in <= 8'hAA; // Alternating pattern
            3: sideband_in <= 8'h55; // Inverse alternating
        endcase
        @(posedge clk);
    end

    // Wait for all events to be processed
    #(CLK_PERIOD * 100);

    $display("Test Finished");
    $finish;
end

//----------------------------------------------------------------
// Monitor for debugging
//----------------------------------------------------------------
initial begin
    @(posedge rst_n);
    forever begin
        @(posedge clk);
        if (u_bfm.vector_changed) begin
            $display("@%0t Vector change detected: 0x%02X -> 0x%02X",
                     $time, u_bfm.sideband_in_d, sideband_in);
        end
        if (u_bfm.fifo_wr_en) begin
            $display("@%0t FIFO Write: 0x%02X -> 0x%02X, full=%0b",
                     $time, u_bfm.fifo_wr_data.old_vector, u_bfm.fifo_wr_data.new_vector, u_bfm.fifo_full);
        end
        if (u_bfm.fifo_rd_en) begin
            $display("@%0t FIFO Read: 0x%02X -> 0x%02X, empty=%0b",
                     $time, u_bfm.fifo_rd_data.old_vector, u_bfm.fifo_rd_data.new_vector, u_bfm.fifo_empty);
        end
    end
end

`ifdef DUMP_FSDB
initial begin
    $fsdbDumpvars(0, tb_fifo_overflow);
    $fsdbDumpon;
    #10000;
    $fsdbDumpoff;
end
`endif

endmodule
