`timescale 1ns/1ps

module tb_fifo_overflow;

//----------------------------------------------------------------
// Parameters
//----------------------------------------------------------------
localparam CLK_PERIOD    = 10; // 10ns clock period
localparam NUM_SIGNALS   = 8;  // Test with a smaller number of signals
localparam DELAY_WIDTH   = 8;
localparam QUEUE_DEPTH   = 4;  // Small FIFO to easily test overflow
localparam INSTANCE_ID   = 0;

//----------------------------------------------------------------
// Signals
//----------------------------------------------------------------
bit clk;
bit rst_n;

bit [NUM_SIGNALS-1:0] sideband_in;

//----------------------------------------------------------------
// Clock and Reset Generation
//----------------------------------------------------------------
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

initial begin
    rst_n = 1'b0;
    # (CLK_PERIOD * 5);
    rst_n = 1'b1;
end

//----------------------------------------------------------------
// DUT Instantiation
//----------------------------------------------------------------
sideband_out_bfm #(
    .INSTANCE_ID(INSTANCE_ID),
    .NUM_SIGNALS(NUM_SIGNALS),
    .DELAY_WIDTH(DELAY_WIDTH),
    .QUEUE_DEPTH(QUEUE_DEPTH)
) u_bfm (
    .clk(clk),
    .rst_n(rst_n),
    .sideband_in(sideband_in)
);

//----------------------------------------------------------------
// Test Sequence
//----------------------------------------------------------------
initial begin
    // Wait for reset to de-assert
    @(posedge rst_n);
    @(posedge clk);

    $display("Test Started: FIFO Overflow Test");
    $display("FIFO Depth: %0d", QUEUE_DEPTH);

    // --- Test Case 1: Fill FIFO beyond capacity ---
    $display("@%0t TC1: Generating rapid signal changes to overflow FIFO", $time);
    
    // Generate more edges than FIFO can hold
    // With QUEUE_DEPTH=4 and a large delay, we should see overflow behavior
    for (int i = 0; i < QUEUE_DEPTH + 3; i++) begin
        $display("@%0t Generating edge %0d on signal 0", $time, i);
        sideband_in[0] <= ~sideband_in[0]; // Toggle signal
        @(posedge clk);
    end

    $display("@%0t Generated %0d edges, FIFO should overflow", $time, QUEUE_DEPTH + 3);

    // Wait for events to be processed
    #(CLK_PERIOD * 50);

    // --- Test Case 2: Multiple signals with rapid changes ---
    $display("@%0t TC2: Multiple signals with rapid changes", $time);
    
    for (int cycle = 0; cycle < 10; cycle++) begin
        // Change multiple signals in each cycle
        sideband_in[1] <= ~sideband_in[1];
        sideband_in[2] <= ~sideband_in[2];
        sideband_in[3] <= ~sideband_in[3];
        @(posedge clk);
    end

    // Wait for all events to be processed
    #(CLK_PERIOD * 100);

    $display("Test Finished");
    $finish;
end

//----------------------------------------------------------------
// Monitor for debugging
//----------------------------------------------------------------
initial begin
    @(posedge rst_n);
    forever begin
        @(posedge clk);
        if (|u_bfm.sideband_edge) begin
            $display("@%0t Edge detected: sideband_in=%b, edge=%b", 
                     $time, sideband_in, u_bfm.sideband_edge);
        end
        if (u_bfm.fifo_wr_en) begin
            $display("@%0t FIFO Write: idx=%0d, val=%0d, full=%0b", 
                     $time, u_bfm.fifo_wr_data.index, u_bfm.fifo_wr_data.value, u_bfm.fifo_full);
        end
        if (u_bfm.fifo_rd_en) begin
            $display("@%0t FIFO Read: idx=%0d, val=%0d, empty=%0b", 
                     $time, u_bfm.fifo_rd_data.index, u_bfm.fifo_rd_data.value, u_bfm.fifo_empty);
        end
    end
end

`ifdef DUMP_FSDB
initial begin
    $fsdbDumpvars(0, tb_fifo_overflow);
    $fsdbDumpon;
    #10000;
    $fsdbDumpoff;
end
`endif

endmodule
