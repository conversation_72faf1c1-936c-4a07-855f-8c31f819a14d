#include "sideband_out_dpi.h"
#include <cstdio>
#include <vector>

// Simple logger class to store received events
class SidebandLogger {
public:
    struct Event {
        int signal_idx;
        int signal_val;
    };

    void log_event(int id, int idx, int val) {
        printf("[SW Bridge] Received event from BFM ID %d: Signal %d -> %d\n", id, idx, val);
        events.push_back({idx, val});
    }

    void print_summary() {
        printf("[SW Bridge] Total events received: %zu\n", events.size());
    }

private:
    std::vector<Event> events;
};

// Global instance of our logger
static SidebandLogger logger;


//----------------------------------------------------------------
// DPI-C Import Function Implementations
//----------------------------------------------------------------

void h2s_sideband_init(int id, int num_signals) {
    printf("[SW Bridge] Initializing for BFM ID %d with %d signals.\n", id, num_signals);

    // --- Configuration ---
    // As per the test plan, we configure the BFM from the software side.
    // 1. Set the mode to non-blocking for this test.
    printf("[SW Bridge] Setting mode to non-blocking (0).\n");
    s2h_set_mode(0);

    // 2. Set a default delay for all signals as an example.
    //    Let's set a delay of 5 cycles for all odd signals.
    for (int i = 0; i < num_signals; ++i) {
        if (i % 2 != 0) {
            int delay = 5;
            printf("[SW Bridge] Setting delay for signal %d to %d cycles.\n", i, delay);
            s2h_set_delay(i, delay);
        }
    }
}

void h2s_sideband_notify_change_nb(int id, int signal_idx, unsigned char signal_val) {
    // This is the primary function for the non-blocking test.
    logger.log_event(id, signal_idx, signal_val);
}

void h2s_sideband_notify_change_b(int id, int signal_idx, unsigned char signal_val) {
    // In the non-blocking test, this function should ideally not be called.
    // We'll add a warning message if it is.
    printf("[SW Bridge] WARNING: Blocking notification received in non-blocking test!\n");
    logger.log_event(id, signal_idx, signal_val);
}
