#include "sideband_out_dpi.h"
#include <cstdio>
#include <vector>

// Enhanced logger class to store received vector transition events
class SidebandVectorLogger {
public:
    struct VectorTransition {
        unsigned int old_vector;
        unsigned int new_vector;
        std::vector<int> changed_bits;
    };

    void log_vector_transition(int id, unsigned int old_vector, unsigned int new_vector) {
        printf("[SW Bridge] Received vector transition from BFM ID %d: 0x%08X -> 0x%08X\n",
               id, old_vector, new_vector);

        // Detect which bits changed using XOR
        unsigned int changed_mask = old_vector ^ new_vector;
        std::vector<int> changed_bits;

        if (changed_mask == 0) {
            printf("[SW Bridge]   No bits changed (should not happen)\n");
        } else {
            // Find all changed bit positions
            for (int i = 0; i < 32; i++) {
                if (changed_mask & (1U << i)) {
                    changed_bits.push_back(i);
                    int old_val = (old_vector >> i) & 1;
                    int new_val = (new_vector >> i) & 1;
                    printf("[SW Bridge]   Bit %d: %d -> %d\n", i, old_val, new_val);
                }
            }
        }

        events.push_back({old_vector, new_vector, changed_bits});
    }

    void print_summary() {
        printf("\n[SW Bridge] === Vector Transition Summary ===\n");
        printf("Total vector transitions received: %zu\n", events.size());

        int total_bit_changes = 0;
        for (const auto& event : events) {
            total_bit_changes += event.changed_bits.size();
        }
        printf("Total bit changes detected: %d\n", total_bit_changes);
        printf("=====================================\n\n");
    }

private:
    std::vector<VectorTransition> events;
};

// Global instance of our logger
static SidebandVectorLogger logger;


//----------------------------------------------------------------
// DPI-C Import Function Implementations
//----------------------------------------------------------------

void h2s_sideband_init(int id, int num_signals) {
    printf("[SW Bridge] Initializing for BFM ID %d with %d signals.\n", id, num_signals);

    // --- Configuration ---
    // As per the test plan, we configure the BFM from the software side.
    // 1. Set the mode to non-blocking for this test.
    printf("[SW Bridge] Setting mode to non-blocking (0).\n");
    s2h_set_mode(0);

    // 2. Set a unified delay for all signals.
    int delay = 5;
    printf("[SW Bridge] Setting unified delay to %d cycles for all signals.\n", delay);
    s2h_set_delay(delay);
}

void h2s_sideband_notify_vector_nb(int id, unsigned int old_vector, unsigned int new_vector) {
    // This is the primary function for the non-blocking test.
    logger.log_vector_transition(id, old_vector, new_vector);
}

void h2s_sideband_notify_vector_b(int id, unsigned int old_vector, unsigned int new_vector) {
    // In the non-blocking test, this function should ideally not be called.
    // We'll add a warning message if it is.
    printf("[SW Bridge] WARNING: Blocking notification received in non-blocking test!\n");
    logger.log_vector_transition(id, old_vector, new_vector);
}
