#include "sideband_out_dpi.h"
#include <cstdio>
#include <vector>
#include <map>

// Enhanced logger class to track overflow behavior
class SidebandOverflowLogger {
public:
    struct Event {
        int signal_idx;
        int signal_val;
        int timestamp;
    };

    void log_event(int id, int idx, int val) {
        static int event_count = 0;
        event_count++;
        
        printf("[SW Bridge] Event %d from BFM ID %d: Signal %d -> %d\n", 
               event_count, id, idx, val);
        
        events.push_back({idx, val, event_count});
        
        // Track per-signal statistics
        signal_stats[idx].total_events++;
        signal_stats[idx].last_value = val;
    }

    void print_summary() {
        printf("\n[SW Bridge] === Test Summary ===\n");
        printf("Total events received: %zu\n", events.size());
        
        printf("\nPer-signal statistics:\n");
        for (auto& pair : signal_stats) {
            int sig_idx = pair.first;
            auto& stats = pair.second;
            printf("  Signal %d: %d events, last value: %d\n", 
                   sig_idx, stats.total_events, stats.last_value);
        }
        
        printf("\nEvent sequence:\n");
        for (size_t i = 0; i < events.size() && i < 20; i++) {
            printf("  %d: Signal %d -> %d\n", 
                   events[i].timestamp, events[i].signal_idx, events[i].signal_val);
        }
        if (events.size() > 20) {
            printf("  ... (showing first 20 events)\n");
        }
        printf("=========================\n\n");
    }

private:
    std::vector<Event> events;
    
    struct SignalStats {
        int total_events = 0;
        int last_value = -1;
    };
    std::map<int, SignalStats> signal_stats;
};

// Global instance of our logger
static SidebandOverflowLogger logger;


//----------------------------------------------------------------
// DPI-C Import Function Implementations
//----------------------------------------------------------------

void h2s_sideband_init(int id, int num_signals) {
    printf("[SW Bridge] Initializing for BFM ID %d with %d signals.\n", id, num_signals);
    printf("[SW Bridge] This test focuses on FIFO overflow behavior.\n");

    // --- Configuration ---
    // 1. Set the mode to non-blocking for this test.
    printf("[SW Bridge] Setting mode to non-blocking (0).\n");
    s2h_set_mode(0);

    // 2. Set a large delay to create backpressure and test FIFO overflow
    int delay = 20; // Large delay to cause FIFO to fill up
    printf("[SW Bridge] Setting unified delay to %d cycles to test overflow.\n", delay);
    s2h_set_delay(delay);
}

void h2s_sideband_notify_change_nb(int id, int signal_idx, unsigned char signal_val) {
    // This is the primary function for the non-blocking test.
    logger.log_event(id, signal_idx, signal_val);
}

void h2s_sideband_notify_change_b(int id, int signal_idx, unsigned char signal_val) {
    // In the non-blocking test, this function should ideally not be called.
    printf("[SW Bridge] WARNING: Blocking notification received in non-blocking test!\n");
    logger.log_event(id, signal_idx, signal_val);
}

// Add a cleanup function to print summary when simulation ends
__attribute__((destructor))
void cleanup() {
    logger.print_summary();
}
