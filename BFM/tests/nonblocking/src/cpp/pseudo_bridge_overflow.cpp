#include "sideband_out_dpi.h"
#include <cstdio>
#include <vector>
#include <map>

// Enhanced logger class to track vector transition overflow behavior
class SidebandVectorOverflowLogger {
public:
    struct VectorTransition {
        unsigned int old_vector;
        unsigned int new_vector;
        int timestamp;
        std::vector<int> changed_bits;
    };

    void log_vector_transition(int id, unsigned int old_vector, unsigned int new_vector) {
        static int event_count = 0;
        event_count++;

        printf("[SW Bridge] Vector Transition %d from BFM ID %d: 0x%08X -> 0x%08X\n",
               event_count, id, old_vector, new_vector);

        // Detect which bits changed using XOR
        unsigned int changed_mask = old_vector ^ new_vector;
        std::vector<int> changed_bits;

        // Find all changed bit positions
        for (int i = 0; i < 32; i++) {
            if (changed_mask & (1U << i)) {
                changed_bits.push_back(i);
                int old_val = (old_vector >> i) & 1;
                int new_val = (new_vector >> i) & 1;
                printf("[SW Bridge]   Bit %d: %d -> %d\n", i, old_val, new_val);

                // Track per-bit statistics
                bit_stats[i].total_changes++;
                bit_stats[i].last_value = new_val;
            }
        }

        events.push_back({old_vector, new_vector, event_count, changed_bits});
    }

    void print_summary() {
        printf("\n[SW Bridge] === Vector Overflow Test Summary ===\n");
        printf("Total vector events received: %zu\n", events.size());

        int total_bit_changes = 0;
        for (const auto& event : events) {
            total_bit_changes += event.changed_bits.size();
        }
        printf("Total bit changes detected: %d\n", total_bit_changes);

        printf("\nPer-bit statistics:\n");
        for (auto& pair : bit_stats) {
            int bit_idx = pair.first;
            auto& stats = pair.second;
            printf("  Bit %d: %d changes, last value: %d\n",
                   bit_idx, stats.total_changes, stats.last_value);
        }

        printf("\nVector transition sequence (first 10):\n");
        for (size_t i = 0; i < events.size() && i < 10; i++) {
            printf("  %d: 0x%08X -> 0x%08X (%zu bits changed)\n",
                   events[i].timestamp, events[i].old_vector, events[i].new_vector, events[i].changed_bits.size());
        }
        if (events.size() > 10) {
            printf("  ... (showing first 10 events)\n");
        }
        printf("=======================================\n\n");
    }

private:
    std::vector<VectorTransition> events;

    struct BitStats {
        int total_changes = 0;
        int last_value = -1;
    };
    std::map<int, BitStats> bit_stats;
};

// Global instance of our logger
static SidebandVectorOverflowLogger logger;


//----------------------------------------------------------------
// DPI-C Import Function Implementations
//----------------------------------------------------------------

void h2s_sideband_init(int id, int num_signals) {
    printf("[SW Bridge] Initializing for BFM ID %d with %d signals.\n", id, num_signals);
    printf("[SW Bridge] This test focuses on FIFO overflow behavior.\n");

    // --- Configuration ---
    // 1. Set the mode to non-blocking for this test.
    printf("[SW Bridge] Setting mode to non-blocking (0).\n");
    s2h_set_mode(0);

    // 2. Set a large delay to create backpressure and test FIFO overflow
    int delay = 20; // Large delay to cause FIFO to fill up
    printf("[SW Bridge] Setting unified delay to %d cycles to test overflow.\n", delay);
    s2h_set_delay(delay);
}

void h2s_sideband_notify_vector_nb(int id, unsigned int old_vector, unsigned int new_vector) {
    // This is the primary function for the non-blocking test.
    logger.log_vector_transition(id, old_vector, new_vector);
}

void h2s_sideband_notify_vector_b(int id, unsigned int old_vector, unsigned int new_vector) {
    // In the non-blocking test, this function should ideally not be called.
    printf("[SW Bridge] WARNING: Blocking notification received in non-blocking test!\n");
    logger.log_vector_transition(id, old_vector, new_vector);
}

// Add a cleanup function to print summary when simulation ends
__attribute__((destructor))
void cleanup() {
    logger.print_summary();
}
