Command: vcs -full64 -sverilog -debug_access+all -timescale=1ns/1ps -kdb -lca +vpi \
+define+DEBUG_BFM +define+DUMP_FSDB +incdir+../../../../src +incdir+../../../nonblocking/src/sv \
../../../../src/sideband_out_bfm.sv ../../../../src/fifo.sv ../../../nonblocking/src/sv/tb_sideband_out.sv \
-top tb_sideband_out -o simv -l vcs_comp.log -CFLAGS -g -I../../../nonblocking/src/cpp \

                         Chronologic VCS (TM)
       Version V-2023.12-SP2_Full64 -- Sun Jul 20 23:14:39 2025

                    Copyright (c) 1991 - 2024 Synopsys, Inc.
   This software and the associated documentation are proprietary to Synopsys,
 Inc. This software may only be used in accordance with the terms and conditions
 of a written license agreement with Synopsys, Inc. All other use, reproduction,
   or distribution of this software is strictly prohibited.  Licensed Products
     communicate with Synopsys servers for the purpose of providing software
    updates, detecting software piracy and verifying that customers are using
    Licensed Products in conformity with the applicable License Key for such
  Licensed Products. Synopsys will use information gathered in connection with
    this process to deliver software updates and pursue software pirates and
                                   infringers.

 Inclusivity & Diversity - Visit SolvNetPlus to read the "Synopsys Statement on
            Inclusivity and Diversity" (Refer to article 000036315 at
                        https://solvnetplus.synopsys.com)


Warning-[LCA_FEATURES_ENABLED] Usage warning
  LCA features enabled by '-lca' argument on the command line.  For more 
  information regarding list of LCA features please refer to Chapter "LCA 
  features" in the VCS Release Notes

Parsing design file '../../../../src/sideband_out_bfm.sv'
Parsing design file '../../../../src/fifo.sv'
Parsing design file '../../../nonblocking/src/sv/tb_sideband_out.sv'
Top Level Modules:
       tb_sideband_out
TimeScale is 1 ns / 1 ps
Starting vcs inline pass...

3 modules and 0 UDP read.
recompiling module tb_sideband_out
	However, due to incremental compilation, only 1 module needs to be compiled. 
make[1]: Entering directory '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/csrc' \

rm -f _cuarc*.so _csrc*.so pre_vcsobj_*.so share_vcsobj_*.so
if [ -x ../simv ]; then chmod a-x ../simv; fi
g++  -o ../simv      -rdynamic  -Wl,-rpath='$ORIGIN'/simv.daidir -Wl,-rpath=./simv.daidir \
-Wl,-rpath=/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib -L/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib \
-Wl,-rpath-link=./   objs/amcQw_d.o  _114572_archive_1.so _prev_archive_1.so  SIM_l.o \
rmapats_mop.o rmapats.o rmar.o rmar_nd.o  rmar_llvm_0_1.o rmar_llvm_0_0.o        \
-lnuma -lvirsim -lerrorinf -lsnpsmalloc -lvfs    -lvcsnew -lsimprofile -luclinative \
/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_tls.o   -Wl,-whole-archive  -lvcsucli \
-Wl,-no-whole-archive      ./../simv.daidir/vc_hdrs.o _vcs_pli_stub_.o   /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_save_restore_new.o \
/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/pli.a -ldl  -lc -lm -lpthread \
-ldl 
../simv up to date
make[1]: Leaving directory '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/csrc' \

CPU time: .892 seconds to compile + .584 seconds to elab + .434 seconds to link
Verdi KDB elaboration done and the database successfully generated: 0 error(s), 0 warning(s)
