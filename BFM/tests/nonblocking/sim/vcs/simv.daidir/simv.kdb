rc file Version 1.0

[Design]
COMPILE_PATH=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs
VCS_HOME=/opt/synopsys/vcs/V-2023.12-SP2
VERDI_HOME=/opt/synopsys/verdi/V-2023.12-SP2
SystemC=FALSE
UUM=FALSE
KDB=TRUE
USE_NOVAS_HOME=FALSE
COSIM=FALSE
TOP=tb_sideband_out 
OPTION=-ssv -ssy 
ELAB_OPTION=-ssv -ssy 
ELAB=TRUE
SIMV_WRAPPER=FALSE
[vcs]
RUN_ELABCOM=unset TURBO_LOG_DIR ;\rm -rf /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/elabcomLog ;/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/elabcom -logdir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/elabcomLog  -simvdaidir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir  -lib /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/work  -saveLevel  -elab /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb -simflow -simdir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb_libs -topfile /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomTopFile -ssv -ssy  -Lf work +disable_message+C00380+C00381 +disable_message+C00388+C00389  >& /dev/null  && echo Success >& /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomReport || echo Fail >& /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomReport; \rm -rf /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/work.lib++ /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb_libs/ 

[Value]
WREALX=ffff534e50535f58
WREALZ=ffff534e50535f5a

[CompInfo]
FLOW_OPTS=-kdb -ssv -ssy 
