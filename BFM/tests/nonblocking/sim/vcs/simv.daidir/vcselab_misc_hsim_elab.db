psSimBaseName                      	simv
psLogFileName                      	vcs_comp.log
psDaiDir                           	/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir
destPath                           	csrc/
fSharedMasterElab                  	0
fHsimPCSharedLibSpecified          	0
hsMainFileCount                    	0
hsMainFileName                     	dummy
hsAuxFileName                      	dummy
partitionName                      	6	MASTER
hsimInitRegValue                   	3
fNSParam                           	128
hsim_noschedinl                    	0
hsim_hdbs                          	4096
eval_order_seq                     	0
simorder_light                     	0
partialelab                        	0
hsim_csdf                          	-2147483648
fHsimRuntimeElabSdf                	0
fNtcNewSolver                      	0
fHsimSdfFileOpt                    	0
fHsimTransUsingdoMpd32             	1
fHsimClasses                       	0
fHsimPulseMPDelay                  	1
fHsimMvsimDb                       	0
fHsimMvsimDebug                    	0
fHsimAllXmrs                       	1
fHsimTaskFuncXmrs                  	0
fHsimTaskFuncXmrsDbg               	0
fHsimAllTaskFuncXmrs               	0
fHsimDoXmrProcessing               	1
fNoMergeDelays                     	0
uGlblTimeUnit                      	4
fHsimAllMtm                        	0
fSimprofileNew                     	0
fFsimFlag                          	0
fMcpDynClkProp                     	0
fHsimVhVlOpt                       	0
fHsimMdbVhVlInputFuseOpt           	0
fHsimMdbVhVlInoutFuseOpt           	0
fHsimMdbVhVlCcnOpt                 	0
fHsimVlVhOpt                       	0
fHsimVlVhVlOpt                     	0
fHsimVlVhBfuseOpt                  	0
xpropMergeMode                     	0
xpropUnifiedInferenceMode          	0
xpropOverride                      	0
xpropVlogLogFileNameLen            	0
xpropVhdlLogFileNameLen            	0
isXpropConfigEnabled               	0
fHsimVectorConst                   	0
fHsimAllMtmPat                     	0
fFsimDutPathEnabled                	0
fFsimEVCDEnabled                   	0
fStimDutCount                      	0
fFsimStrobeIdCount                 	0
fCcExSummaryReportFlag             	0
fCcExConnReport                    	0
fCcExConnDbReport                  	0
fCcExConfigFileFlag                	0
fFsimForcelist                     	0
fNewCBSemantics                    	1
fSchedAtEnd                        	0
fSpecifyInDesign                   	0
fSvNettRealResType                 	0
fFgpSched0User                     	0
fFgpDynelabFix                     	1
fHsimDumpFlatData                  	1
fHsimCompressDiag                  	1
fHsimPowerOpt                      	0
fLoopReportElab                    	0
fHsimRtl                           	0
fHsimCbkOptVec                     	1
fHsimDynamicCcnHeur                	1
fHsimOldLdr                        	0
fHsimSingleDB                      	1
uVfsGcLimit                        	50
fHsimCompatSched                   	0
fHsimCompatOrder                   	0
fHsimDynamicElabForGates           	1
fHsimCompressMdbForDynamicElab     	1
fHsimCompressMdbDiag               	0
fHsimDeferForceSelTillReElab       	0
fHsimModByModElab                  	1
fHsimExprID                        	1
fHsimSequdpon                      	0
fHsimDatapinOpt                    	0
fHsimUdpInitDump                   	0
fHsimExprPrune                     	0
fHsimMimoGate                      	0
fHsimNewChangeCheckFrankch         	1
fHsimNoSched0Front                 	0
fHsimNoSched0FrontForMd            	1
fHsimScalReg                       	0
fHsimNtbVl                         	0
fHsimICTimeStamp                   	0
fHsimICTagVA                       	0
fHsimICTagCCN                      	0
fHsimICDelayCCN                    	0
fHsimICDiag                        	0
fHsimMsidSched                     	0
fHsimNewCSDF                       	1
vcselabIncrMode                    	2
fHsimMPPackDelay                   	0
fHsimMultDriver                    	0
fHsimPart                          	0
fHsimPrlComp                       	0
fHsimPartTest                      	0
fHsimTestChangeCheck               	0
fHsimTestFlatNodeOrder             	0
fHsimTestNState                    	0
fHsimPartDebug                     	0
fHsimPartFlags                     	0
fHsimOdeSched0                     	0
fHsimNewRootSig                    	1
fHsimDisableRootSigModeOpt         	0
fHsimTestRootSigModeOpt            	0
fHsimIncrWriteOnce                 	0
fHsimUnifInterfaceStrId            	1
fHsimUnifInterfaceFlow             	1
fHsimUnifInterfaceFlowDiag         	0
fHsimUnifInterfaceFlowXmrDiag      	0
fHsimUnifInterfaceMultiDrvChk      	1
fHsimTfMultiDrvChkPCDiag           	0
fHsimNoVIPCBD                      	0
fHsimXVirForGenerateScope          	0
fHsimCongruencyIntTestI            	0
fHsimCongruencySVA                 	0
fHsimCongruencySVADbg              	0
fHsimCongruencyLatchEdgeFix        	0
fHsimCongruencyFlopEdgeFix         	0
fHsimCongruencyXprop               	0
fHsimCongruencyXpropFix            	0
fHsimCongruencyXpropDbsEdge        	0
fHsimCongruencyResetRecoveryDbs    	0
fHsimCongruencyClockControlDiag    	0
fHsimCongruencySampleUpdate        	0
fHsimCongruencyFFDbsFix            	0
fHsimCongruency                    	0
fHsimJaguarCongruency              	0
fHsimJaguarCongruencyCG            	0
fHsimJaguarCongruencyGrping        	0
fHsimCongruencySlave               	0
fHsimCongruencyCombinedLoads       	0
fHsimCongruencyFGP                 	0
fHsimDeraceClockDataUdp            	0
fHsimDeraceClockDataLERUpdate      	0
fHsimCongruencyPC                  	0
fHsimCongruencyPCInl               	0
fHsimCongruencyPCDbg               	0
fHsimCongruencyPCNoReuse           	0
fHsimCongruencyDumpHier            	0
fHsimCongruencyResolution          	0
fHsimCongruencyEveBus              	0
fHsimHcExpr                        	1
fHsCgOptModOpt                     	0
fHsCgOptSlowProp                   	0
fHsimCcnOpt                        	1
fHsimCcnOpt2                       	1
fHsimCcnOpt3                       	1
fHsimOneTranCcn                    	0
fHsimSmdMap                        	0
fHsimSmdDiag                       	0
fHsimSmdSimProf                    	0
fHsimEvtSimProf                    	0
fHsimSgdDiag                       	0
fHsimRtDiagLite                    	0
fHsimRtDiagLiteCevent              	100
fHsimRtDiag                        	0
fHsimSkRtDiag                      	0
fHsimDDBSRtdiag                    	0
fHsimDbg                           	0
fHsimCompWithGates                 	0
fHsimMdbDebugOpt                   	0
fHsimMdbDebugOptP1                 	0
fHsimMdbDebugOptP2                 	0
fHsimMdbPruneOpt                   	1
fHsimMdbMemOpt                     	0
hsimRandValue                      	0
fHsimSimMemProfile                 	0
fHsimSimTimeProfile                	0
fHsimElabMemProfile                	0
fHsimElabTimeProfile               	0
fHsimElabMemNodesProfile           	0
fHsimElabMemAllNodesProfile        	0
fHsimDisableVpdGatesProfile        	0
fHsimFileProfile                   	0
fHsimCountProfile                  	0
fHsimGenFuncDefComment             	0
fHsimXmrDefault                    	1
fHsimFuseWireAndReg                	0
fHsimFuseSelfDrvLogic              	0
fHsimFuseProcess                   	0
fHsimNoStitchDump                  	0
fHsimAllExtXmrs                    	0
fHsimAllExtXmrsDiag                	0
fHsimAllExtXmrsAllowClkFusing      	0
fHsimPageArray                     	262143
fHsimPageControls                  	262143
hsDfsNodePageElems                 	0
hsNodePageElems                    	0
hsFlatNodePageElems                	0
hsGateMapPageElems                 	0
hsGateOffsetPageElems              	0
hsGateInputOffsetPageElems         	0
hsDbsOffsetPageElems               	0
hsMinPulseWidthPageElems           	0
hsNodeUpPatternPageElems           	0
hsNodeDownPatternPageElems         	0
hsNodeUpOffsetPageElems            	0
hsNodeEblkOffsetPageElems          	0
hsNodeDownOffsetPageElems          	0
hsNodeUpdateOffsetPageElems        	0
hsSdfOffsetPageElems               	0
hsElabInstancesPageElems           	0
hsDelIparrPageElems                	0
hsIparrPageElems                   	0
fHsimPageAllLevelData              	0
fHsimAggrCg                        	0
fHsimViWire                        	1
fHsimPcCbOpt                       	1
fHsimAmsTunneling                  	0
fHsimAmsTunnelingDiag              	0
fHsimAmsNewDrs                     	0
fHsimScUpwardXmrNoSplit            	1
fHsimOrigNdbViewOnly               	0
fHsimVcsInterface                  	1
fHsimVcsInterfaceAlias             	1
fHsimSVTypesIntf                   	1
fUnifiedAssertCtrlDiag             	0
fHsimEnable2StateScal              	0
fHsimDisable2StateScalIbn          	0
fHsimVcsInterfaceAliasDbg          	0
fHsimVcsInterfaceDbg               	0
fHsimVcsVirtIntfDbg                	0
fHsimInitOpenPort                  	1
fHsimVcsAllIntfVarMem              	0
fHsimCheckVIDynLoadOffsets         	0
fHsimModInline                     	1
fHsimModInlineDbg                  	0
fHsimPCDrvLoadDbg                  	0
fHsimDrvChk                        	1
fHsimRtlProcessingNeeded           	0
fHsimGrpByGrpElab                  	0
fHsimGrpByGrpElabMaster            	0
fHsimNoParentSplitPC               	0
fHsimNusymMode                     	0
fHsimOneIntfPart                   	0
fHsimCompressInSingleDb            	2
fHsimCompressFlatDb                	0
fHsimNoTime0Sched                  	1
fHsimMdbSplitGates                 	0
fHsimDeleteInstances               	0
fHsimUserDeleteInstances           	0
fHsimDeleteGdb                     	0
fHsimMdbDeleteGutsEarly            	0
fHsimDelxmdbDiag                   	0
fHsimDeleteInstancesMdb            	0
fHsimVectorConstProp               	0
fHsStopSlowSigPropForMI            	1
fHsimPromoteParam                  	0
fHsimNoVecInRaptor                 	0
fHsimNewEvcd                       	1
fHsimNewEvcdTest                   	0
fHsimNewEvcdObnDrv                 	1
fHsimNewEvcdW                      	1
fHsimNewEvcdWTest                  	0
fHsimEvcdDbgFlags                  	0
fHsimNewEvcdMultiDrvFmt            	1
fHsimRelPath                       	0
fHsimDumpElabData                  	1
fHsimNoDeposit                     	0
fHsimDumpOffsetData                	1
fNoOfsOpt                          	0
fFlopGlitchDetect                  	0
fHsimClkGlitch                     	0
fHsimGlitchDumpOnce                	0
fHsimDynamicElab                   	1
fHsimDynamicElabDiag               	0
fHsimPrintPats                     	1
fHsimInterpreted                   	0
fHsimAggressiveCodegenForDelays    	1
fHsimAggressiveCgNtcDelays         	1
fHsimCgDelaysDiag                  	0
fHsimCodegenForVectors             	1
fHsimCgVectors2E                   	1
fHsimCgVectors2EByRef              	0
fHsimCgVectors2W                   	1
fHsimCgVectors2Cbk                 	1
fHsimCgVectors2Force               	0
fHsimCgVectors2Debug               	0
fHsimCgVectors2Diag                	0
fHsimHdlForceInfoDiag              	0
fHsimHdlForceInfo                  	0
fHsimCodegenForTcheck              	1
fHsimUdpsched                      	0
fHsimUdpTetramax                   	0
fHsimUdpDelta                      	0
fHsimLerGate                       	0
fHsimMasterNodesOpt                	0
fHsimTransOpt                      	1
fHsimGateGroup                     	0
fHsimGateGroupDiag                 	0
fHsimOldXmr                        	0
fHsimConst                         	1
fHsimOptimizeSeqUdp                	1
fHsimOptimizeNotifier              	0
fHsimPrintUdpTable                 	0
fHsimConstDelay                    	0
fHsimConstForce                    	0
fHsimCcnOpt4                       	0
fHsimCcnOpt5                       	0
fHsimCcnOptDiag                    	0
fHsimCcn                           	1
fHsimDynamicCcn                    	0
fHsimTestBoundaryConditions1       	0
fHsimTestBoundaryConditions2       	0
fHsimTestBoundaryConditions3       	0
fHsimTestElabNodeLimit             	0
fHsimIgnoreForceForNState          	0
fHsimElabNodeWidthCheck            	0
fHsimInsertSched0ForLhsSelects     	1
fHsimVectors                       	1
fHsimOde                           	0
fHsimOdeDynElab                    	0
fHsimOdeDynElabDiag                	0
fHsimOdeUdp                        	0
fHsimOdeSeqUdp                     	0
fHsimOdeSeqUdpXEdge                	0
fHsimOdeSeqUdpDbg                  	0
fHsimOdeRmvSched0                  	0
fHsimOde4State                     	0
fHsimOdeDiag                       	0
fHsimOdeWithVecNew                 	0
fHsimOdeAcceptDeadGates            	0
fHsimOdeAcceptValue4Loads          	0
fHsimOdeAmdSRLatch                 	0
fHsimRmvSched0OnDataOfFlop         	0
fHsimRmvSched0OnMpd                	0
fHsimAllLevelSame                  	0
fHsimDbsList                       	0
fHsimRtlDbsList                    	0
fHsimPePort                        	0
fHsimPrunePePort                   	0
fHsimPrunePePortNodes              	0
fHsimPeRtReadMultiStub             	0
fHsimPeIntfXmr                     	0
fHsimPeXmrDiag                     	0
fHsimPePropDiag                    	0
fHsimPeXmr                         	0
fHsimPePortDiag                    	0
fHsimPeDiag                        	0
fHsimPeMode                        	0
fHsimPeLevelize                    	0
fHsimPeFgpLevelize                 	0
fHsimDbgPeLevelize                 	0
fHsimPeFixedXmr                    	1
fHsimPePcFixScopeXmr               	1
fHsimPePcScopeXmrDiag              	0
fHsimUdpDbs                        	0
fHsimCodeShare                     	0
fHsimRemoveDbgCaps                 	0
fFsdbGateOnepassTraverse           	0
fVpdDeltaCycleInDesign             	0
fHsimAllowVecGateInVpd             	1
fHsimAllowAllVecGateInVpd          	0
fHsimAllowUdpInVpd                 	1
fHsimAllowAlwaysCombInVpd          	1
fHsimAllowAlwaysCombCmpDvcSimv     	0
fHsimAllowAlwaysCombDbg            	0
fHsimMakeAllP2SPrimary             	0
fHsimMakeAllSeqPrimary             	0
fHsimFsdbProfDiag                  	0
fHsimAllCapSet                     	1
fVIDriverPartComp                  	0
fPCAllNodesVIDriver                	0
fVpdSeqGate                        	0
fVpdUseMaxBCode                    	1
fFsdbUseBCodeLimit                 	0
fVpdHsIntVecGate                   	1
fVpdHsCmplxVecGate                 	1
fVpdHsVecGateDiags                 	0
fSeqGateCodePatch                  	0
fVpdLongFaninOpt                   	0
fVpdSeqLongFaninOpt                	0
fVpdNoLoopDetect                   	0
fVpdNoSeqLoopDetect                	0
fVpdOptAllowConstDriver            	0
fVpdAllowCellReconstruction        	1
fVpdRtlForSharedLib                	0
fFsdbRejectedDiag                  	0
fFsdbRejectedDiagExtra             	0
fVpdRtlForPartComp                 	1
fVpdRtlForPartCompDiag             	0
fConstInExprofVpdTable             	0
fHsimVpdOptGateMustDisable         	0
fHsimVpdOptGate                    	1
fHsimVpdOptDelay                   	0
fHsimVpdOptMPDelay                 	0
fHsimVpdOptDiag                    	0
fHsimVpdOptRtlIncrFix              	0
fHsimVpdOptDiagV                   	0
fHsimVpdOptZeroDelay               	1
fHsimCbkOptDiag                    	0
fHsimByRefIBN                      	1
fHsimWireMda                       	1
fHsimUniqifyElabDiag               	0
fHsimSparseMemDiag                 	0
fHsimForceCbkVec                   	1
fHsimSplitForceCbkVec              	1
fHsimLowPower                      	0
fHsimLowPowerDumpOnly              	0
fHsimLowPowerDiag                  	0
fHsimXpropFix                      	1
fHsimXpropConfigTrace              	0
fHsimNameBasedInterface            	1
fHsimVcsInterfaceHierDiag          	0
fHsimCbSchedFix                    	0
fHsimIncrDebug                     	0
fHsimXmrConChksum                  	1
fHsimNoNState                      	0
fHsimCatchNoReuse                  	0
fHsimOffsetFix                     	0
fHsimNoReuse                       	0
fHsimSK                            	0
fHsimSKInteg                       	0
fHsimSKCtOnly                      	0
fHsimSharedKernel                  	0
fHsimSKIncr                        	0
fElabModTimeProfCount              	0
fHsimChangeSharedLib               	0
fHsimNewIncr                       	1
fHsimIncrSkip                      	0
fHsimSecondCheckMdb                	0
fHsimIntraXmrNotMaster             	0
fHsimExtNodeDiag                   	0
fHsimExtIntfXmrDebug               	0
fHsimExtXmrNodeDiag                	0
fPartTopElabModName                	0
fHsimPreResolveXmr                 	1
fHsimEarlyResolveXmr               	0
fHsimXmrLocalDiag                  	0
fHsimNoIntfXmrNonMaster            	1
fHsimXmrPropDebug                  	0
fHsimXmrElabDebug                  	0
fHsimXmrNoMaster                   	1
fHsimXmrNoMasterIBIF               	1
fHsimPcSdfLocal                    	0
fHsimMasterXmrDiag                 	0
fHsimPeLocalXmr                    	0
fHsimPeLocalXmrDiag                	0
fHsimPeUpXmrDiag                   	0
fHsimIncrMaster                    	0
fHsimEffTest                       	0
fHsimIncrTest                      	0
fHsimIncrTesting                   	0
fHsimOnepass                       	0
fHsimPartModSplit                  	0
fHsimNoIncrMatch                   	0
fHsimNoEmIncr                      	0
fHsimIncrXmrDebug                  	0
fHsimMergeOnly                     	0
fHsimStitchNew                     	0
fFrcRelCbk                         	1
fPulserrWarn                       	1
hsMtmSpec                          	0
fprofile                           	0
fPreserveDaidir                    	1
fElabGraphLevelize                 	0
fHsimFreeNodeMap                   	0
fHsimGlobalLevelize                	0
fHsimLevelize                      	1
fHsimSelectLevelize                	0
fHsimSelectEdgeData                	1
fHsimSelectEdgeDataDbg             	0
fHsimSelectEdgeDataSched0          	0
fHsimSelectEdgeDataSanity          	0
fHsimLevelizeFlatNodeLimit         	22
fHsimLevelizeNoSizeLimit           	1
fHsimLevelizeForce                 	0
fHsimParallelLevelize              	0
fHsimParallelLevelizeDbg           	0
fHsimLevelizeNoCgDump              	0
fHsimReuseVcs1Sem                  	0
semLevelizeVar                     	-1
fHsimLevelizeDbg                   	0
fHsimMinputsPostEval               	0
fHsimSeqUdpDbsByteArray            	0
fHsimCoLocate                      	0
fHsimNoinlSched0lq                 	0
fHsimBepats                        	0
fHsimBepatsInfra                   	0
fHsimFepats                        	0
fSv2Cpp                            	0
fHsimUdpOutputOpt                  	0
fHsimSeqUdpEblkOpt                 	0
fHsimSeqUdpEblkPcOpt               	0
fHsimCustomLibGenRma               	0
fHsimCustomLibGenRmaDebug          	0
fHsimXpropEnabled                  	0
fHsimEblkOpt                       	1
fHsimSeqUdpEblkOptDiag             	0
fHsimGateInputAndDbsOffsetsOpt     	1
fHsimMasterNodeSort                	3
fHsimMasterNodeSortDiag            	0
fHsimGDBLargeModule                	1
fHsimRelaxSched0                   	0
fHsimUdpDynElab                    	0
fHsimCbDynElab                     	0
fHsimCompressData                  	4
fHsimIgnoreCaps                    	0
fHsimMdbIgnoreCaps                 	0
fHsimMdbGgIgnoreCaps               	0
fHsimMdbDceIgnoreCaps              	0
fHsimIgnoreZForDfuse               	1
fHsimFuseSameDriversIgnoreReElab   	0
fHsimIgnoreDifferentCaps           	0
fHsimIgnoreDifferentNStates        	0
fHandleGlitchQC                    	1
fGlitchDetectForAllRtlLoads        	0
fHsimAllowFuseOnRegWithMultDrivers 	0
fHsimFuseConstDriversOpt           	1
fHsimMdSchedTr                     	0
fHsimIgnoreReElab                  	0
fHsimFuseMultiDrivers              	0
fHsimSched0                        	0
fHsimPulseFilter                   	0
fHsimPulseFilterOpt                	1
fHsimNoSched0Reg                   	0
fHsimAddSched0                     	0
fHsimLargeBc                       	0
fHsimLargePdbModule                	0
fHsimMMDebug                       	0
fHsimMMLimit                       	0
hsimMMLimit                        	0
fHsimAmsFusionEnabled              	0
fHsimAmsWrealMdrEnabled            	0
fHsimAmsWrealInitValZero           	1
fWrealForce                        	0
fHsimCgMarkers                     	0
fHsimSplitRmaCode                  	1
rmapatsPattCountThreshold          	1000
fHsimElab64                        	0
fHsimTestFnn64                     	0
fHsimTestDgn64                     	0
fHsimRtlDbs                        	0
fHsimWakeupId                      	0
fHsimSkipSched0Tran2MosMDFanout    	0
fHsimPassiveIbn                    	0
fHsimInitialConst                  	0
fHsimForceRtlDbs                   	0
fHsimBcOpt                         	1
fHsimBcOptDebug                    	0
fHsimBfuseFast                     	1
fHsimParallelElab                  	0
fHsimParallelElabVcs1              	0
fpicArchive                        	1
fHsimInterconFE                    	1
fHsimMxOpt                         	1
fHsimModpathFE                     	1
fHsimPathOnCCN                     	0
fHsimTranDelay                     	0
fHsimTranSpdNodeAssert             	0
fHsimDelayToCCN                    	0
fHsimOptMPDelayLoad                	0
fHsimTransMPDelay                  	1
fLargeSizeSdfTest                  	0
fAllMtm                            	0
fHsimDelayGateMbme                 	0
fHsimDelayGateMbmeOld              	0
fHsimNdb                           	1
fHsimNdbDebug                      	0
fHsimNdbTest                       	0
fHsimGrpByGrpElabIncrTest          	0
fHsimGrpByGrpElabIncrTest2         	0
fHsimTestAggrCg                    	0
fHsimOneInputGateAggrCg            	0
fHsimCertitude                     	0
fHsimRaceDetect                    	0
fHsimEventOrderDiag                	0
fHsimEODCommandLineOption          	0
fHsimSRaceDiag                     	0
fHsimSRaceCommandLineOption        	0
fHsimGenRace                       	0
fCheckTcCond                       	0
fHsimMcpFlags                      	0
fHsimMcpAdjFactor                  	6
fHsimMcpDumpChunk                  	4096
fHsimPcPeMode                      	0
fHsimScanOpt                       	0
fHsimScanOptPartComp               	0
fHsimHsoptNoScanOpt                	0
fHsimNoScanOptDeadLogic            	1
fHsimScanOptFixForDInSIPath        	1
fHsimNoScanOptForNonScanLoad       	0
fHsimScanOptLoopFix                	1
fHsimScanOptLoopFix2               	0
fHsimScanOptRelaxDbg               	0
fHsimScanOptRelaxDbgDynamic        	0
fHsimScanOptRelaxDbgDynamicPli     	0
fHsimScanOptRelaxDbgDiag           	0
fHsimScanOptRelaxDbgDiagHi         	0
fHsimScanOptNoErrorOnPliAccess     	0
fHsimScanOptNoFusedSwitchTurnOn    	1
fHsimScanOptTiming                 	0
fRelaxIbnSchedCheck                	2
fHsimScanOptNoDumpCombo            	0
fHsimScanOptPrintSwitchState       	0
fHsimScanOptSelectiveSwitchOn      	1
fHsimScanOptSingleSEPliOpt         	1
fHsimScanOptDesignHasDebugAccessOnly	0
fHsimScanOptPrintPcode             	0
fHsimScanOptRelaxDbgDefaultOn      	0
fHsimNettypeOneDrvPerfOpt          	0
fHsimOldNettypeResFnOffset         	0
fHsimScanOptHashTableTest          	0
fHsimScanoptDump                   	0
fHsimScanDbgFunc                   	0
fHsimScanDbgPerf                   	0
fHsimScanOptAggr                   	0
fHsimScanOptFuse                   	1
fHsimScanMemOpt                    	1
fHsimScanChainOpt                  	0
flatNodeScanPathCountLimitIndex    	0
fHsimForceChangeCheck              	0
fHsimFuseConsts                    	0
fHsimMemBusOpt                     	0
fHsimDefLevelElab                  	0
fHsimOneInstElabMods               	0
fHsimOneInstElabModsHeur           	1
fHsimOneInstElabModsAllowDbg       	0
fHsimTopElabMods                   	0
fHsimNoStitchMap                   	0
fHsimUnifiedModName                	0
fHsimVIIntegrityCheck              	0
fHsimOrigViewType                  	0
fHsimXmrDumpFullDR                 	0
fHsimXmrDumpDebug                  	0
fHsimRTLoopDectEna                 	0
fHsimAssertInActive                	0
dGblTeE                            	1.000000
dGblTeR                            	1.000000
dGblPeE                            	1.000000
dGblPeR                            	1.000000
fNewdaidirpath                     	0
fHsimDelayMbmeCheck                	4
fHsimMdbPartInputLimit             	1
fHsimSdfData                       	0
fHsimDesignHasSdfAnnotation        	0
fHsimDesignUsesParallelVcs         	0
fHsimCMEnabled                     	0
fHsimNotDelRefData                 	0
fGblPulseDecayData                 	0
fGblPulsePartCtrl                  	0
fGblMSah                           	0
fGblMSTe                           	0
fGblIntPe                          	0
fGblTe                             	0
fGblPe                             	0
iPulseR                            	100
iPulseE                            	100
iTransR                            	100
iTransE                            	100
fPulseOpt                          	0
fGblPulseOnD                       	0
fGblPulseOnE                       	0
fVCSiFlow                          	0
fSystemVCSEnabled                  	1
fHsimForcedPort                    	0
fpicOption                         	1
fModelSave                         	0
fHsimGenObj                        	1
fHsimRts                           	0
fHsimCbkMemOptDebug                	0
fHsimMasterModuleOnly              	0
fHsimDumpOriginalFlatNodeNumsMap   	0
fHsimRecordPli                     	0
fHsimPlaybackPli                   	0
fHsimModByModElabForGates          	0
fHsimMdbOpts                       	0
fHsimMdbInlineNew                  	0
fHsimMdbSelUdp2Rtl                 	0
fHsimMdbUdp2Rtl                    	0
fHsimZeroDelayDelta                	1
fHsimMdbUdp2Rtl_3state             	0
fHsimMdbUdp2Rtl_noxedge            	0
fHsimMdbUdp2Rtl_dfsr               	0
fHsimMdbOptimizeSeqUdp             	0
fHsimNoMdbOptimizeSeqUdpPrune      	0
fHsimNoMdbOptimizeSeqUdpPrune2inputs	0
fHsimMdbB2BLatch                   	0
fHsimMdbAggr                       	0
fHsimMdbGateGroupNew               	0
fHsimMdbUdpGroup                   	0
fHsimMdbOptimizeConstants          	0
fHsimMdbDfuse                      	0
fHsimMdbBfuse                      	0
fHsimMdbDce                        	0
fHsimMdbMpopt                      	0
fHsimMdbCondMpOpt                  	0
fHsimMdbSimplifyMpCond             	0
fHsimDceIgnorecaps                 	0
fHsimCondModPathDbs                	0
fHsimCondModPathCompact            	0
fHsimMdbCondMpMerge                	0
fHsimModPathCg                     	0
fHsimNoCondModPathCg               	0
fHsimCompactCode                   	0
fHsimCondTC                        	0
fHsimMacroTC                       	0
fHsimCondMPConst                   	0
fHsimCondTCConst                   	0
fHsimMergeDelay                    	0
fHsimRuntimeSdfOpt                 	0
fHsimUpHierIC                      	0
fHsimDelayOpt                      	0
fRemoveDelonTrans                  	1
fHsimModPathLoadOpt                	1
fHsimMdbTranOpt                    	0
fHsimMdbOptWithTranDelay           	0
fHsimMdbTranMerge                  	0
fHsimPostCodeCompile               	0
fHsimPostCodeCompileCJobs          	16
fHsimLrmSupply                     	0
fHsimNewMbmeFlow                   	0
fHsimBackEndInteg                  	0
fHsimBackEndIntegCapsOk            	0
fHsimBackEndIntegDiag              	0
fHsimBackEndIntegMaxIbns           	1024
fHsimBackEndIntegDeadObns          	0
fHsimTran2MosDriver                	1
fHsimTran2MosIgnoreReadCaps        	0
fHsimTran2MosPortSupply            	1
fHsimDumpCcn                       	0
fHsimMdbNStateAnalysis             	0
fHsimMdbAdjustWidth                	0
fHsimMdbOptimizeSelects            	0
fHsimMdbScalarizePorts             	0
fHsimMdbScalarizePortsThreshold    	100000000
fHsimMdbOptimizeSelectsHeuristic   	1
fHsimMdbPart                       	0
fHsimMdb1006Partition              	0
fHsimVectorPgate                   	0
fHsimNoHs                          	0
fHsimXmrPartition                  	0
fHsimNewPartition                  	0
fHsimElabPart                      	0
fHsimElabPartDiag                  	0
fHsimPMdb                          	0
fHsimElabPartThreshHoldDesign      	1
fHsimParitionCellInstNum           	1000
fHsimParitionCellNodeNum           	1000
fHsimParitionCellXMRNum            	1000
fHsimNewPartCutSingleInstLimit     	268435455
fHsimElabModDistNum                	0
fHsimElabPartThreshHoldModule      	3000000
fHsimElabPartMaxDepth              	0
fHsimElabPartExternalLimitHeur     	10
fHsimElabPartPercentLimit          	0
fHsimElabPartEnableOneInst         	0
fHsimNewPartitionHeur              	0
fHsimElabPartForcedportMaxDep      	0
fHsimElabPartMinHeight             	0
fHsimRtPartitionTable              	3
fHsimElabPartMaxTraversal          	0
fHsimPortsRewriteSdfTop            	0
fHsimPCPortPartition               	0
fHsimPortPartition                 	0
fHsimMdbHdbsBehavior               	0
fHsimMdbHdbsBehaviorTC             	0
fHsimMdbIbnObnPartition            	0
fHsimMdbDebugOpt0                  	0
fHsimMdbClockAnalysis              	0
fHsimMdbMimo                       	0
fHsimMdbMimoLite                   	0
fHsimMdbMimoAggr                   	0
fHsimDumpMdb                       	0
fHsimDumpMdbVpd                    	0
fHsimDumpMdbPartition              	0
fHsimElabDiag                      	0
fHsimElabMasterDiag                	0
fHsimElabDiagSummary               	0
fHsimElabDiagMn                    	0
fHsimElabDiagMnCount               	0
fHsimElabDiagLite                  	0
fHsimSimpCollect                   	0
fHsimPcodeDiag                     	0
fHsimDbsAlwaysBlocks               	1
fHsimPrintNodeMap                  	0
fHsimSvAggr                        	0
fHsimDynamicFlatNode               	0
fHsimSeqPrimCg                     	1
fHsimDdPats                        	0
fHsimPatOpt                        	3
fHsimPatInline                     	0
fHsimPatOutline                    	0
fHsimFastelab                      	0
fHsimMacroOpt                      	0
fHsimSkipOpt                       	0
fHsimSkipOptFanoutlimit            	0
fHsimSkipOptRootlimit              	0
fHsimFuseDelayChains               	0
fFusempchainsFanoutlimit           	0
fFusempchainsDiagCount             	0
fHsimCloadOpt                      	0
fHsimNoICDelayPropPwEqDelay        	0
fHsimPrintMopComment               	0
fNewRace                           	0
fHsimCgVectorGates                 	0
fHsimCgVectorGates1                	0
fHsimCgVectorGates2                	0
fHsimCgVectorGatesNoReElab         	0
fHsimCgScalarGates                 	0
fHsimCgScalarGatesExpr             	0
fHsimCgScalarGatesLut              	0
fHsimCgRtl                         	1
fHsimCgRtlFilter                   	0
fHsimCgRtlDebug                    	0
fHsimCgRtlSize                     	15
fHsimNewCg                         	0
fHsimNewCgRt                       	0
fHsimNewCgFg                       	0
fHsimNewCgMinput                   	0
fHsimNewCgUpdate                   	0
fHsimNewCgMP                       	0
fHsimNewCgMPRt                     	0
fHsimNewCgMPRetain                 	0
fHsimNewCgTC                       	0
fHsimCgRtlInfra                    	1
fHsimGlueOpt                       	0
fHsimPGatePatchOpt                 	0
fHsimCgNoPic                       	0
fHsimElabModCg                     	0
fPossibleNullChecks                	0
fHsimProcessNoSplit                	1
fHsimMdbInstDiag                   	0
fHsimMdbOptInSchedDelta            	0
fScaleTimeValue                    	0
fDebugTimeScale                    	0
fPartCompSDF                       	0
fHsimNbaGate                       	1
fHsimVarDelay                      	0
fDumpDtviInfoInSC                  	0
fDtviDiag                          	0
fFusedNodeInfo                     	1
fDumpSDFBasedMod                   	1
fHsimSdfIC                         	0
fHsimSdfICOverlap                  	0
fHsimSdfICDiag                     	0
fHsimSdfICOpt                      	0
fHsimMsvSdfInout                   	0
fOptimisticNtcSolver               	0
fHsimAllMtm                        	0
fHsimAllMtmPat                     	0
fHsimSdgOptEnable                  	0
fHsimSVTypesRefPorts               	0
fHsimGrpByGrpElabIncr              	0
fHsimGrpByGrpElabIncrDiag          	0
fHsimEvcdTranSeen                  	0
fHsimMarkRefereeInVcsElab          	0
fHsimStreamOpFix                   	1
fHsimInterface                     	0
fHsimNoPruning                     	0
fHsimNoVarBidirs                   	0
fHsimMxWrapOpt                     	0
fHsimMxTopBdryOpt                  	0
fHsimAggressiveDce                 	0
fHsimDceDebug                      	1
fHsimDceDebugDB                    	0
fHsimDceDebugSC                    	0
fHsimDceDebugUseHeuristics         	1
fHsimDceDebugVec                   	0
fHsimDceDebugDiag                  	0
fHsimDceDebugFanoutThreshold       	10000000
fHsimGlobalGPNFlagBit              	0
fHsimMdbUnidirSelects              	0
fHsimMdbUnidirSelectsNol2w         	1
fHsimMdbNewDebugOpt                	0
fHsimMdbNewDebugOptExitOnError     	1
fHsimNewDebugOptMemDiag            	0
hsGlobalVerboseLevel               	0
fHsimTestPxpcode                   	0
fHsimMdbVectorConstProp            	1
fHsimEnableSeqUdpWrite             	1
fHsimDisableSeqUdpWriteForce       	0
fHsimDumpMDBOnlyForSeqUdp          	0
fHsimInitRegRandom                 	0
fHsimInitRegRandomVcs              	1
fUdpPruneNoData                    	0
fRegTypesToWire                    	0
fEnableNewFinalStrHash             	0
fEnableNewAssert                   	1
fRunDbgDmma                        	0
fAssrtCtrlSigChk                   	1
fCheckSigValidity                  	0
fUniqPriToAstRewrite               	0
fUniqPriToAstCtrl                  	0
fAssertcontrolUniqPriNewImpl       	0
fRTLoopDectEna                     	0
fCmplLoopDectEna                   	0
fHsimMopFlow                       	1
fUCaseLabelCtrl                    	0
fUniSolRtSvaEna                    	1
fUniSolSvaEna                      	1
fXpropRtCtrlCallerOnly             	0
fXpropVhVlCombineDisabled          	0
fXpropVcselabPreparseCfg           	1
fXpropVcselabPreparseCfgDbg        	0
fHsimEnableDbsMemOpt               	1
fHsimDebugDbsMemOpt                	0
fHsimRenPart                       	0
cHsimNonReplicatedInstances        	0
fHsimNoTcSched                     	0
fHsimSchedOpt                      	0
fHsimXmrAllWires                   	0
fHsimXmrDiag                       	0
fHsimXmrPort                       	0
fHsimFalcon                        	1
fHsimGenForProfile                 	0
fHsimDumpMdbAll                    	0
fHsimDumpMdbGates                  	0
fHsimDumpMdbPrune                  	0
fHsimDumpMdbInline                 	0
fHsimDumpMdbCondTC                 	0
fHsimDumpMdbNState                 	0
fHsimDumpMdbVhVlInputFuseOpt       	0
fHsimDumpMdbVhVlInoutFuseOpt       	0
fHsimDumpMdbVhVlCcnOpt             	0
fCompressSDF                       	0
fHsimDumpMdbSchedDelta             	0
fHsimDumpMdbNoVarBidirs            	0
fHsimDumpMdbScalarize              	0
fHsimDumpMdbVecInst                	0
fHsimDumpMdbVecInst2               	0
fHsimDumpMdbDce                    	0
fHsimDumpMdbScanopt                	0
fHsimDumpMdbSelects                	0
fHsimDumpMdbAggr                   	0
fHsimDumpMdbOptConst               	0
fHsimDumpMdbVcsInterface           	0
fHsimDumpMdbDfuse                  	0
fHsimDumpMdbBfuse                  	0
fHsimDumpMdbTranOpt                	0
fHsimDumpMdbOptLoops               	0
fHsimDumpMdbSeqUdp                 	0
fHsimDumpMdbMpOpt                  	0
fHsimDumpMdbGG                     	0
fHsimDumpMdbUdpGG                  	0
fHsimDumpMdbMimo                   	0
fHsimDumpMdbUdp2rtl                	0
fHsimDumpMdbUdpDelta               	0
fHsimDumpMdbDebugOpt               	0
fHsimDumpMdbSplitGates             	0
fHsimDumpMdb1006Part               	0
fHsimDumpMdbPart                   	0
fHsimDumpMdbSimplifyMpCond         	0
fHsimDumpMdbCondMpMerge            	0
fHsimDumpMdbCondMp                 	0
fHsimDumpMdbCondModPathDbs         	0
fHsimSdfAltRetain                  	0
fHsimDumpMdbCompress               	1
fHsimDumpMdbSummary                	0
fHsimBfuseOn                       	1
fHsimBfuseHeur                     	0
fHsimBfuseHash                     	1
fHsimBfuseNoRedundantFanout        	1
fHsimBFuseVectorMinputGates        	0
fHsimBFuseVectorAlways             	0
fHsimDfuseOn                       	1
fHsimDumpMdbPruneVpdGates          	0
fHsimGates1209                     	0
fHsimCgRtlNoShareSmd               	0
fHsimGenForErSum                   	0
fVpdOpt                            	1
fHsimMdbCell                       	0
fHsimCellDebug                     	0
fHsimMdbCellFuse                   	0
fHsimMdbCellForDce                 	0
fHsimMdbCellForDceDebug            	0
fHsimMdbCellComplexity             	1.500000
fHsimMdbCellHeur                   	1
fHsimMdbCellDiag                   	0
fHsimMdbCellEgSched0               	0
fHsimMdbCellVpdIbif                	0
fHsimMdbCellElabJ                  	0
fHsimMdbCellHashForRTEncode        	0
fHsimNoPeekInMdbCell               	0
fDebugDump                         	1
fHsimOrigNodeNames                 	0
hsimSrcList                        	filelist
fHsimFileNameForQ                  	grid.command
xpropLogFileName                   	(null)
fHsimCgVectors2VOnly               	0
fHsimPortCoerce                    	0
fHsimBidirOpt                      	0
fHsimCheckLoop                     	1
fHsimCheckLoopDiag                 	0
fHsimCheckLoopMore                 	0
fHsimLoop                          	1
fHsimKeyExpr                       	1
fHsimMdbDeltaGate                  	0
fHsimMdbDeltaGateAggr              	0
fHsimMdbVecDeltaGate               	0
fHsimVpdOptVfsDB                   	1
fHsimMdbPruneVpdGates              	1
fHsimDynamicEblk                   	0
fHsimDynamicEblkDiag               	0
fHsimCompactCompiledLoads          	0
fHsimPcPe                          	0
fHsimVpdGateOnlyFlag               	1
fHsimMxConnFrc                     	0
fHsimNewForceCbkVec                	0
fHsimNewForceCbkVecDiag            	0
fHsimMdbReplaceVpdHighConn         	1
fHsimVpdHighConnReplaced           	1
fHsimVpdOptSVTypes                 	1
fHsimDlyInitFrc                    	0
fHsimPIP                           	0
fHsimRTLoopDectOrgName             	0
fHsimVpdOptPC                      	0
fHsimFusePeXmrFo                   	0
fHsimXmrSched                      	0
fHsimNoMdg                         	0
fHsimUseBidirSelectsInVectorGates  	0
fHsimGates2                        	0
fHsimVectorGates                   	0
fHsimHilCg                         	0
fHsimHilVecAndRtl                  	0
fHsimMdbcgLut                      	0
fHsimMdbcgSelective                	0
fHsimMdbcgUnidirSel                	0
fHsimMdbcgLhsConcat                	0
fHsimMdbcgSelectSplit              	0
fHsimMdbcgProcessSelSplit          	0
fHsimMdbcgMultiDelayControl        	1
fHsimParGateEvalMode               	0
fHsimDFuseVectors                  	0
fHsimDFuseVecIgnoreFrc             	0
fHsimDFuseZero                     	0
fHsimDFuseOpt                      	1
fHsimAllPortsDiag                  	0
fHsimPruneOpt                      	0
fHsimSeqUdpPruneWithConstInputs    	0
fHsimSafeDFuse                     	0
fHsimVpdOptSelGate                 	1
fHsimVpdOptSkipFuncPorts           	0
fHsimVpdOptAlways                  	1
fHsimVpdOptMdbCell                 	0
fHsimVpdOptPartialMdb              	1
fHsimVpdOptPartitionGate           	1
fHsimVpdOptPartitionGateCompress   	1
fHsimVpdOptXmr                     	1
fHsimVpdOptConst                   	1
fHsimVpdOptMoreLevels              	1
fHsimSWave                         	0
fHsimNoSched0InCell                	1
fHsimPartialMdb                    	0
hsimPdbLargeOffsetThreshold        	1048576
fHsimFlatCell                      	0
fHsimFlatCellLimit                 	0
fHsimRegBank                       	0
fHsimHmetisMaxPartSize             	0
fHsimHmetisGateWt                  	0
fHsimHmetisUbFactor                	0
fHsimHmetis                        	0
fHsimHmetisDiag                    	0
fHsimRenumGatesForMdbCell          	0
fHsimHmetisMinPart                 	0
fHsim2stCell                       	0
fHsim2stCellMinSize                	0
fHsimMdbcgDebug                    	0
fHsimMdbcgDebugLite                	0
fHsimMdbcgSepmem                   	0
fHsimMdbcgObjDiag                  	0
fHsimMdbcg2stDiag                  	0
fHsimMdbcgRttrace                  	0
fHsimMdbVectorGateGroup            	1
fHsimMdbProcDfuse                  	1
fHsimMdbHilPrune                   	0
fHsimNewConstProp                  	0
fHsimNewMdbNstate                  	0
fHsimProcessNstate                 	0
fHsimMdbModpathNstate              	0
fHsimPgateConst                    	0
fHsCgOpt                           	1
fHsCgOptUdp                        	1
fHsCgOptRtl                        	1
fCgOptTmask                        	5
fHsCgOptDiag                       	0
fHsCgOptBackProp                   	1
fHsCgOptMoreBackProp               	0
fHsCgOptNoZCheck                   	0
fHsCgOptEnableZSupport             	1
fHsCgOptDce                        	1
fHsCgOpt4StateInfra                	1
fHsCgOptMultiDriverSupport         	0
fHsCgOptUdpChkDataForWakeup        	1
fHsNBACgOpt                        	1
fHsCgOptXprop                      	0
fHsimMdbcgDiag                     	0
fHsCgMaxInputs                     	6
fHsCgOptFwdPass                    	1
fHsimHpnodes                       	0
fLightDump                         	0
fRtdbgAccess                       	0
fRtdbgOption                       	0
fVpdBeforeScan                     	1
fHsCgOptMiSched0                   	0
fgcAddSched0                       	0
fParamClassOptRtDiag               	0
fHsRegress                         	0
fHsBenchmark                       	0
fHsimCgScalarVerilogForce          	1
fVcsElabToRoot                     	1
fHilIbnObnCallByName               	0
fHsimMdbcgCellPartition            	0
fHsimCompressVpdSig                	0
fHsimLowPowerOpt                   	0
fHsimUdpOpt                        	1
fHsimUdpOptNew                     	0
fHsVecOneld                        	0
fNativeVpdDebug                    	0
fNewDtviFuse                       	1
fHsimVcsGenTLS                     	1
fAssertSuccDebugLevelDump          	0
fHsimMinputsChangeCheck            	0
fHsimClkLayout                     	0
fHsimSortLayout                    	0
fHsimIslandLayout                  	0
fHsimConfigSched0                  	0
fHsimSelectFuseAfterDfuse          	0
vcsNettypeDbgOpt                   	4
fHsimFoldedCell                    	0
fHsimSimon2Mdb                     	0
fHsimSWaveEmul                     	0
fHsimSWaveDumpMDB                  	0
fHsimSWaveDumpFlatData             	0
fHsimRenumberAlias                 	0
fHsimAliasRenumbered               	0
fHilCgMode                         	115
fHsimUnionOpt                      	0
fHsimFuseSGDBoundaryNodes          	0
fHsimRemoveCapsVec                 	0
fHsimSlowNfsRmapats                	0
fHsCgOptMux                        	0
fHsCgOptFrc                        	0
fHsCgOpt30                         	0
fHsLpNoCapsOpt                     	0
fHsCgOpt4State                     	1
fHashTableSize                     	12
fSkipStrChangeOnDelay              	1
fHsimTcheckOpt                     	0
fHsCgOptMuxMClk                    	0
fHsCgOptMuxFrc                     	0
fHsCgOptNoPcb                      	0
fHsCgOptMin1                       	0
fHsCgOptUdpChk                     	1
fHsChkXForSlowSigProp              	1
fHsimVcsParallelDbg                	0
fHsimVcsParallelStrategy           	0
fHsimVcsParallelOpt                	0
fHsimVcsParallelSubLevel           	4
fHsimParallelEblk                  	0
fHsimByteCodeParts                 	1
fHsimByteCodePartTesting           	0
fHsimByteCodePartAssert            	0
fFgpNovlInComp                     	0
fFutEventPRL                       	0
fFgpNbaDelay                       	0
fHsimDbsFlagsByteArray             	0
fHsimDbsFlagsByteArrayTC           	0
fHsimDbsFlagsThreadArray           	0
fHsimLevelCompaction               	0
fHsimLevelCompactionThreshold      	0
fHsimGateEdgeEventSched            	0
fHsimGateEdgeEventSchedThreshold   	0
fHsimGateEdgeEventSchedSanity      	0
fHsimSelectEdgeEventSched          	0
fHsimSelectEdgeEventSchedNoTempReuse	0
fHsimSelectEdgeEventSchedThreshold 	0
fHsimMaxComboLevels                	0
fHsimEgschedDynelab                	0
fHsimUdpClkDynelab                 	0
fUdpLayoutOnClk                    	0
fHsimDiagClk                       	1
fHsimPatReason                     	1
fDbsPreCheck                       	0
fHsimSched0Analysis                	0
fHsimMultiDriverSched0             	0
fHsimLargeIbnSched                 	0
fFgpHierarchical                   	0
fFgpHierAllElabModAsRoot           	0
fFgpHierPCElabModAsRoot            	0
fFgpHierMarkBoundariesAsExclusive  	0
fFgpHierRootFromConfig             	0
fFgpHierRandRoot                   	0
fFgpHierThreshold                  	10
fFgpAdjustDataLevelOfLatch         	1
fHsimUdpXedgeEval                  	0
fFgpRaceCheck                      	0
fFgpUnifyClk                       	0
fFgpSmallClkTree                   	0
fFgpSmallRtlClkTree                	4
fFgpNoRtlUnlink                    	0
fFgpNoRtlAuxLevel                  	0
fFgpNumPartitions                  	8
fFgpMultiSocketCompile             	0
fFgpMultiSocketAfterGrping         	0
fFgpMultiSocketNCuts               	1
fFgpMultiSocketDiag                	0
fFgpMultiSocketRecomputePart       	1
fFgpDataDepOn                      	0
fFgpDDIgnore                       	0
fFgpDDVecSplit                     	0
fFgpDDDebug                        	0
fFgpXmrDepOn                       	0
fFgpTbCbOn                         	0
fFgpTbEvOn                         	1
fFgpTbNoVSA                        	0
fFgpTbEvXmr                        	0
fFgpTbEvCgCall                     	1
fFgpDisabledLevel                  	512
fFgpNoSdDelayedNbas                	1
fFgpNBAClosingCircularCallEdge     	1
fFgpNoEvNbas                       	0
fFgpTimingFlags                    	0
fFgpTcLoadThreshold                	0
fFgpSched0Level                    	0
fHsimFgpMultiClock                 	0
fFgpScanOptFix                     	0
fFgpSched0UdpData                  	0
fFgpSanityTest                     	0
fFgpSanityTest_Eng                 	1
fFgpAlternativeLevelization        	0
fFgpHighFanoutThreshold            	1024
fFgpSplitGroupLevels               	1
fFgpSplitGroupIbn                  	1
fFgpSplitGroupGateEdge             	1
fFgpSplitGroupEval                 	3
fFgpGroupingPerfDiag               	0
fFgpSplitGroupDiag                 	0
fFgpStricDepModDiag                	0
fFgpIPProtect                      	0
fFgpIPProtectStrict                	0
fFgpNoVirtualThreads               	0
fFgpLoadBalance0DiagComp           	0
fFgpLoadBalance0CompileTime        	1
fFgpHsCallGraph                    	1
fFgpNoInstStartNode                	1
fFgpDepositDiag                    	0
fFgpEvtDiag.diagOn                 	0
fFgpEvtDiag.printAllNodes          	0
fFgpMangleDiagLog                  	0
fFgpMultiExclDiag                  	0
fFgpSingleExclReason               	0
fHsDoFaninFanoutSanity             	0
fHsFgpNonDbsOva                    	1
fFgpParallelTask                   	1
fFgpCondTcGateedge                 	0
fFgpIbnSched                       	0
fFgpIbnSchedOpt                    	0
fFgpIbnSchedNoLevel                	0
fFgpIbnSchedThreshold              	0
fFgpIbnSchedDyn                    	0
fFgpObnSched                       	0
fFgpMpStateByte                    	0
fFgpTcStateByte                    	0
fHsimVirtIntfDynLoadSched          	0
fHsimNetXmrDrvChk                  	0
fFgpNoRtimeFgp                     	0
fHsFgpGlSched0                     	0
fFgpExclReason                     	0
fHsimIslandByIslandElab            	0
fHsimIslandByIslandFlat            	0
fHsimIslandByIslandFlat1           	0
fHsimVpdIBIF                       	0
fHsimXmrIBIF                       	1
fHsimReportTime                    	0
fHsimIncrElab                      	0
fHsimElabJ                         	0
fHsimMemOpt                        	0
fHsimQuantumMachine                	0
fHsimNoDoMdbOptOnSlave             	0
fHsimUnmap                         	0
fHsimElabQ                         	0
fHsimParallelPartitionDist         	0
fHsimMtElab                        	0
fHsimDiskOpt                       	1
fHsimMasterModuleOnSlave           	0
fHsimDoMdbOptOnSlave               	0
fHsimPromoteIsles                  	0
fHsimPromoteThreshold              	128
fHsimElabQOpt                      	0
fHsimElabModThreshold              	0
fHsimOneInstThreshold              	0
fHsimProcsPerClient                	1
fHsimProcsInServer                 	1
fHsimIBIFIncr                      	0
fHsimCompactFn                     	0
fHsimElabJ4SDF                     	0
cElabProcs                         	0
hf_fHsimElabJ                      	0
fHsimElabJOpt                      	33554432
fHsimElabJOpt1                     	0
fHsimElabJMMFactor                 	0
fHsimOneInstCap                    	0
fHsimSchedMinput                   	0
fHsimSchedSeqPrim                  	0
fHsimSchedRandom                   	0
fHsimSchedAll                      	0
fHsimSchedSelectFanout             	0
fHsimSchedSelectFanoutDebug        	0
fHsimSchedSelectFanoutRandom       	0
fFgpDynamicReadOn                  	0
fHsCgOptAllUc                      	0
fHsimNoReconvergenceSched0         	0
fHsimXmrRepl                       	0
fHsimSdfXmrReplSupport             	0
fZoix                              	0
fHsimDfuseNewOpt                   	0
fHsimBfuseNewOpt                   	0
fFgpMbme                           	0
fFgpXmrSched                       	0
fHsimClearClkCaps                  	0
fHsimClearFrcCaps                  	0
fFgpHideXmrNodes                   	0
fHsimDiagClkConfig                 	0
fHsimDiagClkConfigDebug            	0
fHsimDiagClkConfigDumpAll          	0
fHsDiagClkConfigPara               	0
fHsimDiagClkConfigAn               	0
fHsimCanDumpClkConfig              	0
fFgpInitRout                       	0
fFgpIgnoreExclSD                   	0
fHsimAggrTCOpt                     	0
fFgpNewAggrXmrIterFlow             	0
fFgpNoLocalReferer                 	0
fHsCgOptNoClockFusing              	0
fHsCgOptNcfNoDelay                 	1
fHsFgpSchedCgUcLoads               	1
fHsimAdvanceUdpInfer               	1
fFgpIbnSchedIntf                   	0
fFgpUnsafeCompile                  	0
fHsCgOptNewSelCheck                	1
fFgpReportUnsafeFuncs              	0
fFgpReportSafeFuncs                	0
fHsCgOptUncPrlThreshold            	4
fHsimCosimGatesProp                	0
fHsimMxCcnOff                      	0
fHsSVNettypePerfOpt                	0
fHsCgOptHashFixMap                 	1
fFsimNativeFaultFlow               	0
fFsimSerialFaultFlow               	0
fFsimCompiled                      	0
fFsimOptCapsFlow                   	0
fFsimUserEnabledForceCaps          	1
fEnableForceSelectorOptimization   	0
fFsimPortFaults                    	0
fFsimPortFaultsOpt                 	0
fFsimPortFaultsCellOpt             	0
fFsimNoPortFaultsCellOpt           	0
fFsimPerfMode                      	0
fFsimIddqMode                      	0
fFsimSdfFlow                       	0
fHsimLowPowerRetAnalysisInChild    	0
fHsimNettypeResFnOverwrite         	0
fSupplyFusion                      	0
fHsimForceWireSupply               	1
fHsimNlpHierUpfFlow                	0
fSupplyFusionElabPartCell          	0
fFindCheckerIsOn                   	0
fFgpUvmLockLib                     	0
fFgpUvmLockPcb                     	0
fHsCgOptVec                        	1
fHsCgOptNewElab                    	0
fHsCgOptNewElabRand                	0
fRetainWithDelayedSig              	0
fFgpUvm                            	0
fFgpNoUvm                          	0
fHsimSchedViObn                    	0
fFgpUvmTesting                     	0
fUvmEventProf                      	0
fFgpUvmPushButton                  	0
fHsimSchedObnInVcs                 	1
fHsimChargeDecay                   	0
fFgpUseAltEgLayout                 	0
fFgpAltEgLayoutTestError           	0
fHsimNoOneDriverOneLoad            	0
fHsimTestInstNum64                 	0
fHsimInstNum64                     	0
fHsimPreInlineInstNum64            	0
fHsimAutoInstNum64Limit            	-7
fHsimAutoEnableInstNum64PC         	0
fHsCgOptFixLeaf                    	1
fHsimDumpVars                      	0
fFgpTbReactiveLite                 	1
fHsimCcnUnion                      	0
fHsimOnlyElabMod                   	0
fHsimNoCompressElabNodeMap         	0
fHsimNoDfsInstParentMap            	0
fHsimNoLevelize                    	0
fHsimIncrNodeGateAlloc             	0
fHsimCompressGates                 	0
fHsCgOptIbn2Obn                    	0
fHsCgOptCongLite                   	1
fHsimGateDiag                      	0
hf_fHsimInstNum64                  	0
fHsimPeMemDiag                     	0
fSkipMpTsUpdateOnStr               	0
fHsimCbkPropFix                    	0
fHsimStructReg2Wire                	0
fHsCgOptNoHPCB                     	1
fHsCgOptUncondOpt                  	1
fDSLFlow                           	0
fHsimMcpDynClk                     	0
fHsimEmdOpt                        	0
fHsCgOptSelFix                     	1
fHsCgOptUcArr                      	0
fHsCgOptUcArrVec                   	0
fHsCgOptUcArrVecDiag               	0
fHsimHsoptClkDiv                   	0
fHsimHsoptClkDivLimit              	4
fHsimHsoptClkDivDiag               	0
fDiagPrettyPrint                   	1
fHsDiagDirSet                      	1
fDiagDirCreated                    	0
VFS_XCMP                           	0
VFS_XCMP_HSIM                      	1
VFS_XCMP_ELABMODDB                 	1
fHsimHsoptClkDivLevel              	0
fHsimHybridArray                   	0
fHsimMapNumHybridArray             	0
fHsimDumpCompressedSdb             	1
fgpElabConfFileName                	(null)
fFgpElabConf                       	0
fAvoidCustomFnc                    	NULL
fHsimCustomLibMakeFile             	NULL
fHsimUdpDisableReachability        	0
fHsimDbsOffsetsMemOpt              	1
fHsCgOptClkDefuse                  	0
fHsCgOptClkDefuseRand              	0
fHsCgOptClkDefuseAll               	0
fHsCgOptClkDefuseNoAdjust          	1
fHsCgOptDynElab                    	0
fHsCgOptClkDefuseThres             	4
fHsimStitchConvReps                	0
fFgpGateEdgeLimit                  	0
fHsimDbsOffsetsMemOptDiag          	0
fHsimCompressLevelDB               	0
fHubbleAnnotations                 	0
fHsimDisableUnusedSeq              	0
fHsimScanOptNoFwdProp              	1
fHsimCcnDepositFix                 	0
fHsimTwoLevelGateMap               	0
fHsimTwoLevelGateMapDiag           	0
fHsimTwoLevelGateMapThreshold      	0
fFgpIexcsdFix                      	1
fHsCgOptMinNodes                   	-1
fHsCgOptMaxNodes                   	-1
fFgpEdgeSanity                     	0
fHsimMdbUnidirIunidir              	0
fHsimMdbUnidirJunidir              	-1
fHsimMdbUnidirSelectsForDceDebug   	0
fRtLongFaninMHID                   	0
fHsCgOptMuxClkIsSel                	0
fHsCgOptMuxClkIsSelDiag            	0
fFgpBidirDelayOpt                  	0
fFgpWbg                            	0
fHsimObfuscate                     	0
fHsimElabPartForcedPort            	0
fHsimElabPartWopt                  	0
fHsimIncrCompNormalizeData         	3
fHsimCompressRaceDiag              	1
fHsimNoDumpPrintScanPathData       	0
fHsimRmvRmaIbfIpOfBehavIBN         	0
fHsimCompressElabMapForVpd         	0
fHsimVectorgatesCa                 	0
fHsimVcaWithFgp                    	1
fHsimScanOptDiag                   	0
fHsimInitOrder                     	0
fFgpFatEventThreshold              	500
fJaguarGroupMergeClkPath           	128
fFgpWbgDiag                        	0
fFgpBes                            	0
fFgpBesThreshold                   	500
fJaguarGroupMerge                  	128
fJaguarGroupMergeDbg               	0
fHsimChangePathOldImpl             	0
fFgpWbgEblkCountPercent            	0
fFgpWbgNoWeightForGroupSplit       	0
fFgpWbgHsoptUnitWeight             	1
fFgpWbgUseQuadSize                 	0
fFgpJaguarDynBalance               	0
fForceCmPort                       	1
fHsimElabNodesDiag                 	0
fFgpRtWtBasedGrp                   	0
fFgpWbgLogNormalize                	0
fHsimPulseFilterMos                	0
fHsimEnableMITOpt                  	0
fHsimDynamicVcd                    	0
fHsimDynamicVcdCtDiag              	0
fHsimDynamicVcdRtDiag              	0
fHsPropMux2GG                      	0
fHsComputeSavedEvents              	1
fHsPropMux2GGDiag                  	0
fHsMarkSlowSigConstForce           	0
fHsMarkSlowSigConstForceDiag       	0
fHsimUpdateMasterAfterFusing       	1
fBufferZeroDelayUna                	0
fHsimElabJEMBaseWork               	0
fFgpSlowSignalHandle               	0
fFgpSlowSignalDiag                 	0
fFgpSlowSignalRegress              	0
fFgpSmartLevel                     	0
fFgpSmartLevelDiag                 	0
fJaguar                            	0
fJaguarFuseAfterPartitioning       	0
fJaguarVecSplit                    	0
fJaguarClkPart                     	0
fJaguarClkPartTest                 	0
fJaguarIbnSchedLayout              	0
fHsSimonGatesSeen                  	0
fFgpSchedOpts                      	0
fHsimRelaxSched0ForSimonGates      	0
cMaxEblkNumForSplit                	2000
fPassiveDiag                       	0
fPassiveRewrite                    	0
fSharedBuffer                      	0
fJaguarTbParallelToFgpChild        	0
fJaguarTbParallelToFgpMaster       	0
fJaguarTb                          	0
fHsimWaveform                      	0
fHsimDFuseZPortDelay               	0
fFgpSmartLevelAsc                  	0
fFgpSmartLevelDesc                 	0
fFgpSmartLevelNone                 	0
fFgpClkLoadBucketSize              	0
fFgpSafeLibCalls                   	0
fGblMP64                           	0
fPCSkipVICA                        	0
fPCSkipVICADiag                    	0
fHsCgOptGCLoadRemoveAfterEval      	0
fHsCgOptNewSizeHeur                	0
fHsCgOptProp3State                 	0
fHsCgOptNoZConChk                  	0
fHsCgOptNcfReg                     	0
fHsimXMRCbkCodePatch               	0
fFgpClockLoadBucketConfig          	0
fJaguarGateNumaLayout              	0
fHsimItfPropCodePatch              	0
fHsimModByModSerializeMdb          	0
fHsimPropCodePatchDiag             	0
fHsimSerializeLoader               	0
fHsimSerializeLoaderCpage          	50
fHsimRtvsFlowType                  	0
fHsimRtvsNewDr                     	1
fHsimDefRtvsViewHasSpice           	0
fLicUseAarchOnly                   	0
fHsimUpgradeWarnToErrForICPSD_W    	0
fHsimUpgradeWarnToErrForICPD_PCA_W 	0
fHsimUpgradeWarnToErrForICPD_W     	0
fFgpJaguarLevelization             	0
fFgpLoopSplitLevel                 	0
fFgpClkNbaRtDiag                   	0
fFgpCapoptSeen                     	0
fFgpCapoptVpdDiag                  	0
fFgpCapOptSize                     	1000000000
fFgpSchedSelectGateOpt             	0
fHsimDelInstDiag                   	0
fHsimDelIpMap                      	0
fHsMxFuse                          	0
fHsimStartBcPtrMap                 	0
fHsimStartBcPtrMapDiag             	0
fHsimRhsSelectLoadLoop             	0
fHsimCorrectDelayPatternInit       	0
fHsimAllocElabInstOnDemand         	0
fFgpNumMaxGrp                      	0
fFgpPqSize                         	0
fFgpMaxGrpWeight                   	0
cJaguarClkFOLim                    	1000000
cJaguarMaxClkGrpSize               	200
fFgpJaguarEvNbas                   	0
fHsimJaguarDbgFlow                 	0
fHsimDumpUdpOffsets                	0
cJaguarFOLim                       	1000000
cJaguarLSLim                       	4
fHsCgOptDefuseFix                  	0
fTsanOptionSeen                    	0
fSimProfileOptionSeen              	0
fProfOptionSeen                    	0
hsoptDiagDirPath                   	diag_dir/
fHsimCongruencyConfigFile          	0
fHsimCongruencyLogFile             	0
fHsimCoverageEnabled               	0
fHsimCoverageOptions               	0
fHsimCoverageDir                   	NULL
