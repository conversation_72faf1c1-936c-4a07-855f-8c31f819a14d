fHsimDesignHasDebugNodes           	31
fNSParam                           	128
fLargeSizeSdfTest                  	0
fHsimDelayGateMbme                 	0
fNoMergeDelays                     	0
fHsimAllMtmPat                     	0
fSharedMasterElab                  	0
fFsimDutPathEnabled                	0
fFsimEVCDEnabled                   	0
fStimDutCount                      	0
fFsimStrobeIdCount                 	0
fCcExSummaryReportFlag             	0
fCcExConnReport                    	0
fCcExConnDbReport                  	0
fCcExConfigFileFlag                	0
fFsimForcelist                     	0
fSvNettRealResType                 	0
fFgpSched0User                     	0
fFgpDynelabFix                     	1
hsimLevelizeDone                   	1
fHsimCompressDiag                  	1
fHsimPowerOpt                      	0
fLoopReportElab                    	0
fHsimRtl                           	0
fHsimCbkOptVec                     	1
fHsimDynamicCcnHeur                	1
fHsimOldLdr                        	0
fHsimSingleDB                      	1
uVfsGcLimit                        	50
fHsimCompatSched                   	0
fHsimCompatOrder                   	0
fHsimTransUsingdoMpd32             	1
fHsimDynamicElabForGates           	1
fHsimCompressMdbForDynamicElab     	1
fHsimCompressMdbDiag               	0
fHsimDeferForceSelTillReElab       	0
fHsimModByModElab                  	1
fHsimExprID                        	1
fHsimSequdpon                      	0
fHsimDatapinOpt                    	0
fHsimUdpInitDump                   	0
fHsimExprPrune                     	0
fHsimMimoGate                      	0
fHsimNewChangeCheckFrankch         	1
fHsimNoSched0Front                 	0
fHsimNoSched0FrontForMd            	1
fHsimScalReg                       	0
fHsimNtbVl                         	0
fHsimICTimeStamp                   	0
fHsimICTagVA                       	0
fHsimICTagCCN                      	0
fHsimICDelayCCN                    	0
fHsimICDiag                        	0
fHsimMsidSched                     	0
fHsimNewCSDF                       	1
vcselabIncrMode                    	2
fHsimMPPackDelay                   	0
fHsimMultDriver                    	0
fHsimPart                          	0
fHsimPrlComp                       	0
fHsimPartTest                      	0
fHsimTestChangeCheck               	0
fHsimTestFlatNodeOrder             	0
fHsimTestNState                    	0
fHsimPartDebug                     	0
fHsimPartFlags                     	0
fHsimOdeSched0                     	0
fHsimNewRootSig                    	1
fHsimDisableRootSigModeOpt         	0
fHsimTestRootSigModeOpt            	0
fHsimIncrWriteOnce                 	0
fHsimUnifInterfaceFlow             	1
fHsimUnifInterfaceFlowDiag         	0
fHsimUnifInterfaceFlowXmrDiag      	0
fHsimUnifInterfaceMultiDrvChk      	1
fHsimTfMultiDrvChkPCDiag           	0
fHsimXVirForGenerateScope          	0
fHsimCongruencyIntTestI            	0
fHsimCongruencySVA                 	0
fHsimCongruencySVADbg              	0
fHsimCongruencyLatchEdgeFix        	0
fHsimCongruencyFlopEdgeFix         	0
fHsimCongruencyXprop               	0
fHsimCongruencyXpropFix            	0
fHsimCongruencyXpropDbsEdge        	0
fHsimCongruencyResetRecoveryDbs    	0
fHsimCongruencyClockControlDiag    	0
fHsimCongruencySampleUpdate        	0
fHsimCongruencyFFDbsFix            	0
fHsimCongruency                    	0
fHsimJaguarCongruency              	0
fHsimJaguarCongruencyCG            	0
fHsimJaguarCongruencyGrping        	0
fHsimCongruencySlave               	0
fHsimCongruencyCombinedLoads       	0
fHsimCongruencyFGP                 	0
fHsimDeraceClockDataUdp            	0
fHsimDeraceClockDataLERUpdate      	0
fHsimCongruencyPC                  	0
fHsimCongruencyPCInl               	0
fHsimCongruencyPCDbg               	0
fHsimCongruencyPCNoReuse           	0
fHsimCongruencyDumpHier            	0
fHsimCongruencyResolution          	0
fHsimCongruencyEveBus              	0
fHsimHcExpr                        	1
fHsCgOptModOpt                     	0
fHsCgOptSlowProp                   	0
fHsimCcnOpt                        	1
fHsimCcnOpt2                       	1
fHsimCcnOpt3                       	1
fHsimOneTranCcn                    	0
fHsimSmdMap                        	0
fHsimSmdDiag                       	0
fHsimSmdSimProf                    	0
fHsimEvtSimProf                    	0
fHsimSgdDiag                       	0
fHsimRtDiagLite                    	0
fHsimRtDiagLiteCevent              	100
fHsimRtDiag                        	0
fHsimSkRtDiag                      	0
fHsimDDBSRtdiag                    	0
fHsimDbg                           	0
fHsimCompWithGates                 	0
fHsimMdbDebugOpt                   	0
fHsimMdbDebugOptP1                 	0
fHsimMdbDebugOptP2                 	0
fHsimMdbPruneOpt                   	1
fHsimMdbMemOpt                     	0
hsimRandValue                      	0
fHsimSimMemProfile                 	0
fHsimSimTimeProfile                	0
fHsimElabMemProfile                	0
fHsimElabTimeProfile               	0
fHsimElabMemNodesProfile           	0
fHsimElabMemAllNodesProfile        	0
fHsimDisableVpdGatesProfile        	0
fHsimFileProfile                   	0
fHsimCountProfile                  	0
fHsimXmrDefault                    	1
fHsimFuseWireAndReg                	0
fHsimFuseSelfDrvLogic              	0
fHsimFuseProcess                   	0
fHsimNoStitchDump                  	0
fHsimAllExtXmrs                    	0
fHsimAllExtXmrsDiag                	0
fHsimAllExtXmrsAllowClkFusing      	0
fHsimAllXmrs                       	1
fHsimMvsimDb                       	0
fHsimTaskFuncXmrs                  	0
fHsimTaskFuncXmrsDbg               	0
fHsimAllTaskFuncXmrs               	0
fHsimPageArray                     	262143
fHsimPageControls                  	262143
hsDfsNodePageElems                 	0
hsNodePageElems                    	0
hsFlatNodePageElems                	0
hsGateMapPageElems                 	0
hsGateOffsetPageElems              	0
hsGateInputOffsetPageElems         	0
hsDbsOffsetPageElems               	0
hsMinPulseWidthPageElems           	0
hsNodeUpPatternPageElems           	0
hsNodeDownPatternPageElems         	0
hsNodeUpOffsetPageElems            	0
hsNodeEblkOffsetPageElems          	0
hsNodeDownOffsetPageElems          	0
hsNodeUpdateOffsetPageElems        	0
hsSdfOffsetPageElems               	0
hsElabInstancesPageElems           	0
hsDelIparrPageElems                	0
hsIparrPageElems                   	0
fHsimPageAllLevelData              	0
fHsimAggrCg                        	0
fHsimViWire                        	1
fHsimPcCbOpt                       	1
fHsimAmsTunneling                  	0
fHsimAmsTunnelingDiag              	0
fHsimScUpwardXmrNoSplit            	1
fHsimOrigNdbViewOnly               	0
fHsimVcsInterface                  	1
fHsimVcsInterfaceAlias             	1
fHsimSVTypesIntf                   	1
fUnifiedAssertCtrlDiag             	0
fHsimEnable2StateScal              	0
fHsimDisable2StateScalIbn          	0
fHsimVcsInterfaceAliasDbg          	0
fHsimVcsInterfaceDbg               	0
fHsimVcsVirtIntfDbg                	0
fHsimInitOpenPort                  	1
fHsimVcsAllIntfVarMem              	0
fHsimCheckVIDynLoadOffsets         	0
fHsimModInline                     	1
fHsimModInlineDbg                  	0
fHsimPCDrvLoadDbg                  	0
fHsimDrvChk                        	1
fHsimRtlProcessingNeeded           	0
fHsimGrpByGrpElab                  	0
fHsimGrpByGrpElabMaster            	0
fHsimNoParentSplitPC               	0
fHsimNusymMode                     	0
fHsimOneIntfPart                   	0
fHsimCompressInSingleDb            	2
fHsimCompressFlatDb                	0
fHsimNoTime0Sched                  	1
fHsimMdbSplitGates                 	0
fHsimDeleteInstances               	0
fHsimUserDeleteInstances           	0
fHsimDeleteGdb                     	0
fHsimMdbDeleteGutsEarly            	0
fHsimDelxmdbDiag                   	0
fHsimDeleteInstancesMdb            	0
fHsimVectorConstProp               	0
fHsStopSlowSigPropForMI            	1
fHsimPromoteParam                  	0
fHsimNoVecInRaptor                 	0
fHsimNewEvcd                       	0
fHsimNewEvcdMX                     	0
fHsimNewEvcdTest                   	0
fHsimNewEvcdObnDrv                 	1
fHsimNewEvcdW                      	0
fHsimNewEvcdWTest                  	0
fHsimEvcdDbgFlags                  	0
fHsimNewEvcdMultiDrvFmt            	1
fHsimRelPath                       	0
fHsimDumpOffsetData                	1
fFlopGlitchDetect                  	0
fHsimClkGlitch                     	0
fHsimGlitchDumpOnce                	0
fHsimDynamicElab                   	1
fHsimCgVectors2Debug               	0
fHsimUdpTetramax                   	0
fHsimLerGate                       	0
fHsimGateGroup                     	0
fHsimGateGroupDiag                 	0
fHsimConstForce                    	0
fHsimElabNodeWidthCheck            	0
fHsimOdeDynElab                    	0
fHsimOdeDynElabDiag                	0
fHsimOdeSeqUdp                     	0
fHsimOdeSeqUdpXEdge                	0
fHsimOdeSeqUdpDbg                  	0
fHsimOdeRmvSched0                  	0
fHsimAllLevelSame                  	0
fHsimRtlDbsList                    	0
fHsimPePort                        	0
fHsimPrunePePort                   	0
fHsimPrunePePortNodes              	0
fHsimPeRtReadMultiStub             	0
fHsimPeIntfXmr                     	0
fHsimPeXmrDiag                     	0
fHsimPePropDiag                    	0
fHsimPeXmr                         	0
fHsimPePortDiag                    	0
fHsimPeDiag                        	0
fHsimPeMode                        	0
fHsimPeLevelize                    	0
fHsimPeFgpLevelize                 	0
fHsimDbgPeLevelize                 	0
fHsimPeFixedXmr                    	1
fHsimPePcFixScopeXmr               	1
fHsimPePcScopeXmrDiag              	0
fHsimUdpDbs                        	0
fHsimRemoveDbgCaps                 	0
fFsdbGateOnepassTraverse           	0
fVpdDeltaCycleInDesign             	0
fHsimAllowVecGateInVpd             	1
fHsimAllowAllVecGateInVpd          	0
fHsimAllowUdpInVpd                 	1
fHsimAllowAlwaysCombInVpd          	1
fHsimAllowAlwaysCombCmpDvcSimv     	0
fHsimAllowAlwaysCombDbg            	0
fHsimMakeAllP2SPrimary             	0
fHsimMakeAllSeqPrimary             	0
fHsimFsdbProfDiag                  	0
fHsimAllCapSet                     	1
fVIDriverPartComp                  	0
fPCAllNodesVIDriver                	0
fVpdSeqGate                        	0
fVpdUseMaxBCode                    	1
fFsdbUseBCodeLimit                 	0
fVpdHsIntVecGate                   	1
fVpdHsCmplxVecGate                 	1
fVpdHsVecGateDiags                 	0
fSeqGateCodePatch                  	0
fVpdLongFaninOpt                   	0
fVpdSeqLongFaninOpt                	0
fVpdNoLoopDetect                   	0
fVpdNoSeqLoopDetect                	0
fVpdOptAllowConstDriver            	0
fVpdAllowCellReconstruction        	1
fVpdRtlForSharedLib                	0
fFsdbRejectedDiag                  	0
fFsdbRejectedDiagExtra             	0
fVpdRtlForPartComp                 	1
fVpdRtlForPartCompDiag             	0
fHsimVpdOptGate                    	1
fHsimVpdOptDelay                   	0
fHsimVpdOptMPDelay                 	0
fHsimCbkOptDiag                    	0
fHsimSK                            	0
fHsimSKInteg                       	0
fHsimSKCtOnly                      	0
fHsimSharedKernel                  	0
fHsimPcSdfLocal                    	0
fHsimPeLocalXmr                    	0
fHsimPeUpXmrDiag                   	0
fHsimOnepass                       	0
fHsimStitchNew                     	0
fHsimGlobalLevelize                	0
fHsimParallelLevelize              	0
fHsimParallelLevelizeDbg           	0
fHsimSeqUdpDbsByteArray            	0
fHsimCoLocate                      	0
fHsimBepats                        	0
fHsimBepatsInfra                   	0
fHsimFepats                        	0
fSv2Cpp                            	0
fHsimSeqUdpEblkOpt                 	0
fHsimSeqUdpEblkPcOpt               	0
fHsimSeqUdpEblkOptDiag             	0
fHsimGateInputAndDbsOffsetsOpt     	1
fHsimMasterNodeSort                	3
fHsimGDBLargeModule                	1
fHsimUdpDynElab                    	0
fHsimCompressData                  	4
fHsimIgnoreZForDfuse               	1
fHsimFuseSameDriversIgnoreReElab   	0
fHsimIgnoreDifferentCaps           	0
fHandleGlitchQC                    	1
fGlitchDetectForAllRtlLoads        	0
fHsimFuseConstDriversOpt           	1
fHsimMdSchedTr                     	0
fHsimIgnoreReElab                  	0
fHsimFuseMultiDrivers              	0
fHsimNoSched0Reg                   	0
fHsimAmsFusionEnabled              	0
fHsimElab64                        	0
fHsimRtlDbs                        	0
fHsimWakeupId                      	0
fHsimPassiveIbn                    	0
fHsimBcOpt                         	1
fHsimTranDelay                     	0
fHsimDelayToCCN                    	0
fHsimCertitude                     	0
fHsimRaceDetect                    	0
fHsimEventOrderDiag                	0
fHsimEODCommandLineOption          	0
fHsimSRaceDiag                     	0
fHsimSRaceCommandLineOption        	0
fCheckTcCond                       	0
fHsimMcpFlags                      	0
fHsimMcpAdjFactor                  	6
fHsimMcpDumpChunk                  	4096
fHsimPcPeMode                      	0
fHsimScanOptRelaxDbg               	0
fHsimScanOptRelaxDbgDynamic        	0
fHsimScanOptRelaxDbgDynamicPli     	0
fHsimScanOptRelaxDbgDiag           	0
fHsimScanOptRelaxDbgDiagHi         	0
fHsimScanOptNoErrorOnPliAccess     	0
fHsimScanOptNoFusedSwitchTurnOn    	1
fHsimScanOptTiming                 	0
fRelaxIbnSchedCheck                	2
fHsimScanOptNoDumpCombo            	0
fHsimScanOptPrintSwitchState       	0
fHsimScanOptSelectiveSwitchOn      	1
fHsimScanOptSingleSEPliOpt         	1
fHsimScanOptDesignHasDebugAccessOnly	0
fHsimScanOptPrintPcode             	0
fHsimScanOptRelaxDbgDefaultOn      	0
fHsimScanDbgPerf                   	0
fHsimFuseConsts                    	0
fHsimNoStitchMap                   	0
fHsimUnifiedModName                	0
fHsimAssertInActive                	0
fHsimCbkMemOptDebug                	0
fHsimMasterModuleOnly              	0
fHsimCompactCode                   	0
fHsimUpHierIC                      	0
fHsimDelayOpt                      	0
fHsimTran2MosIgnoreReadCaps        	0
fHsimMdbOptimizeSelects            	0
fHsimMdbScalarizePorts             	0
fHsimMdbOptimizeSelectsHeuristic   	1
fHsimMdb1006Partition              	0
fHsimVectorPgate                   	0
fHsimNoHs                          	0
fHsimXmrPartition                  	0
fHsimNewPartition                  	0
fHsimElabPart                      	0
fHsimElabPartDiag                  	0
fHsimPMdb                          	0
fHsimElabPartThreshHoldDesign      	1
fHsimParitionCellInstNum           	1000
fHsimParitionCellNodeNum           	1000
fHsimParitionCellXMRNum            	1000
fHsimNewPartCutSingleInstLimit     	268435455
fHsimElabModDistNum                	0
fHsimElabPartThreshHoldModule      	3000000
fHsimElabPartMaxDepth              	0
fHsimElabPartExternalLimitHeur     	10
fHsimElabPartPercentLimit          	0
fHsimElabPartEnableOneInst         	0
fHsimNewPartitionHeur              	0
fHsimElabPartForcedportMaxDep      	0
fHsimElabPartMinHeight             	0
fHsimRtPartitionTable              	3
fHsimPCPortPartition               	0
fHsimPortPartition                 	0
fHsimDumpMdb                       	0
fHsimElabDiag                      	0
fHsimSimpCollect                   	0
fHsimPcodeDiag                     	0
fHsimFastelab                      	0
fHsimMacroOpt                      	0
fHsimSkipOpt                       	0
fHsimSkipOptFanoutlimit            	0
fHsimSkipOptRootlimit              	0
fHsimFuseDelayChains               	0
fFusempchainsFanoutlimit           	0
fFusempchainsDiagCount             	0
fHsimCgVectorGates                 	0
fHsimCgVectorGates1                	0
fHsimCgVectorGates2                	0
fHsimCgVectorGatesNoReElab         	0
fHsimCgScalarGates                 	0
fHsimCgScalarGatesExpr             	0
fHsimCgScalarGatesLut              	0
fHsimCgRtl                         	1
fHsimCgRtlFilter                   	0
fHsimCgRtlDebug                    	0
fHsimCgRtlSize                     	15
fHsimNewCgRt                       	0
fHsimNewCgMPRt                     	0
fHsimNewCgMPRetain                 	0
fHsimCgRtlInfra                    	1
fHsimGlueOpt                       	0
fHsimPGatePatchOpt                 	0
fHsimCgNoPic                       	0
fHsimElabModCg                     	0
fPossibleNullChecks                	0
fHsimProcessNoSplit                	1
fHsimMdbOptInSchedDelta            	0
fScaleTimeValue                    	0
fDebugTimeScale                    	0
fPartCompSDF                       	0
fHsimNbaGate                       	1
fHsimVarDelay                      	0
fDumpDtviInfoInSC                  	0
fDtviDiag                          	0
fFusedNodeInfo                     	1
fDumpSDFBasedMod                   	1
fHsimSdfIC                         	0
fOptimisticNtcSolver               	0
fHsimAllMtm                        	0
fHsimAllMtmPat                     	0
fHsimSdgOptEnable                  	0
fHsimSVTypesRefPorts               	0
fHsimGrpByGrpElabIncr              	0
fHsimMarkRefereeInVcsElab          	0
fHsimStreamOpFix                   	1
fHsimInterface                     	0
fHsimMxWrapOpt                     	0
fHsimMxTopBdryOpt                  	0
fHsimClasses                       	0
fHsimAggressiveDce                 	0
fHsimDceDebug                      	1
fHsimDceDebugDB                    	0
fHsimDceDebugSC                    	0
fHsimDceDebugUseHeuristics         	1
fHsimDceDebugVec                   	0
fHsimDceDebugDiag                  	0
fHsimDceDebugFanoutThreshold       	10000000
fHsimMdbNewDebugOpt                	0
fHsimMdbNewDebugOptExitOnError     	1
fHsimNewDebugOptMemDiag            	0
hsGlobalVerboseLevel               	0
fHsimTestPxpcode                   	0
fHsimMdbVectorConstProp            	1
fHsimEnableSeqUdpWrite             	1
fHsimDisableSeqUdpWriteForce       	0
fHsimDumpMDBOnlyForSeqUdp          	0
fHsimInitRegRandom                 	0
fHsimInitRegRandomVcs              	1
fRegTypesToWire                    	0
fEnableNewFinalStrHash             	0
fEnableNewAssert                   	1
fRunDbgDmma                        	0
fAssrtCtrlSigChk                   	1
fCheckSigValidity                  	0
fUniqPriToAstRewrite               	0
fUniqPriToAstCtrl                  	0
fAssertcontrolUniqPriNewImpl       	0
fRTLoopDectEna                     	0
fCmplLoopDectEna                   	0
fHsimMopFlow                       	1
fUCaseLabelCtrl                    	0
fUniSolRtSvaEna                    	1
fUniSolSvaEna                      	1
fXpropRtCtrlCallerOnly             	0
fXpropVhVlCombineDisabled          	0
fHsimEnableDbsMemOpt               	1
fHsimDebugDbsMemOpt                	0
fHsimRenPart                       	0
cHsimNonReplicatedInstances        	0
fHsimXmrAllWires                   	0
fHsimXmrDiag                       	0
fHsimXmrPort                       	0
fHsimFalcon                        	1
fHsimGenForProfile                 	0
fCompressSDF                       	0
fHsimGates1209                     	0
fHsimCgRtlNoShareSmd               	0
fHsimGenForErSum                   	0
fVpdOpt                            	1
fHsimMdbCell                       	0
fHsimCellDebug                     	0
fHsimMdbCellFuse                   	0
fHsimMdbCellForDce                 	0
fHsimMdbCellForDceDebug            	0
fHsimMdbCellDiag                   	0
fHsimMdbCellEgSched0               	0
fHsimMdbCellVpdIbif                	0
fHsimMdbCellElabJ                  	0
fHsimMdbCellHashForRTEncode        	0
fHsimNoPeekInMdbCell               	0
igetOpcodeSmdPtrLayoutId           	-1
igetFieldSmdPtr                    	-1
fDebugDump                         	1
fHsimOrigNodeNames                 	0
fHsimCgVectors2VOnly               	0
fHsimMdbDeltaGate                  	0
fHsimMdbDeltaGateAggr              	0
fHsimMdbVecDeltaGate               	0
fHsimVpdOptVfsDB                   	1
fHsimMdbPruneVpdGates              	1
fHsimDynamicEblk                   	0
fHsimDynamicEblkDiag               	0
fHsimCompactCompiledLoads          	0
fHsimPcPe                          	0
fHsimVpdGateOnlyFlag               	1
fHsimMxConnFrc                     	0
fHsimNewForceCbkVec                	0
fHsimNewForceCbkVecDiag            	0
fHsimMdbReplaceVpdHighConn         	1
fHsimVpdOptSVTypes                 	1
fHsHasPeUpXmr                      	0
fHsimPIP                           	0
fHsimRTLoopDectOrgName             	0
fHsimVpdOptPC                      	0
fHsimFusePeXmrFo                   	0
fHsimXmrSched                      	0
fHsimNoMdg                         	0
fHsimVectorGates                   	0
fHsimMdbcgLut                      	0
fHsimMdbcgSelective                	0
fHsimMdbcgLevelize                 	0
fHsimParGateEvalMode               	0
fHsimDFuseVectors                  	0
fHsimDFuseZero                     	0
fHsimDFuseOpt                      	1
fHsimPruneOpt                      	0
fHsimSeqUdpPruneWithConstInputs    	0
fHsimSafeDFuse                     	0
fHsimVpdOptSelGate                 	1
fHsimVpdOptSkipFuncPorts           	0
fHsimVpdOptAlways                  	1
fHsimVpdOptMdbCell                 	0
fHsimVpdOptPartialMdb              	0
fHsimVpdOptPartitionGate           	1
fHsimVpdOptPartitionGateCompress   	1
fHsimVpdOptXmr                     	1
fHsimVpdOptMoreLevels              	1
fHsimSWave                         	0
fHsimNoSched0InCell                	1
fHsimPartialMdb                    	0
hsimPdbLargeOffsetThreshold        	1048576
fHsimFlatCell                      	0
fHsimFlatCellLimit                 	0
fHsimRegBank                       	0
fHsimHmetisMaxPartSize             	0
fHsimHmetisGateWt                  	0
fHsimHmetisUbFactor                	0
fHsimHmetis                        	0
fHsimHmetisDiag                    	0
fHsimRenumGatesForMdbCell          	0
fHsimHmetisMinPart                 	0
fHsim2stCell                       	0
fHsim2stCellMinSize                	0
fHsimMdbcgDebug                    	0
fHsimMdbcgDebugLite                	0
fHsimMdbcgSepmem                   	1
fHsimMdbcgObjDiag                  	0
fHsimMdbcg2stDiag                  	0
fHsimMdbcgRttrace                  	0
fHsimMdbVectorGateGroup            	1
fHsimMdbProcDfuse                  	1
fHsimMdbHilPrune                   	0
fHsCgOpt                           	1
fHsCgOptUdp                        	1
fHsCgOptRtl                        	1
fCgOptTmask                        	5
fHsCgOptDiag                       	0
fHsCgOptNoZCheck                   	0
fHsCgOptEnableZSupport             	1
fHsCgOptDce                        	1
fHsCgOpt4StateInfra                	1
fHsCgOptMultiDriverSupport         	0
fHsCgOptUdpChkDataForWakeup        	1
fHsCgOptXprop                      	0
fHsimMdbcgDiag                     	0
fHsCgMaxInputs                     	6
fHsCgOptFwdPass                    	1
fHsimHpnodes                       	0
fLightDump                         	0
fVpdBeforeScan                     	1
fHsCgOptMiSched0                   	0
fgcAddSched0                       	0
fParamClassOptRtDiag               	0
fHsRegress                         	0
fHsBenchmark                       	0
fHsimCgScalarVerilogForce          	1
fVcsElabToRoot                     	1
fHilIbnObnCallByName               	0
fHsimMdbcgCellPartition            	0
fHsimCompressVpdSig                	0
fHsimLowPowerOpt                   	0
fHsimUdpOpt                        	1
fHsimUdpOptNew                     	0
fHsVecOneld                        	0
fNativeVpdDebug                    	0
fNewDtviFuse                       	1
fHsimVcsGenTLS                     	1
fAssertSuccDebugLevelDump          	0
fHsimMinputsChangeCheck            	0
fHsimClkLayout                     	0
fHsimSortLayout                    	0
fHsimIslandLayout                  	0
fHsimConfigSched0                  	0
fHsimSelectFuseAfterDfuse          	0
fHsimFoldedCell                    	0
fHsimSWaveEmul                     	0
fHsimSWaveDumpMDB                  	0
fHsimSWaveDumpFlatData             	0
fHsimRenumberAlias                 	0
fHsimAliasRenumbered               	0
fHilCgMode                         	115
fHsimUnionOpt                      	0
fHsimFuseSGDBoundaryNodes          	0
fHsimRemoveCapsVec                 	0
fHsCgOptMux                        	0
fHsCgOptFrc                        	0
fHsCgOpt30                         	0
fHsLpNoCapsOpt                     	0
fHsCgOpt4State                     	1
fSkipStrChangeOnDelay              	1
fHsimTcheckOpt                     	0
fHsCgOptMuxMClk                    	0
fHsCgOptMuxFrc                     	0
fHsCgOptNoPcb                      	0
fHsCgOptMin1                       	0
fHsCgOptUdpChk                     	1
fHsChkXForSlowSigProp              	1
fHsimVcsParallelDbg                	0
fHsimVcsParallelStrategy           	0
fHsimVcsParallelOpt                	0
fHsimVcsParallelSubLevel           	4
fHsimParallelEblk                  	0
fHsimByteCodeParts                 	1
fFgpNovlInComp                     	0
fFutEventPRL                       	0
fFgpNbaDelay                       	0
fHsimDbsFlagsByteArray             	0
fHsimDbsFlagsByteArrayTC           	0
fHsimDbsFlagsThreadArray           	0
fHsimGateEdgeEventSched            	0
fHsimMaxComboLevels                	0
fHsimEgschedDynelab                	0
fHsimUdpClkDynelab                 	0
fUdpLayoutOnClk                    	0
fHsimDiagClk                       	1
fDbsPreCheck                       	0
fHsimSched0Analysis                	0
fHsimMultiDriverSched0             	0
fHsimLargeIbnSched                 	0
fFgpHierarchical                   	0
fFgpHierAllElabModAsRoot           	0
fFgpHierPCElabModAsRoot            	0
fFgpHierMarkBoundariesAsExclusive  	0
fFgpHierRootFromConfig             	0
fFgpHierRandRoot                   	0
fFgpHierThreshold                  	10
fFgpAdjustDataLevelOfLatch         	1
fHsimUdpXedgeEval                  	0
fFgpRaceCheck                      	0
fFgpUnifyClk                       	0
fFgpSmallClkTree                   	0
fFgpSmallRtlClkTree                	4
fFgpNoRtlUnlink                    	0
fFgpNoRtlAuxLevel                  	0
fFgpNumPartitions                  	8
fFgpMultiSocketCompile             	0
fFgpDataDepOn                      	0
fFgpDDIgnore                       	0
fFgpDDVecSplit                     	0
fFgpDDDebug                        	0
fFgpTbCbOn                         	0
fFgpTbEvOn                         	1
fFgpTbNoVSA                        	0
fFgpTbEvXmr                        	0
fFgpTbEvCgCall                     	1
fFgpDisabledLevel                  	512
fFgpNoSdDelayedNbas                	1
fFgpNoEvNbas                       	0
fFgpTimingFlags                    	0
fFgpSched0Level                    	0
fHsimFgpMultiClock                 	0
fFgpScanOptFix                     	0
fFgpSched0UdpData                  	0
fFgpLoadBalance0CompileTime        	1
fFgpDepositDiag                    	0
fFgpEvtDiag.diagOn                 	0
fFgpEvtDiag.printAllNodes          	0
fFgpMangleDiagLog                  	0
fFgpMultiExclDiag                  	0
fFgpSingleExclReason               	0
fHsDoFaninFanoutSanity             	0
fHsFgpNonDbsOva                    	1
fFgpParallelTask                   	1
fFgpCondTcGateedge                 	0
fFgpIbnSched                       	0
fFgpIbnSchedOpt                    	0
fFgpIbnSchedThreshold              	0
fFgpIbnSchedDyn                    	0
fFgpMpStateByte                    	0
fFgpTcStateByte                    	0
fHsimVirtIntfDynLoadSched          	0
fFgpNoRtimeFgp                     	0
fHsFgpGlSched0                     	0
fFgpExclReason                     	0
fHsimIslandByIslandElab            	0
fHsimIslandByIslandFlat            	153749568
fHsimIslandByIslandFlat1           	4
fHsimVpdIBIF                       	0
fHsimXmrIBIF                       	1
fHsimReportTime                    	0
fHsimIncrElab                      	0
fHsimElabJ                         	0
fHsimQuantumMachine                	0
fHsimNoDoMdbOptOnSlave             	0
fHsimElabQ                         	0
fHsimFileOpenElabJQ                	0
fHsimMtElab                        	0
fHsimDiskOpt                       	1
fHsimElabQOpt                      	0
fHsimElabModThreshold              	0
fHsimOneInstThreshold              	0
fHsimProcsPerClient                	1
fHsimProcsInServer                 	1
fHsimIBIFIncr                      	0
fHsimCompactFn                     	0
hf_fHsimElabJ                      	0
fHsimElabJOpt                      	33554432
fHsimElabJOpt1                     	0
fHsimSchedMinput                   	0
fHsimSchedSeqPrim                  	0
fHsimSchedSelectFanout             	0
fHsimSchedSelectFanoutDebug        	0
fSpecifyInDesign                   	0
fFgpDynamicReadOn                  	0
fHsCgOptAllUc                      	0
fHsimXmrRepl                       	0
fHsimSdfXmrReplSupport             	0
fZoix                              	0
fHsimDfuseNewOpt                   	0
fHsimBfuseNewOpt                   	0
fFgpXmrSched                       	0
fHsimClearClkCaps                  	0
fHsimClearFrcCaps                  	0
fHsimDiagClkConfig                 	0
fHsimDiagClkConfigDebug            	0
fHsimDiagClkConfigDumpAll          	0
fHsDiagClkConfigPara               	0
fHsimDiagClkConfigAn               	0
fHsimCanDumpClkConfig              	0
fFgpInitRout                       	0
fFgpIgnoreExclSD                   	0
fHsCgOptNoClockFusing              	0
fHsimPCSharedLibSpecified          	0
fHsFgpSchedCgUcLoads               	1
fHsCgOptNewSelCheck                	1
fFgpReportUnsafeFuncs              	0
fFgpReportSafeFuncs                	0
fHsCgOptUncPrlThreshold            	4
fHsSVNettypePerfOpt                	0
fFsimNativeFaultFlow               	0
fFsimSerialFaultFlow               	0
fFsimCompiled                      	0
fFsimOptCapsFlow                   	0
fFsimUserEnabledForceCaps          	1
fEnableForceSelectorOptimization   	0
fFsimPortFaults                    	0
fFsimPortFaultsOpt                 	0
fFsimPortFaultsCellOpt             	0
fFsimNoPortFaultsCellOpt           	0
fFsimPerfMode                      	0
fFsimIddqMode                      	0
fFsimSdfFlow                       	0
fHsimLowPowerRetAnalysisInChild    	0
fHsimForceWireSupply               	1
fFindCheckerIsOn                   	0
fHsCgOptVec                        	1
fHsCgOptNewElab                    	0
fHsCgOptNewElabRand                	0
fRetainWithDelayedSig              	0
fFgpUvm                            	0
fFgpUvmTesting                     	0
fUvmEventProf                      	0
fFgpUvmPushButton                  	0
fHsimChargeDecay                   	0
fFgpUseAltEgLayout                 	0
fHsimTestInstNum64                 	0
fHsimInstNum64                     	0
fHsimPreInlineInstNum64            	0
fHsimAutoInstNum64Limit            	-7
fHsimAutoEnableInstNum64PC         	0
fHsCgOptFixLeaf                    	1
fHsimDumpVars                      	0
fFgpTbReactiveLite                 	1
fHsCgOptCongLite                   	1
fHsimGateDiag                      	0
hf_fHsimInstNum64                  	0
fHsimPeMemDiag                     	0
fSkipMpTsUpdateOnStr               	0
fHsimCbkPropFix                    	0
fHsimStructReg2Wire                	0
fHsCgOptNoHPCB                     	1
fHsCgOptSelFix                     	1
fHsCgOptUcArr                      	0
fHsCgOptUcArrVec                   	0
fHsCgOptUcArrVecDiag               	0
fDiagPrettyPrint                   	1
fHsDiagDirSet                      	1
fDiagDirCreated                    	0
VFS_XCMP                           	0
VFS_XCMP_HSIM                      	1
VFS_XCMP_ELABMODDB                 	1
fHsimHybridArray                   	0
fHsimMapNumHybridArray             	0
fHsimDumpCompressedSdb             	1
fHsimUdpDisableReachability        	0
fHsimDbsOffsetsMemOpt              	1
fHsCgOptDynElab                    	0
fHsimDbsOffsetsMemOptDiag          	0
fHsimCompressLevelDB               	0
fHubbleAnnotations                 	0
fHsimScanOptNoFwdProp              	1
fHsimCcnDepositFix                 	0
fHsimTwoLevelGateMap               	0
fHsimTwoLevelGateMapDiag           	0
fHsimTwoLevelGateMapThreshold      	0
fHsimMdbUnidirSelectsForDceDebug   	0
fRtLongFaninMHID                   	0
fHsCgOptMuxClkIsSel                	0
fHsCgOptMuxClkIsSelDiag            	0
fHsimObfuscate                     	0
fHsimElabPartForcedPort            	0
fHsimElabPartWopt                  	0
fHsimIncrCompNormalizeData         	3
fHsimCompressRaceDiag              	1
fHsimNoDumpPrintScanPathData       	0
fHsimRmvRmaIbfIpOfBehavIBN         	0
fHsimCompressElabMapForVpd         	0
fHsimVectorgatesCa                 	0
fHsimVcaWithFgp                    	1
fHsimInitOrder                     	0
fHsimChangePathOldImpl             	0
fFgpJaguarDynBalance               	0
fForceCmPort                       	1
fHsimElabNodesDiag                 	0
fFgpRtWtBasedGrp                   	0
fHsimDynamicVcd                    	0
fHsimDynamicVcdCtDiag              	0
fHsimDynamicVcdRtDiag              	0
fHsPropMux2GG                      	0
fHsComputeSavedEvents              	1
fHsPropMux2GGDiag                  	0
fHsMarkSlowSigConstForce           	0
fHsMarkSlowSigConstForceDiag       	0
fHsimUpdateMasterAfterFusing       	1
fBufferZeroDelayUna                	0
fJaguar                            	0
fJaguarFuseAfterPartitioning       	0
fJaguarVecSplit                    	0
fJaguarClkPart                     	0
fJaguarClkPartTest                 	0
fJaguarIbnSchedLayout              	0
fHsSimonGatesSeen                  	0
fHsimRelaxSched0ForSimonGates      	0
fPassiveRewrite                    	0
fJaguarTbParallelToFgpChild        	0
fJaguarTbParallelToFgpMaster       	0
fJaguarTb                          	0
fHsimWaveform                      	0
fHsimDFuseZPortDelay               	0
fFgpClkLoadBucketSize              	0
fFgpSafeLibCalls                   	0
fHsCgOptGCLoadRemoveAfterEval      	0
fHsCgOptNewSizeHeur                	0
fHsCgOptProp3State                 	0
fHsCgOptNoZConChk                  	0
fHsCgOptNcfReg                     	0
fHsimXMRCbkCodePatch               	0
fFgpClockLoadBucketConfig          	0
fJaguarGateNumaLayout              	0
fHsimItfPropCodePatch              	0
fHsimPropCodePatchDiag             	0
fHsimSerializeLoader               	0
fHsimSerializeLoaderCpage          	50
fHsimRtvsFlowType                  	0
fHsimRtvsNewDr                     	1
fHsimDefRtvsViewHasSpice           	0
fLicUseAarchOnly                   	0
fFgpJaguarLevelization             	0
fFgpClkNbaRtDiag                   	0
fFgpCapoptSeen                     	0
fFgpCapoptVpdDiag                  	0
fFgpCapOptSize                     	1000000000
fFgpSchedSelectGateOpt             	0
fHsimDelInstDiag                   	0
fHsimDelIpMap                      	0
fHsMxFuse                          	0
fHsimStartBcPtrMap                 	0
fHsimStartBcPtrMapDiag             	0
fHsimRhsSelectLoadLoop             	0
fHsimCorrectDelayPatternInit       	0
fHsimAllocElabInstOnDemand         	0
fFgpNumMaxGrp                      	0
fFgpPqSize                         	0
fFgpMaxGrpWeight                   	0
fFgpJaguarEvNbas                   	0
fHsimJaguarDbgFlow                 	0
fTsanOptionSeen                    	0
fSimProfileOptionSeen              	0
fProfOptionSeen                    	0
hsoptDiagDirPath                   	diag_dir/
