0
47
+define+DEBUG_BFM
+define+DUMP_FSDB
+incdir+../../../../src
+incdir+../../../nonblocking/src/sv
+itf+/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcsdp_lite.tab
+vcsd1
+vpi
+vpi
-<PERSON><PERSON>run=
-Masflags=
-Mcc=gcc
-Mcfl= -pipe -fPIC -g -I../../../nonblocking/src/cpp -I/opt/synopsys/vcs/V-2023.12-SP2/include
-Mcplusplus=g++
-Mcrt0=
-Mcrtn=
-Mcsrc=
-Mexternalobj=
-Mldflags= -rdynamic
-Mobjects= /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvirsim.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/liberrorinf.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libsnpsmalloc.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvfs.so
-Mout=simv
-Msaverestoreobj=/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_save_restore_new.o
-Msyslibs=/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/pli.a -ldl
-Mvcsaceobjs=
-Mxcflags= -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include
-Mxllcflags=
-P
-Xufe=2steps
-debug_access+all
-fsdb
-full64
-gen_obj
-kdb
-l
-lca
-o simv
-picarchive
-sverilog
-timescale=1ns/1ps
-top
/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/vcs1
vcs_comp.log
tb_sideband_out
simv
/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/verdi.tab
../../../../src/sideband_out_bfm.sv
../../../../src/fifo.sv
../../../nonblocking/src/sv/tb_sideband_out.sv
193
https_proxy=http://127.0.0.1:7890
http_proxy=http://127.0.0.1:7890
_ZO_DATA_DIR=/home/<USER>/.config/zoxide
ZSH_HOME=/home/<USER>/zsh
ZLIB_HOME=/home/<USER>/zlib
ZEROMQ_HOME=/home/<USER>/zeromq
YODL_HOME=/home/<USER>/yodl
XSEL_HOME=/home/<USER>/xsel
XMODIFIERS=@im=ibus
XDG_VTNR=1
XDG_SESSION_TYPE=x11
XDG_SESSION_ID=1
XDG_SESSION_DESKTOP=gnome-classic
XDG_SEAT=seat0
XDG_RUNTIME_DIR=/run/user/1000
XDG_MENU_PREFIX=gnome-
XDG_DATA_DIRS=/home/<USER>/.local/share/flatpak/exports/share/:/var/lib/flatpak/exports/share/:/usr/local/share/:/usr/share/
XDG_CURRENT_DESKTOP=GNOME-Classic:GNOME
XCLIP_HOME=/home/<USER>/xclip
XAUTHORITY=/run/gdm/auth-for-harriszh-idrDvM/database
WINDOWPATH=1
VTE_VERSION=5204
VMR_MODE_FLAG=64
VIM_HOME=/home/<USER>/vim
VERIBLE_HOME=/home/<USER>/verible
VERDI_LIB=/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64
VERDI_HOME=/opt/synopsys/verdi/V-2023.12-SP2
VCS_PYTHON3=/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/Python-3.6.1/bin/python3
VCS_PATHMAP_PRELOAD_DONE=1
VCS_MX_HOME_INTERNAL=1
VCS_MODE_FLAG=64
VCS_LOG_FILE=vcs_comp.log
VCS_LCAMSG_PRINT_OFF=1
VCS_HOME=/opt/synopsys/vcs/V-2023.12-SP2
VCS_EXEC_DONE=1
VCS_DEPTH=0
VCS_ARG_ADDED_FOR_TMP=1
VCS_ARCH=linux64
USERNAME=harriszh
UNAME=/bin/uname
TOOL_HOME=/opt/synopsys/vcs/V-2023.12-SP2/linux64
TMUX_HOME=/home/<USER>/tmux
TCL_HOME=/home/<USER>/tcl
SYSTEMC_HOME=/home/<USER>/systemc
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SSH_AGENT_PID=2883
SPDLOG_HOME=/home/<USER>/spdlog
SOURCE_ZSH=1
SOURCE_ZLIB=1
SOURCE_ZEROMQ=1
SOURCE_YODL=1
SOURCE_XSEL=1
SOURCE_XCLIP=1
SOURCE_VIM=1
SOURCE_VERIBLE=1
SOURCE_TMUX=1
SOURCE_TCL=1
SOURCE_SYSTEMC=1
SOURCE_SPDLOG=1
SOURCE_READLINE=1
SOURCE_PYTHON=1
SOURCE_PCRE2=1
SOURCE_OPENSSL=1
SOURCE_ONIGURUMA=1
SOURCE_NODE=1
SOURCE_MAKE=1
SOURCE_M4=1
SOURCE_LUA=1
SOURCE_LLVM=1
SOURCE_LIBTOOL=1
SOURCE_LIBSOCKET=1
SOURCE_LIBPSL=1
SOURCE_LIBICONV=1
SOURCE_LIBEVENT=1
SOURCE_LIBB2=1
SOURCE_IVERILOG=1
SOURCE_IMLIB2=1
SOURCE_GTEST=1
SOURCE_GRAPHVIZ=1
SOURCE_GPERF=1
SOURCE_GO=1
SOURCE_GLOBAL=1
SOURCE_GIT=1
SOURCE_GIFLIB=1
SOURCE_GETTEXT=1
SOURCE_FLATBUFFERS=1
SOURCE_FEH=1
SOURCE_DTC=1
SOURCE_DROPBEAR=1
SOURCE_DOXYGEN=1
SOURCE_CURL=1
SOURCE_CTAGS=1
SOURCE_CMAKE=1
SOURCE_BITWISE=1
SOURCE_BEAR=1
SOURCE_AUTOMAKE=1
SOURCE_AUTOCONF=1
SOURCE_ABSEIL=1
SNPS_VERDI_INTERNAL_LP_XML_NEW_FLOW=1
SNPS_VCS_PYTHON3=/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/Python-3.6.1/bin/python3
SNPS_VCS_CLANG_PATH=/opt/synopsys/vcs/V-2023.12-SP2/linux64/clang
SNPS_INTERNAL_VCS_LINUX_OS=linux
SESSION_MANAGER=local/unix:@/tmp/.ICE-unix/2746,unix/unix:/tmp/.ICE-unix/2746
SCRNAME=vcs
SCRIPT_NAME=vcs
RIPGREP_CONFIG_PATH=/home/<USER>/.ripgreprc
READLINE_HOME=/home/<USER>/readline
QT_IM_MODULE=ibus
PYTHON_HOME=/home/<USER>/python
PUBLIC_TOOLS=/home/<USER>
PKG_CONFIG_PATH=/opt/rh/devtoolset-11/root/usr/lib64/pkgconfig:/home/<USER>/zlib/lib/pkgconfig:/home/<USER>/zeromq/lib/pkgconfig:/home/<USER>/zeromq/lib64/pkgconfig:/home/<USER>/tcl/lib/pkgconfig:/home/<USER>/systemc/lib/pkgconfig:/home/<USER>/spdlog/lib64/pkgconfig:/home/<USER>/readline/lib/pkgconfig:/home/<USER>/python/lib/pkgconfig:/home/<USER>/pcre2/lib64/pkgconfig:/home/<USER>/openssl/lib64/pkgconfig:/home/<USER>/oniguruma/lib/pkgconfig:/home/<USER>/libsocket/lib/pkgconfig:/home/<USER>/libpsl/lib/pkgconfig:/home/<USER>/libiconv/lib/pkgconfig:/home/<USER>/libevent/lib64/pkgconfig:/home/<USER>/libb2/lib/pkgconfig:/home/<USER>/imlib2/lib/pkgconfig:/home/<USER>/gtest/lib64/pkgconfig:/home/<USER>/graphviz/lib/pkgconfig:/home/<USER>/flatbuffers/lib64/pkgconfig:/home/<USER>/curl/lib64/pkgconfig:/home/<USER>/abseil/lib64/pkgconfig:/home/<USER>/.local/lib64/pkgconfig:/home/<USER>/.local/lib/pkgconfig:/home/<USER>/.local/vte/lib64/pkgconfig:
PCRE2_HOME=/home/<USER>/pcre2
PCP_DIR=/opt/rh/devtoolset-11/root
OVA_UUM=0
OPENSSL_HOME=/home/<USER>/openssl
ONIGURUMA_HOME=/home/<USER>/oniguruma
NO_PLATFORM_REL_CHECK=1
NODE_HOME=/home/<USER>/node
MFLAGS=
MANPAGER=sh -c 'col -bx | bat -l man -p --paging always'
MAKE_TERMOUT=/dev/pts/6
MAKE_TERMERR=/dev/pts/6
MAKE_HOME=/home/<USER>/make
MAKELEVEL=1
MAKEFLAGS=
M4_HOME=/home/<USER>/m4
LUA_HOME=/home/<USER>/lua
LLVM_HOME=/home/<USER>/llvm
LIBTOOL_HOME=/home/<USER>/libtool
LIBSOCKET_HOME=/home/<USER>/libsocket
LIBPSL_HOME=/home/<USER>/libpsl
LIBICONV_HOME=/home/<USER>/libiconv
LIBEVENT_HOME=/home/<USER>/libevent
LIBB2_HOME=/home/<USER>/libb2
LESS_TERMCAP_us=[1;32m
LESS_TERMCAP_ue=[0m
LESS_TERMCAP_me=[0m
LESS_TERMCAP_md=[1;31m
LESS_TERMCAP_mb=[1;31m
LESSOPEN=|/home/<USER>/local/bin/batpipe %s
LESS=--chop-long-lines --ignore-case --jump-target=4 --LONG-PROMPT --no-init --quit-if-one-screen --RAW-CONTROL-CHARS -R
LC_ALL=en_US.UTF-8
IVERILOG_HOME=/home/<USER>/iverilog
IMSETTINGS_MODULE=none
IMSETTINGS_INTEGRATE_DESKTOP=yes
IMLIB2_HOME=/home/<USER>/imlib2
GTEST_HOME=/home/<USER>/gtest
GREP_COLORS=mt=37;45
GREP_COLOR=37;45
GRAPHVIZ_HOME=/home/<USER>/graphviz
GPERF_HOME=/home/<USER>/gperf
GO_HOME=/home/<USER>/go
GNOME_TERMINAL_SERVICE=:1.112
GNOME_TERMINAL_SCREEN=/org/gnome/Terminal/screen/806a719e_8871_42e0_8c9b_d686a19754e0
GNOME_SHELL_SESSION_MODE=classic
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GLOBAL_HOME=/home/<USER>/global
GIT_HOME=/home/<USER>/git
GIT_EXTRAS_HOME=/home/<USER>/git-extras
GIT_EDITOR=vim
GIFLIB_HOME=/home/<USER>/giflib
GETTEXT_HOME=/home/<USER>/gettext
GDM_LANG=en_US.UTF-8
GDMSESSION=gnome-classic
FZF_DEFAULT_OPTS=_  --bind='?:toggle-preview'_  --bind='ctrl-u:preview-page-up'_  --bind='ctrl-d:preview-page-down'_  --bind 'ctrl-/:change-preview-window(80%,border-bottom|hidden|)' _  --bind 'ctrl-r:reload(fd --type f --follow --exclude .git --exclude \*.swp)'_  --bind 'ctrl-i:reload(fd --type f -I --follow --exclude .git --exclude \*.swp)'_  --bind 'ctrl-h:reload(fd --type f -I -H  --follow --exclude .git --exclude \*.swp)'_  --bind 'ctrl-o:execute(vim {})'_  --bind 'ctrl-w:reload(/home/<USER>/.cargo/bin/zoxide query --list)'_  --preview-window 'right:60%:wrap'_  --preview '/home/<USER>/.sh/fzf-bat.sh {}'
FZF_DEFAULT_COMMAND=fd --type f --follow --exclude .git --exclude \*.swp
FZF_DEFAULT_COMMAND3=fd --type f -I -H  --follow --exclude .git --exclude \*.swp
FZF_DEFAULT_COMMAND2=fd --type f -I --follow --exclude .git --exclude \*.swp
FZF_CTRL_T_COMMAND=fd --type f --type d --hidden --follow --exclude .git --exclude \*.swp
FZF_ALT_C_COMMAND=fd --type d --hidden --follow --exclude .git --exclude \*.swp
FLATBUFFERS_HOME=/home/<USER>/flatbuffers
FEH_HOME=/home/<USER>/feh
DTC_HOME=/home/<USER>/dtc
DROPBEAR_HOME=/home/<USER>/dropbear
DOXYGEN_HOME=/home/<USER>/doxygen
DESKTOP_SESSION=gnome-classic
DBUS_SESSION_BUS_ADDRESS=unix:abstract=/tmp/dbus-jrCY1UuLJL,guid=47f535f9f7a5101a64d402b06877357b
CURL_HOME=/home/<USER>/curl
CTAGS_HOME=/home/<USER>/ctags
COLORTERM=24bit
CMAKE_HOME=/home/<USER>/cmake
CDS_LIC_FILE=5280@127.0.0.1
CARGO_HOME=/home/<USER>/.cargo
BITWISE_HOME=/home/<USER>/bitwise
BITMODE=64
BEAR_HOME=/home/<USER>/bear
BAT_THEME=gruvbox-dark
BAT_STYLE=full
BATPIPE=color
AUTOMAKE_HOME=/home/<USER>/automake
AUTOCONF_HOME=/home/<USER>/autoconf
ACLOCAL_PATH=/home/<USER>/libtool/share/aclocal:/usr/share/aclocal
ABSEIL_HOME=/home/<USER>/abseil
0
0
5
1753024463 ../../../nonblocking/src/sv/tb_sideband_out.sv
1753019333 ../../../../src/fifo.sv
1753022252 ../../../../src/sideband_out_bfm.sv
1716718493 /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/verdi.tab
1717384209 /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcsdp_lite.tab
4
1717385583 /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvirsim.so
1717384763 /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/liberrorinf.so
1717384706 /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libsnpsmalloc.so
1717384758 /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvfs.so
1753024484 simv.daidir
-1 partitionlib
