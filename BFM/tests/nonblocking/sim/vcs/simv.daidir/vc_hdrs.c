#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif
#include <stdio.h>
#include <dlfcn.h>
#include "svdpi.h"

#ifdef __cplusplus
extern "C" {
#endif

/* VCS error reporting routine */
extern void vcsMsgReport1(const char *, const char *, int, void *, void*, const char *);

#ifndef _VC_TYPES_
#define _VC_TYPES_
/* common definitions shared with DirectC.h */

typedef unsigned int U;
typedef unsigned char UB;
typedef unsigned char scalar;
typedef struct { U c; U d;} vec32;

#define scalar_0 0
#define scalar_1 1
#define scalar_z 2
#define scalar_x 3

extern long long int ConvUP2LLI(U* a);
extern void ConvLLI2UP(long long int a1, U* a2);
extern long long int GetLLIresult();
extern void StoreLLIresult(const unsigned int* data);
typedef struct VeriC_Descriptor *vc_handle;

#ifndef SV_3_COMPATIBILITY
#define SV_STRING const char*
#else
#define SV_STRING char*
#endif

#endif /* _VC_TYPES_ */

#ifndef __VCS_IMPORT_DPI_STUB_h2s_sideband_init
#define __VCS_IMPORT_DPI_STUB_h2s_sideband_init
__attribute__((weak)) void h2s_sideband_init(/* INPUT */int A_1, /* INPUT */int A_2)
{
    static int _vcs_dpi_stub_initialized_ = 0;
    static void (*_vcs_dpi_fp_)(/* INPUT */int A_1, /* INPUT */int A_2) = NULL;
    if (!_vcs_dpi_stub_initialized_) {
        _vcs_dpi_fp_ = (void (*)(int A_1, int A_2)) dlsym(RTLD_NEXT, "h2s_sideband_init");
        _vcs_dpi_stub_initialized_ = 1;
    }
    if (_vcs_dpi_fp_) {
        _vcs_dpi_fp_(A_1, A_2);
    } else {
        const char *fileName;
        int lineNumber;
        svGetCallerInfo(&fileName, &lineNumber);
        vcsMsgReport1("DPI-DIFNF", fileName, lineNumber, 0, 0, "h2s_sideband_init");
    }
}
#endif /* __VCS_IMPORT_DPI_STUB_h2s_sideband_init */

#ifndef __VCS_IMPORT_DPI_STUB_h2s_sideband_notify_change_nb
#define __VCS_IMPORT_DPI_STUB_h2s_sideband_notify_change_nb
__attribute__((weak)) void h2s_sideband_notify_change_nb(/* INPUT */int A_1, /* INPUT */int A_2, /* INPUT */unsigned char A_3)
{
    static int _vcs_dpi_stub_initialized_ = 0;
    static void (*_vcs_dpi_fp_)(/* INPUT */int A_1, /* INPUT */int A_2, /* INPUT */unsigned char A_3) = NULL;
    if (!_vcs_dpi_stub_initialized_) {
        _vcs_dpi_fp_ = (void (*)(int A_1, int A_2, unsigned char A_3)) dlsym(RTLD_NEXT, "h2s_sideband_notify_change_nb");
        _vcs_dpi_stub_initialized_ = 1;
    }
    if (_vcs_dpi_fp_) {
        _vcs_dpi_fp_(A_1, A_2, A_3);
    } else {
        const char *fileName;
        int lineNumber;
        svGetCallerInfo(&fileName, &lineNumber);
        vcsMsgReport1("DPI-DIFNF", fileName, lineNumber, 0, 0, "h2s_sideband_notify_change_nb");
    }
}
#endif /* __VCS_IMPORT_DPI_STUB_h2s_sideband_notify_change_nb */

#ifndef __VCS_IMPORT_DPI_STUB_h2s_sideband_notify_change_b
#define __VCS_IMPORT_DPI_STUB_h2s_sideband_notify_change_b
__attribute__((weak)) void h2s_sideband_notify_change_b(/* INPUT */int A_1, /* INPUT */int A_2, /* INPUT */unsigned char A_3)
{
    static int _vcs_dpi_stub_initialized_ = 0;
    static void (*_vcs_dpi_fp_)(/* INPUT */int A_1, /* INPUT */int A_2, /* INPUT */unsigned char A_3) = NULL;
    if (!_vcs_dpi_stub_initialized_) {
        _vcs_dpi_fp_ = (void (*)(int A_1, int A_2, unsigned char A_3)) dlsym(RTLD_NEXT, "h2s_sideband_notify_change_b");
        _vcs_dpi_stub_initialized_ = 1;
    }
    if (_vcs_dpi_fp_) {
        _vcs_dpi_fp_(A_1, A_2, A_3);
    } else {
        const char *fileName;
        int lineNumber;
        svGetCallerInfo(&fileName, &lineNumber);
        vcsMsgReport1("DPI-DIFNF", fileName, lineNumber, 0, 0, "h2s_sideband_notify_change_b");
    }
}
#endif /* __VCS_IMPORT_DPI_STUB_h2s_sideband_notify_change_b */

#ifndef __VCS_EXPORT_DPI_DUMMY_REFERENCES__
#define __VCS_EXPORT_DPI_DUMMY_REFERENCES__
/* Dummy references to those export DPI routines.
 * The symbols will be then exported, so the
 * import DPI routines in another shared
 * libraries can call.
 */
void __vcs_export_dpi_dummy_references__();
void __vcs_export_dpi_dummy_references__()
{
    extern void s2h_set_mode(void);
    void (*fp0)(void) = (void (*)(void)) s2h_set_mode;
    fp0 = fp0;
    extern void s2h_set_delay(void);
    void (*fp1)(void) = (void (*)(void)) s2h_set_delay;
    fp1 = fp1;
}
#endif /* __VCS_EXPORT_DPI_DUMMY_REFERENCES_ */

#ifdef __cplusplus
}
#endif

