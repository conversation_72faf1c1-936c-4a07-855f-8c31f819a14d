#!/bin/sh -f

cd "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs"
export ABSEIL_HOME="/home/<USER>/abseil"
export ACLOCAL_PATH="/home/<USER>/libtool/share/aclocal:/usr/share/aclocal"
export AUTOCONF_HOME="/home/<USER>/autoconf"
export AUTOMAKE_HOME="/home/<USER>/automake"
export BATPIPE="color"
export BAT_STYLE="full"
export BAT_THEME="gruvbox-dark"
export BEAR_HOME="/home/<USER>/bear"
export BITMODE="64"
export BITWISE_HOME="/home/<USER>/bitwise"
export BROWSER="/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/helpers/browser.sh"
export CARGO_HOME="/home/<USER>/.cargo"
export CDS_LIC_FILE="5280@127.0.0.1"
export CMAKE_HOME="/home/<USER>/cmake"
export COLORTERM="24bit"
export CTAGS_HOME="/home/<USER>/ctags"
export CURL_HOME="/home/<USER>/curl"
export DISABLE_LIBRARY_MAP_CHECK="1"
export DOXYGEN_HOME="/home/<USER>/doxygen"
export DROPBEAR_HOME="/home/<USER>/dropbear"
export DTC_HOME="/home/<USER>/dtc"
export EDITOR="vim"
export FEH_HOME="/home/<USER>/feh"
export FLATBUFFERS_HOME="/home/<USER>/flatbuffers"
export FLEXLM_BORROWFILE="/home/<USER>/.pc-harriszh2-borrow.txt"
export FZF_ALT_C_COMMAND="fd --type d --hidden --follow --exclude .git --exclude \\*.swp"
export FZF_CTRL_T_COMMAND="fd --type f --type d --hidden --follow --exclude .git --exclude \\*.swp"
export FZF_DEFAULT_COMMAND="fd --type f --follow --exclude .git --exclude \\*.swp"
export FZF_DEFAULT_COMMAND2="fd --type f -I --follow --exclude .git --exclude \\*.swp"
export FZF_DEFAULT_COMMAND3="fd --type f -I -H  --follow --exclude .git --exclude \\*.swp"
export FZF_DEFAULT_OPTS="
  --bind='?:toggle-preview'
  --bind='ctrl-u:preview-page-up'
  --bind='ctrl-d:preview-page-down'
  --bind 'ctrl-/:change-preview-window(80%,border-bottom|hidden|)' 
  --bind 'ctrl-r:reload(fd --type f --follow --exclude .git --exclude \\*.swp)'
  --bind 'ctrl-i:reload(fd --type f -I --follow --exclude .git --exclude \\*.swp)'
  --bind 'ctrl-h:reload(fd --type f -I -H  --follow --exclude .git --exclude \\*.swp)'
  --bind 'ctrl-o:execute(vim {})'
  --bind 'ctrl-w:reload(/home/<USER>/.cargo/bin/zoxide query --list)'
  --preview-window 'right:60%:wrap'
  --preview '/home/<USER>/.sh/fzf-bat.sh {}'"
export GETTEXT_HOME="/home/<USER>/gettext"
export GIFLIB_HOME="/home/<USER>/giflib"
export GIT_ASKPASS="/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/extensions/git/dist/askpass.sh"
export GIT_EDITOR="vim"
export GIT_EXTRAS_HOME="/home/<USER>/git-extras"
export GIT_HOME="/home/<USER>/git"
export GLOBAL_HOME="/home/<USER>/global"
export GO_HOME="/home/<USER>/go"
export GPERF_HOME="/home/<USER>/gperf"
export GRAPHVIZ_HOME="/home/<USER>/graphviz"
export GREP_COLOR="37;45"
export GREP_COLORS="mt=37;45"
export GTEST_HOME="/home/<USER>/gtest"
export HOME="/home/<USER>"
export IMLIB2_HOME="/home/<USER>/imlib2"
export INFOPATH="/opt/rh/devtoolset-9/root/usr/share/info"
export IVERILOG_HOME="/home/<USER>/iverilog"
export LANG="en_US.UTF-8"
export LD_LIBRARY_PATH="/opt/synopsys/vcs/V-2023.12-SP2/lib:/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib:/opt/rh/devtoolset-9/root/usr/lib64:/opt/rh/devtoolset-9/root/usr/lib:/opt/rh/devtoolset-9/root/usr/lib64/dyninst:/opt/rh/devtoolset-9/root/usr/lib/dyninst:/opt/rh/devtoolset-9/root/usr/lib64:/opt/rh/devtoolset-9/root/usr/lib:/home/<USER>/zsh/lib:/home/<USER>/zlib/lib:/home/<USER>/zeromq/lib:/home/<USER>/zeromq/lib64:/home/<USER>/tcl/lib:/home/<USER>/systemc/lib:/home/<USER>/spdlog/lib64:/home/<USER>/readline/lib:/home/<USER>/python/lib:/home/<USER>/pcre2/lib64:/home/<USER>/openssl/lib64:/home/<USER>/oniguruma/lib:/home/<USER>/node/lib:/home/<USER>/lua/lib:/home/<USER>/llvm/lib:/home/<USER>/libtool/lib:/home/<USER>/libsocket/lib:/home/<USER>/libpsl/lib:/home/<USER>/libiconv/lib:/home/<USER>/libevent/lib64:/home/<USER>/libb2/lib:/home/<USER>/iverilog/lib:/home/<USER>/imlib2/lib:/home/<USER>/gtest/lib64:/home/<USER>/graphviz/lib:/home/<USER>/go/lib:/home/<USER>/global/lib:/home/<USER>/giflib/lib:/home/<USER>/gettext/lib:/home/<USER>/flatbuffers/lib64:/home/<USER>/dtc/lib:/home/<USER>/curl/lib64:/home/<USER>/bear/lib64:/home/<USER>/abseil/lib64:/home/<USER>/.local/lib64:/home/<USER>/.local/lib:/usr/local/lib:/lib64:/usr/lib64:/usr/lib:/lib"
export LESS="--chop-long-lines --ignore-case --jump-target=4 --LONG-PROMPT --no-init --quit-if-one-screen --RAW-CONTROL-CHARS -R -R"
export LESSOPEN="|/home/<USER>/local/bin/batpipe %s"
export LESS_TERMCAP_mb="[1;31m"
export LESS_TERMCAP_md="[1;31m"
export LESS_TERMCAP_me="[0m"
export LESS_TERMCAP_ue="[0m"
export LESS_TERMCAP_us="[1;32m"
export LIBB2_HOME="/home/<USER>/libb2"
export LIBEVENT_HOME="/home/<USER>/libevent"
export LIBICONV_HOME="/home/<USER>/libiconv"
export LIBPSL_HOME="/home/<USER>/libpsl"
export LIBSOCKET_HOME="/home/<USER>/libsocket"
export LIBTOOL_HOME="/home/<USER>/libtool"
export LLVM_HOME="/home/<USER>/llvm"
export LM_LICENSE_FILE="5280@127.0.0.1"
export LOGNAME="harriszh"
export LUA_HOME="/home/<USER>/lua"
export M4_HOME="/home/<USER>/m4"
export MAIL="/var/mail/harriszh"
export MAKEFLAGS=" -- TEST_SUITE=nonblocking"
export MAKELEVEL="1"
export MAKEOVERRIDES="\${-*-command-variables-*-}"
export MAKE_HOME="/home/<USER>/make"
export MAKE_TERMERR="/dev/pts/9"
export MAKE_TERMOUT="/dev/pts/9"
export MANPAGER="sh -c 'col -bx | bat -l man -p --paging always'"
export MANPATH="/opt/rh/devtoolset-9/root/usr/share/man:"
export MFLAGS=""
export NODE_HOME="/home/<USER>/node"
export NOVAS_ENABLE_NOBINDDOC_CHECK=""
export NOVAS_LC_ALL="C"
export NOVAS_NO_DUMP_LOGDIR="1"
export NO_COLOR="1"
export NO_PLATFORM_REL_CHECK="1"
export OLDPWD
export ONIGURUMA_HOME="/home/<USER>/oniguruma"
export OPENSSL_HOME="/home/<USER>/openssl"
export OVA_UUM="0"
export PAGER="cat"
export PATH=".:/opt/rh/devtoolset-9/root/usr/bin:/opt/synopsys/vcs/V-2023.12-SP2/bin:/opt/synopsys/verdi/V-2023.12-SP2/bin:/home/<USER>/git-extras/bin:/home/<USER>/zsh/bin:/home/<USER>/zeromq/bin:/home/<USER>/yodl/bin:/home/<USER>/xsel/bin:/home/<USER>/xclip/bin:/home/<USER>/vim/bin:/home/<USER>/verible/bin:/home/<USER>/tmux/bin:/home/<USER>/tcl/bin:/home/<USER>/readline/bin:/home/<USER>/python/bin:/home/<USER>/pcre2/bin:/home/<USER>/openssl/bin:/home/<USER>/oniguruma/bin:/home/<USER>/node/bin:/home/<USER>/make/bin:/home/<USER>/m4/bin:/home/<USER>/lua/bin:/home/<USER>/llvm/bin:/home/<USER>/libtool/bin:/home/<USER>/libpsl/bin:/home/<USER>/libiconv/bin:/home/<USER>/libevent/bin:/home/<USER>/iverilog/bin:/home/<USER>/imlib2/bin:/home/<USER>/graphviz/bin:/home/<USER>/gperf/bin:/home/<USER>/go/bin:/home/<USER>/global/bin:/home/<USER>/git/bin:/home/<USER>/giflib/bin:/home/<USER>/gettext/bin:/home/<USER>/flatbuffers/bin:/home/<USER>/feh/bin:/home/<USER>/dtc/bin:/home/<USER>/dropbear/bin:/home/<USER>/dropbear/sbin:/home/<USER>/doxygen/bin:/home/<USER>/curl/bin:/home/<USER>/ctags/bin:/home/<USER>/cmake/bin:/home/<USER>/bitwise/bin:/home/<USER>/bear/bin:/home/<USER>/automake/bin:/home/<USER>/autoconf/bin:/home/<USER>/local/bin:/home/<USER>/.cargo/bin:/trunk/go/bin:/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/bin/remote-cli:/usr/local/bin:/usr/bin:/home/<USER>/.fzf/bin"
export PCP_DIR="/opt/rh/devtoolset-9/root"
export PCRE2_HOME="/home/<USER>/pcre2"
export PKG_CONFIG_PATH="/opt/rh/devtoolset-9/root/usr/lib64/pkgconfig:/home/<USER>/zlib/lib/pkgconfig:/home/<USER>/zeromq/lib/pkgconfig:/home/<USER>/zeromq/lib64/pkgconfig:/home/<USER>/tcl/lib/pkgconfig:/home/<USER>/systemc/lib/pkgconfig:/home/<USER>/spdlog/lib64/pkgconfig:/home/<USER>/readline/lib/pkgconfig:/home/<USER>/python/lib/pkgconfig:/home/<USER>/pcre2/lib64/pkgconfig:/home/<USER>/openssl/lib64/pkgconfig:/home/<USER>/oniguruma/lib/pkgconfig:/home/<USER>/libsocket/lib/pkgconfig:/home/<USER>/libpsl/lib/pkgconfig:/home/<USER>/libiconv/lib/pkgconfig:/home/<USER>/libevent/lib64/pkgconfig:/home/<USER>/libb2/lib/pkgconfig:/home/<USER>/imlib2/lib/pkgconfig:/home/<USER>/gtest/lib64/pkgconfig:/home/<USER>/graphviz/lib/pkgconfig:/home/<USER>/flatbuffers/lib64/pkgconfig:/home/<USER>/curl/lib64/pkgconfig:/home/<USER>/abseil/lib64/pkgconfig:/home/<USER>/.local/lib64/pkgconfig:/home/<USER>/.local/lib/pkgconfig:/home/<USER>/.local/vte/lib64/pkgconfig:/home/<USER>/zlib/lib/pkgconfig:/home/<USER>/zeromq/lib/pkgconfig:/home/<USER>/zeromq/lib64/pkgconfig:/home/<USER>/tcl/lib/pkgconfig:/home/<USER>/systemc/lib/pkgconfig:/home/<USER>/spdlog/lib64/pkgconfig:/home/<USER>/readline/lib/pkgconfig:/home/<USER>/python/lib/pkgconfig:/home/<USER>/pcre2/lib64/pkgconfig:/home/<USER>/openssl/lib64/pkgconfig:/home/<USER>/oniguruma/lib/pkgconfig:/home/<USER>/libsocket/lib/pkgconfig:/home/<USER>/libpsl/lib/pkgconfig:/home/<USER>/libiconv/lib/pkgconfig:/home/<USER>/libevent/lib64/pkgconfig:/home/<USER>/libb2/lib/pkgconfig:/home/<USER>/imlib2/lib/pkgconfig:/home/<USER>/gtest/lib64/pkgconfig:/home/<USER>/graphviz/lib/pkgconfig:/home/<USER>/flatbuffers/lib64/pkgconfig:/home/<USER>/curl/lib64/pkgconfig:/home/<USER>/abseil/lib64/pkgconfig:/home/<USER>/.local/lib64/pkgconfig:/home/<USER>/.local/lib/pkgconfig:/home/<USER>/.local/vte/lib64/pkgconfig:"
export PROMPT_EOL_MARK=""
export PUBLIC_TOOLS="/home/<USER>"
export PWD="/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs"
export PYTHON_HOME="/home/<USER>/python"
export READLINE_HOME="/home/<USER>/readline"
export RIPGREP_CONFIG_PATH="/home/<USER>/.ripgreprc"
export SCRIPT_NAME="vcs"
export SCRNAME="vcs"
export SHELL="/home/<USER>/zsh/bin/zsh"
export SHLVL="6"
export SNPSLMD_LICENSE_FILE="27000@pc-harriszh2"
export SNPS_INTERNAL_VCS_LINUX_OS="linux"
export SNPS_VCS_CLANG_PATH="/opt/synopsys/vcs/V-2023.12-SP2/linux64/clang"
export SNPS_VCS_INTERNAL_CURR_PID="46291"
export SNPS_VCS_INTERNAL_MAKE="make"
export SNPS_VCS_INTERNAL_MAKE_OPT=""
export SNPS_VCS_INTERNAL_MEMPRELOAD_USE_LIBC="1"
export SNPS_VCS_INTERNAL_ROOT_PID="46291"
export SNPS_VCS_INTERNAL_ROOT_START_TIME="**********.271608156"
export SNPS_VCS_INTERNAL_SR_ASLR_RESTART="1"
export SNPS_VCS_PYTHON3="/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/Python-3.6.1/bin/python3"
export SNPS_VERDI_INTERNAL_LP_XML_NEW_FLOW="1"
export SOURCE_ABSEIL="1"
export SOURCE_AUTOCONF="1"
export SOURCE_AUTOMAKE="1"
export SOURCE_BEAR="1"
export SOURCE_BITWISE="1"
export SOURCE_CMAKE="1"
export SOURCE_CTAGS="1"
export SOURCE_CURL="1"
export SOURCE_DOXYGEN="1"
export SOURCE_DROPBEAR="1"
export SOURCE_DTC="1"
export SOURCE_FEH="1"
export SOURCE_FLATBUFFERS="1"
export SOURCE_GETTEXT="1"
export SOURCE_GIFLIB="1"
export SOURCE_GIT="1"
export SOURCE_GLOBAL="1"
export SOURCE_GO="1"
export SOURCE_GPERF="1"
export SOURCE_GRAPHVIZ="1"
export SOURCE_GTEST="1"
export SOURCE_IMLIB2="1"
export SOURCE_IVERILOG="1"
export SOURCE_LIBB2="1"
export SOURCE_LIBEVENT="1"
export SOURCE_LIBICONV="1"
export SOURCE_LIBPSL="1"
export SOURCE_LIBSOCKET="1"
export SOURCE_LIBTOOL="1"
export SOURCE_LLVM="1"
export SOURCE_LUA="1"
export SOURCE_M4="1"
export SOURCE_MAKE="1"
export SOURCE_NODE="1"
export SOURCE_ONIGURUMA="1"
export SOURCE_OPENSSL="1"
export SOURCE_PCRE2="1"
export SOURCE_PYTHON="1"
export SOURCE_READLINE="1"
export SOURCE_SPDLOG="1"
export SOURCE_SYSTEMC="1"
export SOURCE_TCL="1"
export SOURCE_TMUX="1"
export SOURCE_VERIBLE="1"
export SOURCE_VIM="1"
export SOURCE_XCLIP="1"
export SOURCE_XSEL="1"
export SOURCE_YODL="1"
export SOURCE_ZEROMQ="1"
export SOURCE_ZLIB="1"
export SOURCE_ZSH="1"
export SPDLOG_HOME="/home/<USER>/spdlog"
export SSH_CLIENT="************* 3041 22"
export SSH_CONNECTION="************* 3041 *************32 22"
export SSL_CERT_DIR="/etc/pki/tls/certs"
export SSL_CERT_FILE="/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem"
export SYNOPSYS_SIM="/opt/synopsys/vcs/V-2023.12-SP2"
export SYSTEMC_HOME="/home/<USER>/systemc"
export TCL_HOME="/home/<USER>/tcl"
export TERM="gnome-256color"
export TERM_PROGRAM="vscode"
export TERM_PROGRAM_VERSION="1.98.0"
export TEST_SUITE="nonblocking"
export TMUX_HOME="/home/<USER>/tmux"
export TOOL_HOME="/opt/synopsys/vcs/V-2023.12-SP2/linux64"
export TURBO_LOG_DIR="/tmp/vcs_20250720095104_46291"
export UNAME="/bin/uname"
export USER="harriszh"
export USER_ZDOTDIR="/home/<USER>"
export VCS_ARCH="linux64"
export VCS_ARG_ADDED_FOR_TMP="1"
export VCS_COM="/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/vcs1"
export VCS_DEPTH="0"
export VCS_EXEC_DONE="1"
export VCS_HOME="/opt/synopsys/vcs/V-2023.12-SP2"
export VCS_LCAMSG_PRINT_OFF="1"
export VCS_LOG_FILE="vcs_comp.log"
export VCS_MODE_FLAG="64"
export VCS_MX_HOME_INTERNAL="1"
export VCS_PATHMAP_PRELOAD_DONE="1"
export VCS_PYTHON3="/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/Python-3.6.1/bin/python3"
export VERDI_HOME="/opt/synopsys/verdi/V-2023.12-SP2"
export VERDI_LIB="/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64"
export VERIBLE_HOME="/home/<USER>/verible"
export VIM_HOME="/home/<USER>/vim"
export VMR_MODE_FLAG="64"
export VSCODE_GIT_ASKPASS_EXTRA_ARGS=""
export VSCODE_GIT_ASKPASS_MAIN="/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/extensions/git/dist/askpass-main.js"
export VSCODE_GIT_ASKPASS_NODE="/home/<USER>/.vscode-server/cli/servers/Stable-6609ac3d66f4eade5cf376d1cb76f13985724bcb/server/node"
export VSCODE_GIT_IPC_HANDLE="/run/user/1000/vscode-git-d2adfddec3.sock"
export VSCODE_INJECTION="1"
export VSCODE_IPC_HOOK_CLI="/run/user/1000/vscode-ipc-8eb49bed-4733-4060-aaf3-ca32da566227.sock"
export VTE_VERSION="0"
export XCLIP_HOME="/home/<USER>/xclip"
export XDG_RUNTIME_DIR="/run/user/1000"
export XDG_SESSION_ID="114"
export XSEL_HOME="/home/<USER>/xsel"
export YODL_HOME="/home/<USER>/yodl"
export ZDOTDIR="/home/<USER>"
export ZEROMQ_HOME="/home/<USER>/zeromq"
export ZLIB_HOME="/home/<USER>/zlib"
export ZSH_HOME="/home/<USER>/zsh"
export _="/usr/bin/time"
export _ZO_DATA_DIR="/home/<USER>/.config/zoxide"
export http_proxy="http://127.0.0.1:7890/"
export https_proxy="http://127.0.0.1:7890/"
export sysc_uni_pwd="/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs"
unset VERDI_ADV_COMPILE
if [ -z "${ELABCOM_DIAGTOOL_PREFIX}" ] && [ -z "${ELABCOM_DIAGTOOL_POSTFIX}" ];
then
    unset TURBO_LOG_DIR ;\rm -rf /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/elabcomLog ;/opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/elabcom -logdir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/elabcomLog  -simvdaidir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir  -lib /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/work  -saveLevel  -elab /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb -simflow -simdir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb_libs -topfile /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomTopFile -ssv -ssy  -Lf work +disable_message+C00380+C00381 +disable_message+C00388+C00389  >& /dev/null  && echo Success >& /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomReport || echo Fail >& /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomReport; \rm -rf /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/work.lib++ /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb_libs/ 
else
    unset TURBO_LOG_DIR ;\rm -rf /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/elabcomLog ; ${ELABCOM_DIAGTOOL_PREFIX} /opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/elabcom -logdir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/elabcomLog  -simvdaidir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir  ${ELABCOM_DIAGTOOL_POSTFIX}  -lib /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/work  -saveLevel  -elab /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb -simflow -simdir /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb_libs -topfile /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomTopFile -ssv -ssy  -Lf work +disable_message+C00380+C00381 +disable_message+C00388+C00389 
    status=$?
    if [ "$status" == 0 ];
    then
        echo Success >& /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomReport
    else
        echo Fail >& /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/.elabcomReport
    fi

 \rm -rf /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/work.lib++ /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/kdb_libs/ 
fi


sed -i -e 's/^ELAB=FALSE$/ELAB=TRUE/' /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/simv.kdb 
