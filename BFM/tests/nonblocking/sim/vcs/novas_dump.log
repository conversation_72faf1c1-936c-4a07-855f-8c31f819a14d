#######################################################################################
# log primitive debug message of FSDB dumping                                         #
# This is for R&D to analyze when there are issues happening when FSDB dump           #
#######################################################################################
ANF: vcsd_get_serial_mode_status('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_get_serial_mode_status')
ANF: vcsd_enable_sva_success_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_enable_sva_success_callback')
ANF: vcsd_disable_sva_success_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_disable_sva_success_callback')
ANF: vcsd_get_power_scope_name('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_get_power_scope_name')
ANF: vcsd_dump_var_vpdg('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_dump_var_vpdg')
ANF: vcsd_begin_no_value_var_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_begin_no_value_var_info')
ANF: vcsd_end_no_value_var_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_end_no_value_var_info')
ANF: vcsd_remove_xprop_merge_mode_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_remove_xprop_merge_mode_callback')
ANF: vhpi_get_cb_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_get_cb_info')
ANF: vhpi_free_handle('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_free_handle')
ANF: vhpi_fetch_vcsd_handle('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_fetch_vcsd_handle')
ANF: vhpi_fetch_vpi_handle('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_fetch_vpi_handle')
ANF: vhpi_has_verilog_parent('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_has_verilog_parent')
ANF: vhpi_is_verilog_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_is_verilog_scope')
ANF: scsd_xprop_is_enabled('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_is_enabled')
ANF: scsd_xprop_sig_is_promoted('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_sig_is_promoted')
ANF: scsd_xprop_int_xvalue('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_int_xvalue')
ANF: scsd_xprop_bool_xvalue('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_bool_xvalue')
ANF: scsd_xprop_enum_xvalue('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_enum_xvalue')
ANF: scsd_xprop_register_merge_mode_cb('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_register_merge_mode_cb')
ANF: scsd_xprop_delete_merge_mode_cb('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_delete_merge_mode_cb')
ANF: scsd_xprop_get_merge_mode('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_get_merge_mode')
ANF: scsd_thread_get_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_thread_get_info')
ANF: scsd_thread_vc_init('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_thread_vc_init')
ANF: scsd_master_set_delta_sync_cbk('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_master_set_delta_sync_cbk')
ANF: scsd_fgp_get_fsdb_cores('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_fgp_get_fsdb_cores')
ANF: scsd_fgp_get_is_cpu_affinity_disabled('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_fgp_get_is_cpu_affinity_disabled')
ANF: vhdi_dt_get_type('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_type')
ANF: vhdi_dt_get_key('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_key')
ANF: vhdi_dt_get_vhdl_enum_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_enum_info')
ANF: vhdi_dt_get_vhdl_physical_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_physical_info')
ANF: vhdi_dt_get_vhdl_array_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_array_info')
ANF: vhdi_dt_get_vhdl_record_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_record_info')
ANF: vhdi_dt_get_vhdl_integer_range_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_integer_range_info')
ANF: vhdi_def_traverse_module('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_traverse_module')
ANF: vhdi_def_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_traverse_scope')
ANF: vhdi_def_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_traverse_variable')
ANF: vhdi_def_get_module_id_by_vhpi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_get_module_id_by_vhpi')
ANF: vhdi_def_get_handle_by_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_get_handle_by_module_id')
ANF: vhdi_def_get_variable_info_by_vhpi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_get_variable_info_by_vhpi')
ANF: vhdi_def_free('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_free')
ANF: vhdi_ist_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_traverse_scope')
ANF: vhdi_ist_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_traverse_variable')
ANF: vhdi_ist_convert_by_vhpi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_convert_by_vhpi')
ANF: vhdi_ist_clone('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_clone')
ANF: vhdi_ist_free('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_free')
ANF: vhdi_ist_hash_key('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_hash_key')
ANF: vhdi_ist_compare('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_compare')
ANF: vhdi_ist_get_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_get_value_addr')
ANF: vhdi_set_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_scsd_callback')
ANF: vhdi_cbk_set_force_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_cbk_set_force_callback')
ANF: vhdi_trigger_init_force('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_trigger_init_force')
ANF: vhdi_ist_check_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_check_scsd_callback')
ANF: vhdi_ist_add_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_add_scsd_callback')
ANF: vhdi_ist_remove_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_remove_scsd_callback')
ANF: vhdi_ist_get_scsd_user_data('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_get_scsd_user_data')
ANF: vhdi_add_time_change_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_add_time_change_callback')
ANF: vhdi_get_real_value_by_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_get_real_value_by_value_addr')
ANF: vhdi_get_64_value_by_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_get_64_value_by_value_addr')
ANF: vhdi_xprop_inst_is_promoted('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_xprop_inst_is_promoted')
ANF: vdi_ist_convert_by_vhdi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vdi_ist_convert_by_vhdi')
ANF: vhdi_ist_get_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_get_module_id')
ANF: vhdi_refine_foreign_scope_type('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_refine_foreign_scope_type')
ANF: vhdi_flush_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_flush_callback')
ANF: vhdi_set_orig_name('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_orig_name')
ANF: vhdi_set_dump_pt('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_dump_pt')
ANF: vhdi_set_int_subtype('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_int_subtype')
ANF: vhdi_set_fixed_point('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_fixed_point')
ANF: vhdi_get_fsdb_option('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_get_fsdb_option')
ANF: vhdi_fgp_get_mode('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_fgp_get_mode')
ANF: vhdi_node_register_composite_var('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_register_composite_var')
ANF: vhdi_node_analysis('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_analysis')
ANF: vhdi_node_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_id')
ANF: vhdi_node_ist_check_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_ist_check_scsd_callback')
ANF: vhdi_node_ist_add_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_ist_add_scsd_callback')
ANF: vhdi_node_ist_get_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_ist_get_value_addr')
ANF: vhdi_enc_def_traverse_module('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_traverse_module')
ANF: vhdi_enc_def_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_traverse_scope')
ANF: vhdi_enc_def_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_traverse_variable')
ANF: vhdi_enc_ist_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_ist_traverse_scope')
ANF: vhdi_enc_ist_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_ist_traverse_variable')
ANF: vhdi_enc_ist_get_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_ist_get_module_id')
ANF: vhdi_enc_def_get_handle_by_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_get_handle_by_module_id')
VCS compile option:
 option[0]: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv
 option[1]: -sv_lib
 option[2]: ../../../nonblocking/pseudo_bridge_nonblocking
 option[3]: +UVM_VERDI_TRACE=UVM_AWARE
 option[4]: +fsdb+gate=off
 option[5]: -ucli2Proc
 option[6]: -ucli
 option[7]: -l
 option[8]: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/sim.log
 option[9]: /opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/vcs1
 option[10]: -Mcc=gcc
 option[11]: -Mcplusplus=g++
 option[12]: -Masflags=
 option[13]: -Mcfl= -pipe -fPIC -g -I../../../nonblocking/src/cpp -I/opt/synopsys/vcs/V-2023.12-SP2/include 
 option[14]: -Mxllcflags=
 option[15]: -Mxcflags= -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include
 option[16]: -Mldflags= -rdynamic 
 option[17]: -Mout=simv
 option[18]: -Mamsrun=
 option[19]: -Mvcsaceobjs=
 option[20]: -Mobjects= /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvirsim.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/liberrorinf.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libsnpsmalloc.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvfs.so 
 option[21]: -Mexternalobj=
 option[22]: -Msaverestoreobj=/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_save_restore_new.o
 option[23]: -Mcrt0=
 option[24]: -Mcrtn=
 option[25]: -Mcsrc=
 option[26]: -Msyslibs=/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/pli.a -ldl 
 option[27]: -l
 option[28]: vcs_comp.log
 option[29]: -full64
 option[30]: -debug_access+all
 option[31]: +vpi
 option[32]: +vcsd1
 option[33]: +itf+/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcsdp_lite.tab
 option[34]: -timescale=1ns/1ps
 option[35]: -kdb
 option[36]: -Xufe=2steps
 option[37]: -lca
 option[38]: +define+DEBUG_BFM
 option[39]: +define+DUMP_FSDB
 option[40]: -top
 option[41]: tb_sideband_out
 option[42]: -o
 option[43]: simv
 option[44]: -picarchive
 option[45]: -P
 option[46]: /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/verdi.tab
 option[47]: -fsdb
 option[48]: -sverilog
 option[49]: +vpi
 option[50]: -gen_obj
 option[51]: +incdir+../../../../src
 option[52]: +incdir+../../../nonblocking/src/sv
 option[53]: ../../../../src/sideband_out_bfm.sv
 option[54]: ../../../../src/fifo.sv
 option[55]: ../../../nonblocking/src/sv/tb_sideband_out.sv
 option[56]: -load
 option[57]: /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/libnovas.so:FSDBDumpCmd
 option[58]: timescale=1ns/1ps
Chronologic Simulation VCS Release V-2023.12-SP2_Full64
Linux 3.10.0-1160.119.1.el7.x86_64 #1 SMP Tue Jun 4 14:43:51 UTC 2024 x86_64
CPU cores: 8
Limit information:
======================================
cputime		unlimited
filesize		unlimited
datasize		unlimited
stacksize		8192 kbytes
coredumpsize		0 kbytes
memoryuse		unlimited
vmemoryuse		unlimited
descriptors		4096
memorylocked		64 kbytes
maxproc		4096
======================================
(Special)Runtime environment variables:

Runtime environment variables:
SOURCE_ABSEIL=1
GREP_COLOR=37;45
XDG_VTNR=1
MANPATH=/opt/rh/devtoolset-11/root/usr/share/man
_ZO_DATA_DIR=/home/<USER>/.config/zoxide
SOURCE_VERIBLE=1
XSEL_HOME=/home/<USER>/xsel
LESS_TERMCAP_mb=[1;31m
SSH_AGENT_PID=2883
XDG_SESSION_ID=1
ZLIB_HOME=/home/<USER>/zlib
NOVAS_SYNC_MOTIF_DISP=
FLATBUFFERS_HOME=/home/<USER>/flatbuffers
SOURCE_GO=1
SOURCE_BEAR=1
LESS_TERMCAP_md=[1;31m
IMSETTINGS_INTEGRATE_DESKTOP=yes
ONIGURUMA_HOME=/home/<USER>/oniguruma
SOURCE_LIBTOOL=1
SOURCE_IMLIB2=1
SOURCE_GTEST=1
LESS_TERMCAP_me=[0m
VIM_HOME=/home/<USER>/vim
SOURCE_DOXYGEN=1
TERM=gnome-256color
SHELL=/home/<USER>/zsh/bin/zsh
XDG_MENU_PREFIX=gnome-
VTE_VERSION=5204
SOURCE_CMAKE=1
RIPGREP_CONFIG_PATH=/home/<USER>/.ripgreprc
LUA_HOME=/home/<USER>/lua
SOURCE_AUTOMAKE=1
SOURCE_AUTOCONF=1
SPS_FONT_PATH=/opt/synopsys/verdi/V-2023.12-SP2/font
GNOME_TERMINAL_SCREEN=/org/gnome/Terminal/screen/806a719e_8871_42e0_8c9b_d686a19754e0
VCS_STACK_EXEC=true
AUTOMAKE_HOME=/home/<USER>/automake
SPS_XFONT_PATH=/opt/synopsys/verdi/V-2023.12-SP2/XFont
SOURCE_SYSTEMC=1
SOURCE_FLATBUFFERS=1
VCS_PATHMAP_PRELOAD_DONE=1
LESS_TERMCAP_ue=[0m
SPDLOG_HOME=/home/<USER>/spdlog
AUTOCONF_HOME=/home/<USER>/autoconf
SOURCE_FEH=1
LC_ALL=C
GIT_EDITOR=vim
XCLIP_HOME=/home/<USER>/xclip
OPENSSL_HOME=/home/<USER>/openssl
SOURCE_LIBPSL=1
SOURCE_CURL=1
IMSETTINGS_MODULE=none
http_proxy=http://127.0.0.1:7890
PCP_DIR=/opt/rh/devtoolset-11/root
CMAKE_HOME=/home/<USER>/cmake
USER=harriszh
SOURCE_YODL=1
LD_LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib64:/opt/rh/devtoolset-11/root/usr/lib:/opt/rh/devtoolset-11/root/usr/lib64/dyninst:/opt/rh/devtoolset-11/root/usr/lib/dyninst:/home/<USER>/zsh/lib:/home/<USER>/zlib/lib:/home/<USER>/zeromq/lib:/home/<USER>/zeromq/lib64:/home/<USER>/tcl/lib:/home/<USER>/systemc/lib:/home/<USER>/spdlog/lib64:/home/<USER>/readline/lib:/home/<USER>/python/lib:/home/<USER>/pcre2/lib64:/home/<USER>/openssl/lib64:/home/<USER>/oniguruma/lib:/home/<USER>/node/lib:/home/<USER>/lua/lib:/home/<USER>/llvm/lib:/home/<USER>/libtool/lib:/home/<USER>/libsocket/lib:/home/<USER>/libpsl/lib:/home/<USER>/libiconv/lib:/home/<USER>/libevent/lib64:/home/<USER>/libb2/lib:/home/<USER>/iverilog/lib:/home/<USER>/imlib2/lib:/home/<USER>/gtest/lib64:/home/<USER>/graphviz/lib:/home/<USER>/go/lib:/home/<USER>/global/lib:/home/<USER>/giflib/lib:/home/<USER>/gettext/lib:/home/<USER>/flatbuffers/lib64:/home/<USER>/dtc/lib:/home/<USER>/curl/lib64:/home/<USER>/bear/lib64:/home/<USER>/abseil/lib64:/home/<USER>/.local/lib64:/home/<USER>/.local/lib:/usr/local/lib:/lib64:/usr/lib64:/usr/lib:/lib
LS_COLORS=di=1;34:ln=35:so=32:pi=33:ex=31:bd=1;36:cd=1;33:su=30;41:sg=30;46:tw=30;42:ow=30;43
VERIBLE_HOME=/home/<USER>/verible
GREP_COLORS=mt=37;45
GNOME_TERMINAL_SERVICE=:1.112
XNLSPATH=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/nls
NO_PLATFORM_REL_CHECK=1
FEH_HOME=/home/<USER>/feh
SOURCE_ZSH=1
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
MALLOC_ARENA_MAX=1
PUBLIC_TOOLS=/home/<USER>
SNPSLMD_LICENSE_FILE=27000@pc-harriszh2
SOURCE_PCRE2=1
USERNAME=harriszh
FZF_DEFAULT_OPTS=
  --bind='?:toggle-preview'
  --bind='ctrl-u:preview-page-up'
  --bind='ctrl-d:preview-page-down'
  --bind 'ctrl-/:change-preview-window(80%,border-bottom|hidden|)' 
  --bind 'ctrl-r:reload(fd --type f --follow --exclude .git --exclude \*.swp)'
  --bind 'ctrl-i:reload(fd --type f -I --follow --exclude .git --exclude \*.swp)'
  --bind 'ctrl-h:reload(fd --type f -I -H  --follow --exclude .git --exclude \*.swp)'
  --bind 'ctrl-o:execute(vim {})'
  --bind 'ctrl-w:reload(/home/<USER>/.cargo/bin/zoxide query --list)'
  --preview-window 'right:60%:wrap'
  --preview '/home/<USER>/.sh/fzf-bat.sh {}'
GETTEXT_HOME=/home/<USER>/gettext
DROPBEAR_HOME=/home/<USER>/dropbear
BITWISE_HOME=/home/<USER>/bitwise
PAGER=less
LIBICONV_HOME=/home/<USER>/libiconv
SOURCE_BITWISE=1
LESS_TERMCAP_us=[1;32m
ABSEIL_HOME=/home/<USER>/abseil
SOURCE_XCLIP=1
SOURCE_TMUX=1
SOURCE_LIBSOCKET=1
GNOME_SHELL_SESSION_MODE=classic
PATH=/opt/synopsys/vcs/V-2023.12-SP2/bin:/opt/synopsys/verdi/V-2023.12-SP2/bin:/opt/rh/devtoolset-11/root/usr/bin:/home/<USER>/git-extras/bin:/home/<USER>/zsh/bin:/home/<USER>/zeromq/bin:/home/<USER>/yodl/bin:/home/<USER>/xsel/bin:/home/<USER>/xclip/bin:/home/<USER>/vim/bin:/home/<USER>/verible/bin:/home/<USER>/tmux/bin:/home/<USER>/tcl/bin:/home/<USER>/readline/bin:/home/<USER>/python/bin:/home/<USER>/pcre2/bin:/home/<USER>/openssl/bin:/home/<USER>/oniguruma/bin:/home/<USER>/node/bin:/home/<USER>/make/bin:/home/<USER>/m4/bin:/home/<USER>/lua/bin:/home/<USER>/llvm/bin:/home/<USER>/libtool/bin:/home/<USER>/libpsl/bin:/home/<USER>/libiconv/bin:/home/<USER>/libevent/bin:/home/<USER>/iverilog/bin:/home/<USER>/imlib2/bin:/home/<USER>/graphviz/bin:/home/<USER>/gperf/bin:/home/<USER>/go/bin:/home/<USER>/global/bin:/home/<USER>/git/bin:/home/<USER>/giflib/bin:/home/<USER>/gettext/bin:/home/<USER>/flatbuffers/bin:/home/<USER>/feh/bin:/home/<USER>/dtc/bin:/home/<USER>/dropbear/bin:/home/<USER>/dropbear/sbin:/home/<USER>/doxygen/bin:/home/<USER>/curl/bin:/home/<USER>/ctags/bin:/home/<USER>/cmake/bin:/home/<USER>/bitwise/bin:/home/<USER>/bear/bin:/home/<USER>/automake/bin:/home/<USER>/autoconf/bin:/home/<USER>/local/bin:/home/<USER>/.cargo/bin:/trunk/go/bin:/usr/local/bin:/usr/local/sbin:/usr/bin:/usr/sbin:/bin:/sbin:/home/<USER>/.fzf/bin
DESKTOP_SESSION=gnome-classic
YODL_HOME=/home/<USER>/yodl
SOURCE_LIBICONV=1
CARGO_HOME=/home/<USER>/.cargo
M4_HOME=/home/<USER>/m4
QT_IM_MODULE=ibus
VERDI_HOME=/opt/synopsys/verdi/V-2023.12-SP2
GO_HOME=/home/<USER>/go
XDG_SESSION_TYPE=x11
PWD=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs
VCS_HOME=/opt/synopsys/vcs/V-2023.12-SP2
FZF_ALT_C_COMMAND=fd --type d --hidden --follow --exclude .git --exclude \*.swp
SOURCE_ZEROMQ=1
XMODIFIERS=@im=none
EDITOR=vim
BAT_STYLE=full
LIBEVENT_HOME=/home/<USER>/libevent
SOURCE_GLOBAL=1
SYS_PROG_NAME=verdi
LIBPSL_HOME=/home/<USER>/libpsl
SOURCE_READLINE=1
LANG=C
GDM_LANG=en_US.UTF-8
SYSTEMC_HOME=/home/<USER>/systemc
READLINE_HOME=/home/<USER>/readline
LIBSOCKET_HOME=/home/<USER>/libsocket
GPERF_HOME=/home/<USER>/gperf
SOURCE_PYTHON=1
SOURCE_LUA=1
SYS_INST_DIR=/opt/synopsys/verdi/V-2023.12-SP2
https_proxy=http://127.0.0.1:7890
LM_LICENSE_FILE=5280@127.0.0.1
GDMSESSION=gnome-classic
GRAPHVIZ_HOME=/home/<USER>/graphviz
GIFLIB_HOME=/home/<USER>/giflib
SOURCE_GETTEXT=1
MAKE_HOME=/home/<USER>/make
CURL_HOME=/home/<USER>/curl
MANPAGER=sh -c 'col -bx | bat -l man -p --paging always'
LIBB2_HOME=/home/<USER>/libb2
IVERILOG_HOME=/home/<USER>/iverilog
SOURCE_M4=1
XDG_SEAT=seat0
HOME=/home/<USER>
SHLVL=1
VERDI_LIB=/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64
DOXYGEN_HOME=/home/<USER>/doxygen
SOURCE_CTAGS=1
VCS_EXEC_DONE=1
LIBTOOL_HOME=/home/<USER>/libtool
SOURCE_LIBB2=1
VERDI_ORIGNAL_LD_LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib64:/opt/rh/devtoolset-11/root/usr/lib:/opt/rh/devtoolset-11/root/usr/lib64/dyninst:/opt/rh/devtoolset-11/root/usr/lib/dyninst:/home/<USER>/zsh/lib:/home/<USER>/zlib/lib:/home/<USER>/zeromq/lib:/home/<USER>/zeromq/lib64:/home/<USER>/tcl/lib:/home/<USER>/systemc/lib:/home/<USER>/spdlog/lib64:/home/<USER>/readline/lib:/home/<USER>/python/lib:/home/<USER>/pcre2/lib64:/home/<USER>/openssl/lib64:/home/<USER>/oniguruma/lib:/home/<USER>/node/lib:/home/<USER>/lua/lib:/home/<USER>/llvm/lib:/home/<USER>/libtool/lib:/home/<USER>/libsocket/lib:/home/<USER>/libpsl/lib:/home/<USER>/libiconv/lib:/home/<USER>/libevent/lib64:/home/<USER>/libb2/lib:/home/<USER>/iverilog/lib:/home/<USER>/imlib2/lib:/home/<USER>/gtest/lib64:/home/<USER>/graphviz/lib:/home/<USER>/go/lib:/home/<USER>/global/lib:/home/<USER>/giflib/lib:/home/<USER>/gettext/lib:/home/<USER>/flatbuffers/lib64:/home/<USER>/dtc/lib:/home/<USER>/curl/lib64:/home/<USER>/bear/lib64:/home/<USER>/abseil/lib64:/home/<USER>/.local/lib64:/home/<USER>/.local/lib:/usr/local/lib:/lib64:/usr/lib64:/usr/lib:/lib
FZF_DEFAULT_COMMAND2=fd --type f -I --follow --exclude .git --exclude \*.swp
TMUX_HOME=/home/<USER>/tmux
BEAR_HOME=/home/<USER>/bear
SOURCE_OPENSSL=1
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
FZF_DEFAULT_COMMAND3=fd --type f -I -H  --follow --exclude .git --exclude \*.swp
TCL_HOME=/home/<USER>/tcl
SOURCE_SPDLOG=1
SOURCE_GRAPHVIZ=1
GIT_HOME=/home/<USER>/git
SOURCE_GIT=1
ZSH_HOME=/home/<USER>/zsh
SOURCE_ONIGURUMA=1
LESS=--chop-long-lines --ignore-case --jump-target=4 --LONG-PROMPT --no-init --quit-if-one-screen --RAW-CONTROL-CHARS -R
XDG_SESSION_DESKTOP=gnome-classic
LOGNAME=harriszh
BATPIPE=color
FZF_CTRL_T_COMMAND=fd --type f --type d --hidden --follow --exclude .git --exclude \*.swp
GLOBAL_HOME=/home/<USER>/global
SOURCE_LLVM=1
SOURCE_GIFLIB=1
XDG_DATA_DIRS=/home/<USER>/.local/share/flatpak/exports/share/:/var/lib/flatpak/exports/share/:/usr/local/share/:/usr/share/
DBUS_SESSION_BUS_ADDRESS=unix:abstract=/tmp/dbus-jrCY1UuLJL,guid=47f535f9f7a5101a64d402b06877357b
NOVASHLPPATH=/opt/synopsys/verdi/V-2023.12-SP2/doc
SPS_RGB_PATH=/opt/synopsys/verdi/V-2023.12-SP2/etc/rgb
CDS_LIC_FILE=5280@127.0.0.1
FZF_DEFAULT_COMMAND=fd --type f --follow --exclude .git --exclude \*.swp
ZEROMQ_HOME=/home/<USER>/zeromq
CTAGS_HOME=/home/<USER>/ctags
LESSOPEN=|/home/<USER>/local/bin/batpipe %s
DTC_HOME=/home/<USER>/dtc
PKG_CONFIG_PATH=/opt/rh/devtoolset-11/root/usr/lib64/pkgconfig:/home/<USER>/zlib/lib/pkgconfig:/home/<USER>/zeromq/lib/pkgconfig:/home/<USER>/zeromq/lib64/pkgconfig:/home/<USER>/tcl/lib/pkgconfig:/home/<USER>/systemc/lib/pkgconfig:/home/<USER>/spdlog/lib64/pkgconfig:/home/<USER>/readline/lib/pkgconfig:/home/<USER>/python/lib/pkgconfig:/home/<USER>/pcre2/lib64/pkgconfig:/home/<USER>/openssl/lib64/pkgconfig:/home/<USER>/oniguruma/lib/pkgconfig:/home/<USER>/libsocket/lib/pkgconfig:/home/<USER>/libpsl/lib/pkgconfig:/home/<USER>/libiconv/lib/pkgconfig:/home/<USER>/libevent/lib64/pkgconfig:/home/<USER>/libb2/lib/pkgconfig:/home/<USER>/imlib2/lib/pkgconfig:/home/<USER>/gtest/lib64/pkgconfig:/home/<USER>/graphviz/lib/pkgconfig:/home/<USER>/flatbuffers/lib64/pkgconfig:/home/<USER>/curl/lib64/pkgconfig:/home/<USER>/abseil/lib64/pkgconfig:/home/<USER>/.local/lib64/pkgconfig:/home/<USER>/.local/lib/pkgconfig:/home/<USER>/.local/vte/lib64/pkgconfig:
GIT_EXTRAS_HOME=/home/<USER>/git-extras
INFOPATH=/opt/rh/devtoolset-11/root/usr/share/info
ACLOCAL_PATH=/home/<USER>/libtool/share/aclocal:/usr/share/aclocal
SOURCE_ZLIB=1
WINDOWPATH=1
LD_NOVERSION=1
XDG_RUNTIME_DIR=/run/user/1000
DISPLAY=:0
QT_PLUGIN_PATH=/opt/synopsys/verdi/V-2023.12-SP2/platform/LINUXAMD64/lib/Qt/plugins
BAT_THEME=gruvbox-dark
LLVM_HOME=/home/<USER>/llvm
GTEST_HOME=/home/<USER>/gtest
SOURCE_TCL=1
SOURCE_DROPBEAR=1
BITMODE=64
PYTHON_HOME=/home/<USER>/python
PCRE2_HOME=/home/<USER>/pcre2
SOURCE_MAKE=1
SOURCE_GPERF=1
XDG_CURRENT_DESKTOP=GNOME-Classic:GNOME
SOURCE_LIBEVENT=1
SOURCE_DTC=1
NODE_HOME=/home/<USER>/node
SOURCE_XSEL=1
SOURCE_VIM=1
SOURCE_NODE=1
IMLIB2_HOME=/home/<USER>/imlib2
SOURCE_IVERILOG=1
COLORTERM=24bit
XAUTHORITY=/run/gdm/auth-for-harriszh-idrDvM/database
NOVAS_LC_ALL=C
SIMV_DAIDIR_PATH=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir
NOVAS_SIGNAL_BASE_EXTRACTION=1
SIGNAL_BASE_EXTRACTION=1
NOVAS_VERDI_SVTB_BETA=1
VERDI_SVTB_BETA=1
NOVAS_VERDI_SVTB_ALPHA=1
VERDI_SVTB_ALPHA=1
NOVAS_VERDI_TB_HT=1
VERDI_TB_HT=1
NOVAS_WAVE_REDRAW_ALLVC=1
WAVE_REDRAW_ALLVC=1
NOVAS_TCL_LIBRARY=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/tcl86_library
TCL_LIBRARY=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/tcl86_library
XKEYSYMDB=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/XKeysymDB2.1
XLOCALEDIR=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/locale
QTWEBENGINE_CHROMIUM_FLAGS=--log-level=3
NOVAS_SIGNAL_BASED_BA=0
SIGNAL_BASED_BA=0
SNPS_SIM_DEFAULT_GUI=verdi
FSDB_FILE=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
TBSIM_SIM_LOG_PATH=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/vcs.log
VCS_UCLI_STDIN_BLOCKING=1
FSDB_VHDL_PROTECTED=1
FSDB_RD_IR_ENABLE=1
FSDB_SVA_STATUS=1
SYNOPSYS_SIM=/opt/synopsys/vcs/V-2023.12-SP2
DISABLE_LIBRARY_MAP_CHECK=1
AST_DUMP=11
TRMIS_REVERSE_JUMP_TO_EARLIEST=1
TS_TRMIS_DECIDE_RM_RC_ATTR=0
TFV_FSDB_MIS_BATCH_REMOVE_INTF_RC_HAVING_INTF_MISMATCH=0
TFV_FSDB_MIS_BATCH_ENABLE_FSDB_GATE=1
TFV_FSDB_MIS_BATCH_DUMP_TDC=0
TFV_FSDB_MIS_RCA_MODE=1
VCS_STOP_SAFE=1
DVE_SIM_SELECT_LOOP=on
FLEXLM_BORROWFILE=/home/<USER>/.pc-harriszh2-borrow.txt
Runtime command line arguments:
argv[0]=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv
argv[1]=-sv_lib
argv[2]=../../../nonblocking/pseudo_bridge_nonblocking
argv[3]=+UVM_VERDI_TRACE=UVM_AWARE
argv[4]=+fsdb+gate=off
argv[5]=-ucli2Proc
argv[6]=-ucli
argv[7]=-l
argv[8]=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/sim.log
414 profile - 100
          CPU/Mem usage: 0.040 sys,  0.070 user,  290.49M mem
415 Elapsed time:    0:00:02    Sun Jul 20 19:06:21 2025
416 User CPU time used: 0 seconds
417 System CPU time used: 0 seconds
418 pliAppInit
419 ndpGetenv(FSDB_FILE): /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
420 ndpGetenv(FSDB_SVA_STATUS): 1
421 ndpGetenv(FSDB_VHDL_PROTECTED): 1
422 [SC]Init: MemoryConsumed=0.00MB ElapsedTime=0hour 0min. 0sec.
423 FSDB_GATE & FSDB_RTL is disabled.
424 FSDB_PACKED_MODE is enabled.
425 FSDB_GATE_PACKED is enabled.
426 FSDB_ALIAS_ARRAY_ELEM is set to 4294967295.
427 Enable Parallel Dumping.
428 pliAppMiscSet: New Sim Round
429 pliEntryInit
430 LIBSSCORE=found /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/lib/LINUXAMD64/libsscore_vcs202312.so through $NOVAS_HOME setting.
431 FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
432 (C) 1996 - 2024 by Synopsys, Inc.
433 sps_tcl_fsdbDumpfile_main at 0
434 argv[0]: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
435 *Verdi* : Create FSDB file '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb'
436 [spi_vcs_vd_ppi_create_root]: no upf option
437 compile option from '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/vcs_rebuild'.
438   "vcs '-full64' '-sverilog' '-debug_access+all' '-timescale=1ns/1ps' '-kdb' '-lca' '+vpi' '+define+DEBUG_BFM' '+define+DUMP_FSDB' '+incdir+../../../../src' '+incdir+../../../nonblocking/src/sv' '../../../../src/sideband_out_bfm.sv' '../../../../src/fifo.sv' '../../../nonblocking/src/sv/tb_sideband_out.sv' '-top' 'tb_sideband_out' '-o' 'simv' '-l' 'vcs_comp.log' '-CFLAGS' '-g -I../../../nonblocking/src/cpp' 2>&1"
439 sps_tcl_fsdbDumpflush_vd_main at 0 : N/A(0)
440 *Verdi* : Flush all FSDB Files at 0 ps.
441 DVDI_is_vir_unload_enabled is enable
442 FSDB_VCS_ENABLE_NATIVE_VC is enable
443 sps_tcl_fsdbDumpvars_vd_main
444 argv[0]: 0
445 argv[1]: tb_sideband_out.u_bfm.clk
446 argv[2]: +all
447 argv[3]: +trace_process
448 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.clk).
449 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.070 sys,  0.100 user,  389.94M mem
                   incr: 0.010 sys,  0.000 user,  2.27M mem
                   accu: 0.010 sys,  0.000 user,  2.27M mem
              accu incr: 0.010 sys,  0.000 user,  2.27M mem

          Count usage: 0 var,  2 idcode,  1 callback
                 incr: 0 var,  2 idcode,  1 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  2 idcode,  1 callback
450 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
451 User CPU time used: 0 seconds
452 System CPU time used: 0 seconds
453 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.070 sys,  0.100 user,  391.06M mem
                   incr: 0.000 sys,  0.000 user,  1.12M mem
                   accu: 0.010 sys,  0.000 user,  3.38M mem
              accu incr: 0.000 sys,  0.000 user,  1.12M mem

          Count usage: 0 var,  2 idcode,  1 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
454 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
455 User CPU time used: 0 seconds
456 System CPU time used: 0 seconds
457 sps_tcl_fsdbDumpvars_vd_main
458 argv[0]: 0
459 argv[1]: tb_sideband_out.u_bfm.rst_n
460 argv[2]: +all
461 argv[3]: +trace_process
462 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.rst_n).
463 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.070 sys,  0.100 user,  391.06M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  4 idcode,  2 callback
                 incr: 0 var,  2 idcode,  1 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  2 idcode,  1 callback
464 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
465 User CPU time used: 0 seconds
466 System CPU time used: 0 seconds
467 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.070 sys,  0.100 user,  391.06M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  4 idcode,  2 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
468 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
469 User CPU time used: 0 seconds
470 System CPU time used: 0 seconds
471 sps_tcl_fsdbDumpvars_vd_main
472 argv[0]: 0
473 argv[1]: tb_sideband_out.u_bfm.sideband_in
474 argv[2]: +all
475 argv[3]: +trace_process
476 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in).
477 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.070 sys,  0.100 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  1.06M mem
                   accu: 0.000 sys,  0.000 user,  1.06M mem
              accu incr: 0.000 sys,  0.000 user,  1.06M mem

          Count usage: 0 var,  5 idcode,  3 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
478 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
479 User CPU time used: 0 seconds
480 System CPU time used: 0 seconds
481 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.070 sys,  0.100 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  1.06M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  5 idcode,  3 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
482 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
483 User CPU time used: 0 seconds
484 System CPU time used: 0 seconds
485 sps_tcl_fsdbDumpvars_vd_main
486 argv[0]: 0
487 argv[1]: tb_sideband_out.u_bfm.sideband_valid
488 argv[2]: +all
489 argv[3]: +trace_process
490 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_valid).
491 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.010 user,  0.00M mem
                   accu: 0.000 sys,  0.010 user,  0.00M mem
              accu incr: 0.000 sys,  0.010 user,  0.00M mem

          Count usage: 0 var,  6 idcode,  4 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
492 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
493 User CPU time used: 0 seconds
494 System CPU time used: 0 seconds
495 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.010 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  6 idcode,  4 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
496 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
497 User CPU time used: 0 seconds
498 System CPU time used: 0 seconds
499 sps_tcl_fsdbDumpvars_vd_main
500 argv[0]: 0
501 argv[1]: tb_sideband_out.u_bfm.INSTANCE_ID
502 argv[2]: +all
503 argv[3]: +trace_process
504 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.INSTANCE_ID).
505 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  7 idcode,  4 callback
                 incr: 0 var,  1 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  1 idcode,  0 callback
506 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
507 User CPU time used: 0 seconds
508 System CPU time used: 0 seconds
509 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  7 idcode,  4 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
510 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
511 User CPU time used: 0 seconds
512 System CPU time used: 0 seconds
513 sps_tcl_fsdbDumpvars_vd_main
514 argv[0]: 0
515 argv[1]: tb_sideband_out.u_bfm.NUM_SIGNALS
516 argv[2]: +all
517 argv[3]: +trace_process
518 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.NUM_SIGNALS).
519 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  8 idcode,  4 callback
                 incr: 0 var,  1 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  1 idcode,  0 callback
520 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
521 User CPU time used: 0 seconds
522 System CPU time used: 0 seconds
523 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  8 idcode,  4 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
524 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
525 User CPU time used: 0 seconds
526 System CPU time used: 0 seconds
527 sps_tcl_fsdbDumpvars_vd_main
528 argv[0]: 0
529 argv[1]: tb_sideband_out.u_bfm.DELAY_WIDTH
530 argv[2]: +all
531 argv[3]: +trace_process
532 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.DELAY_WIDTH).
533 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  8 idcode,  4 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  0 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
534 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
535 User CPU time used: 0 seconds
536 System CPU time used: 0 seconds
537 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.070 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  8 idcode,  4 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  0 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
538 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
539 User CPU time used: 0 seconds
540 System CPU time used: 0 seconds
541 sps_tcl_fsdbDumpvars_vd_main
542 argv[0]: 0
543 argv[1]: tb_sideband_out.u_bfm.configured_delays
544 argv[2]: +all
545 argv[3]: +trace_process
546 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.configured_delays).
547 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.080 sys,  0.110 user,  392.13M mem
                   incr: 0.010 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.010 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  16 idcode,  5 callback
                 incr: 0 var,  8 idcode,  1 callback
                 accu: 0 var,  8 idcode,  1 callback
            accu incr: 0 var,  8 idcode,  1 callback
548 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
549 User CPU time used: 0 seconds
550 System CPU time used: 0 seconds
551 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.080 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  16 idcode,  5 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  8 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
552 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
553 User CPU time used: 0 seconds
554 System CPU time used: 0 seconds
555 sps_tcl_fsdbDumpvars_vd_main
556 argv[0]: 0
557 argv[1]: tb_sideband_out.u_bfm.sideband_valid_d
558 argv[2]: +all
559 argv[3]: +trace_process
560 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_valid_d).
561 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.080 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  17 idcode,  6 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
562 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
563 User CPU time used: 0 seconds
564 System CPU time used: 0 seconds
565 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.080 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  17 idcode,  6 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
566 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
567 User CPU time used: 0 seconds
568 System CPU time used: 0 seconds
569 sps_tcl_fsdbDumpvars_vd_main
570 argv[0]: 0
571 argv[1]: tb_sideband_out.u_bfm.is_blocking_mode
572 argv[2]: +all
573 argv[3]: +trace_process
574 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.is_blocking_mode).
575 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.080 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  18 idcode,  7 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
576 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
577 User CPU time used: 0 seconds
578 System CPU time used: 0 seconds
579 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.080 sys,  0.110 user,  392.13M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  18 idcode,  7 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
580 Elapsed time:    0:00:03    Sun Jul 20 19:06:22 2025
581 User CPU time used: 0 seconds
582 System CPU time used: 0 seconds
583 sps_tcl_fsdbDumpflush_vd_main at 0 : N/A(0)
584 *Verdi* : Flush all FSDB Files at 0 ps.
585 sps_tcl_fsdbDumpflush_vd_main at 0 : N/A(0)
586 *Verdi* : Flush all FSDB Files at 0 ps.
587 sps_call_fsdbDumpvars_vd_main at 0 : ../../../nonblocking/src/sv/tb_sideband_out.sv(116)
588 argv[0]: (0)
589 argv[1]: (handle) tb_sideband_out
590 *Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
591 *Verdi* : End of traversing.
592 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.740 sys,  0.210 user,  394.63M mem
                   incr: 0.000 sys,  0.010 user,  0.00M mem
                   accu: 0.000 sys,  0.010 user,  0.00M mem
              accu incr: 0.000 sys,  0.010 user,  0.00M mem

          Count usage: 28 var,  40 idcode,  28 callback
                 incr: 28 var,  22 idcode,  21 callback
                 accu: 28 var,  22 idcode,  21 callback
            accu incr: 28 var,  22 idcode,  21 callback
593 Elapsed time:    0:00:07    Sun Jul 20 19:06:26 2025
594 User CPU time used: 0 seconds
595 System CPU time used: 0 seconds
596 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.740 sys,  0.210 user,  394.63M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.010 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  40 idcode,  28 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 28 var,  22 idcode,  21 callback
            accu incr: 0 var,  0 idcode,  0 callback
597 Elapsed time:    0:00:07    Sun Jul 20 19:06:26 2025
598 User CPU time used: 0 seconds
599 System CPU time used: 0 seconds
600 sps_call_fsdbDumpon_vd_main at 0 : ../../../nonblocking/src/sv/tb_sideband_out.sv(117)
601 *Verdi* : fsdbDumpon - All FSDB files at 0 ps.
602 sps_tcl_fsdbDumpflush_vd_main at 595000 : N/A(0)
603 *Verdi* : Flush all FSDB Files at 595,000 ps.
604 sps_tcl_fsdbDumpvars_vd_main
605 argv[0]: 1
606 argv[1]: tb_sideband_out.u_bfm.FIFO_DEPTH
607 argv[2]: +all
608 argv[3]: +trace_process
609 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.FIFO_DEPTH).
610 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.760 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  40 idcode,  28 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  0 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
611 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
612 User CPU time used: 0 seconds
613 System CPU time used: 0 seconds
614 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.760 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  40 idcode,  28 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  0 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
615 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
616 User CPU time used: 0 seconds
617 System CPU time used: 0 seconds
618 sps_tcl_fsdbDumpvars_vd_main
619 argv[0]: 1
620 argv[1]: tb_sideband_out.u_bfm.SIGNAL_IDX_WIDTH
621 argv[2]: +all
622 argv[3]: +trace_process
623 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.SIGNAL_IDX_WIDTH).
624 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.760 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  41 idcode,  28 callback
                 incr: 0 var,  1 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  1 idcode,  0 callback
625 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
626 User CPU time used: 0 seconds
627 System CPU time used: 0 seconds
628 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.760 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  41 idcode,  28 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
629 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
630 User CPU time used: 0 seconds
631 System CPU time used: 0 seconds
632 sps_tcl_fsdbDumpvars_vd_main
633 argv[0]: 1
634 argv[1]: tb_sideband_out.u_bfm.current_event
635 argv[2]: +all
636 argv[3]: +trace_process
637 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.current_event).
638 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.770 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  42 idcode,  29 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
639 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
640 User CPU time used: 0 seconds
641 System CPU time used: 0 seconds
642 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.770 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  42 idcode,  29 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
643 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
644 User CPU time used: 0 seconds
645 System CPU time used: 0 seconds
646 sps_tcl_fsdbDumpvars_vd_main
647 argv[0]: 1
648 argv[1]: tb_sideband_out.u_bfm.fifo_wr_data
649 argv[2]: +all
650 argv[3]: +trace_process
651 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_data).
652 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.770 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  43 idcode,  30 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
653 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
654 User CPU time used: 0 seconds
655 System CPU time used: 0 seconds
656 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.770 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  43 idcode,  30 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
657 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
658 User CPU time used: 0 seconds
659 System CPU time used: 0 seconds
660 sps_tcl_fsdbDumpvars_vd_main
661 argv[0]: 1
662 argv[1]: tb_sideband_out.u_bfm.fifo_rd_data
663 argv[2]: +all
664 argv[3]: +trace_process
665 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_data).
666 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.770 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  44 idcode,  31 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
667 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
668 User CPU time used: 0 seconds
669 System CPU time used: 0 seconds
670 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.770 sys,  0.210 user,  394.80M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 28 var,  44 idcode,  31 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
671 Elapsed time:    0:00:27    Sun Jul 20 19:06:46 2025
672 User CPU time used: 0 seconds
673 System CPU time used: 0 seconds
674 sps_tcl_fsdbDumpflush_vd_main at 595000 : N/A(0)
675 *Verdi* : Flush all FSDB Files at 595,000 ps.
