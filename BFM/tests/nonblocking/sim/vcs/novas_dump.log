#######################################################################################
# log primitive debug message of FSDB dumping                                         #
# This is for R&D to analyze when there are issues happening when FSDB dump           #
#######################################################################################
ANF: vcsd_get_serial_mode_status('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_get_serial_mode_status')
ANF: vcsd_enable_sva_success_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_enable_sva_success_callback')
ANF: vcsd_disable_sva_success_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_disable_sva_success_callback')
ANF: vcsd_get_power_scope_name('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_get_power_scope_name')
ANF: vcsd_dump_var_vpdg('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_dump_var_vpdg')
ANF: vcsd_begin_no_value_var_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_begin_no_value_var_info')
ANF: vcsd_end_no_value_var_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_end_no_value_var_info')
ANF: vcsd_remove_xprop_merge_mode_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vcsd_remove_xprop_merge_mode_callback')
ANF: vhpi_get_cb_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_get_cb_info')
ANF: vhpi_free_handle('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_free_handle')
ANF: vhpi_fetch_vcsd_handle('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_fetch_vcsd_handle')
ANF: vhpi_fetch_vpi_handle('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_fetch_vpi_handle')
ANF: vhpi_has_verilog_parent('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_has_verilog_parent')
ANF: vhpi_is_verilog_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhpi_is_verilog_scope')
ANF: scsd_xprop_is_enabled('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_is_enabled')
ANF: scsd_xprop_sig_is_promoted('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_sig_is_promoted')
ANF: scsd_xprop_int_xvalue('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_int_xvalue')
ANF: scsd_xprop_bool_xvalue('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_bool_xvalue')
ANF: scsd_xprop_enum_xvalue('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_enum_xvalue')
ANF: scsd_xprop_register_merge_mode_cb('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_register_merge_mode_cb')
ANF: scsd_xprop_delete_merge_mode_cb('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_delete_merge_mode_cb')
ANF: scsd_xprop_get_merge_mode('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_xprop_get_merge_mode')
ANF: scsd_thread_get_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_thread_get_info')
ANF: scsd_thread_vc_init('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_thread_vc_init')
ANF: scsd_master_set_delta_sync_cbk('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_master_set_delta_sync_cbk')
ANF: scsd_fgp_get_fsdb_cores('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_fgp_get_fsdb_cores')
ANF: scsd_fgp_get_is_cpu_affinity_disabled('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: scsd_fgp_get_is_cpu_affinity_disabled')
ANF: vhdi_dt_get_type('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_type')
ANF: vhdi_dt_get_key('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_key')
ANF: vhdi_dt_get_vhdl_enum_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_enum_info')
ANF: vhdi_dt_get_vhdl_physical_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_physical_info')
ANF: vhdi_dt_get_vhdl_array_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_array_info')
ANF: vhdi_dt_get_vhdl_record_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_record_info')
ANF: vhdi_dt_get_vhdl_integer_range_info('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_dt_get_vhdl_integer_range_info')
ANF: vhdi_def_traverse_module('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_traverse_module')
ANF: vhdi_def_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_traverse_scope')
ANF: vhdi_def_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_traverse_variable')
ANF: vhdi_def_get_module_id_by_vhpi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_get_module_id_by_vhpi')
ANF: vhdi_def_get_handle_by_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_get_handle_by_module_id')
ANF: vhdi_def_get_variable_info_by_vhpi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_get_variable_info_by_vhpi')
ANF: vhdi_def_free('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_def_free')
ANF: vhdi_ist_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_traverse_scope')
ANF: vhdi_ist_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_traverse_variable')
ANF: vhdi_ist_convert_by_vhpi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_convert_by_vhpi')
ANF: vhdi_ist_clone('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_clone')
ANF: vhdi_ist_free('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_free')
ANF: vhdi_ist_hash_key('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_hash_key')
ANF: vhdi_ist_compare('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_compare')
ANF: vhdi_ist_get_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_get_value_addr')
ANF: vhdi_set_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_scsd_callback')
ANF: vhdi_cbk_set_force_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_cbk_set_force_callback')
ANF: vhdi_trigger_init_force('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_trigger_init_force')
ANF: vhdi_ist_check_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_check_scsd_callback')
ANF: vhdi_ist_add_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_add_scsd_callback')
ANF: vhdi_ist_remove_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_remove_scsd_callback')
ANF: vhdi_ist_get_scsd_user_data('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_get_scsd_user_data')
ANF: vhdi_add_time_change_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_add_time_change_callback')
ANF: vhdi_get_real_value_by_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_get_real_value_by_value_addr')
ANF: vhdi_get_64_value_by_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_get_64_value_by_value_addr')
ANF: vhdi_xprop_inst_is_promoted('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_xprop_inst_is_promoted')
ANF: vdi_ist_convert_by_vhdi('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vdi_ist_convert_by_vhdi')
ANF: vhdi_ist_get_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_ist_get_module_id')
ANF: vhdi_refine_foreign_scope_type('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_refine_foreign_scope_type')
ANF: vhdi_flush_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_flush_callback')
ANF: vhdi_set_orig_name('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_orig_name')
ANF: vhdi_set_dump_pt('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_dump_pt')
ANF: vhdi_set_int_subtype('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_int_subtype')
ANF: vhdi_set_fixed_point('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_set_fixed_point')
ANF: vhdi_get_fsdb_option('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_get_fsdb_option')
ANF: vhdi_fgp_get_mode('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_fgp_get_mode')
ANF: vhdi_node_register_composite_var('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_register_composite_var')
ANF: vhdi_node_analysis('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_analysis')
ANF: vhdi_node_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_id')
ANF: vhdi_node_ist_check_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_ist_check_scsd_callback')
ANF: vhdi_node_ist_add_scsd_callback('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_ist_add_scsd_callback')
ANF: vhdi_node_ist_get_value_addr('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_node_ist_get_value_addr')
ANF: vhdi_enc_def_traverse_module('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_traverse_module')
ANF: vhdi_enc_def_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_traverse_scope')
ANF: vhdi_enc_def_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_traverse_variable')
ANF: vhdi_enc_ist_traverse_scope('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_ist_traverse_scope')
ANF: vhdi_enc_ist_traverse_variable('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_ist_traverse_variable')
ANF: vhdi_enc_ist_get_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_ist_get_module_id')
ANF: vhdi_enc_def_get_handle_by_module_id('/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv: undefined symbol: vhdi_enc_def_get_handle_by_module_id')
VCS compile option:
 option[0]: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv
 option[1]: -sv_lib
 option[2]: ../../../nonblocking/pseudo_bridge_nonblocking_basic
 option[3]: +UVM_VERDI_TRACE=UVM_AWARE
 option[4]: +fsdb+gate=off
 option[5]: -ucli2Proc
 option[6]: -ucli
 option[7]: -l
 option[8]: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/sim.log
 option[9]: /opt/synopsys/vcs/V-2023.12-SP2/linux64/bin/vcs1
 option[10]: -Mcc=gcc
 option[11]: -Mcplusplus=g++
 option[12]: -Masflags=
 option[13]: -Mcfl= -pipe -fPIC -g -I../../../nonblocking/src/cpp -I/opt/synopsys/vcs/V-2023.12-SP2/include 
 option[14]: -Mxllcflags=
 option[15]: -Mxcflags= -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include
 option[16]: -Mldflags= -rdynamic 
 option[17]: -Mout=simv
 option[18]: -Mamsrun=
 option[19]: -Mvcsaceobjs=
 option[20]: -Mobjects= /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvirsim.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/liberrorinf.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libsnpsmalloc.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvfs.so 
 option[21]: -Mexternalobj=
 option[22]: -Msaverestoreobj=/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_save_restore_new.o
 option[23]: -Mcrt0=
 option[24]: -Mcrtn=
 option[25]: -Mcsrc=
 option[26]: -Msyslibs=/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/pli.a -ldl 
 option[27]: -l
 option[28]: vcs_comp.log
 option[29]: -full64
 option[30]: -debug_access+all
 option[31]: +vpi
 option[32]: +vcsd1
 option[33]: +itf+/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcsdp_lite.tab
 option[34]: -timescale=1ns/1ps
 option[35]: -kdb
 option[36]: -Xufe=2steps
 option[37]: -lca
 option[38]: +define+DEBUG_BFM
 option[39]: +define+DUMP_FSDB
 option[40]: -top
 option[41]: tb_sideband_out
 option[42]: -o
 option[43]: simv
 option[44]: -picarchive
 option[45]: -P
 option[46]: /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/verdi.tab
 option[47]: -fsdb
 option[48]: -sverilog
 option[49]: +vpi
 option[50]: -gen_obj
 option[51]: +incdir+../../../../src
 option[52]: +incdir+../../../nonblocking/src/sv
 option[53]: ../../../../src/sideband_out_bfm.sv
 option[54]: ../../../../src/fifo.sv
 option[55]: ../../../nonblocking/src/sv/tb_sideband_out.sv
 option[56]: -load
 option[57]: /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/libnovas.so:FSDBDumpCmd
 option[58]: timescale=1ns/1ps
Chronologic Simulation VCS Release V-2023.12-SP2_Full64
Linux 3.10.0-1160.119.1.el7.x86_64 #1 SMP Tue Jun 4 14:43:51 UTC 2024 x86_64
CPU cores: 8
Limit information:
======================================
cputime		unlimited
filesize		unlimited
datasize		unlimited
stacksize		8192 kbytes
coredumpsize		0 kbytes
memoryuse		unlimited
vmemoryuse		unlimited
descriptors		4096
memorylocked		64 kbytes
maxproc		4096
======================================
(Special)Runtime environment variables:

Runtime environment variables:
SOURCE_ABSEIL=1
GREP_COLOR=37;45
XDG_VTNR=1
MANPATH=/opt/rh/devtoolset-11/root/usr/share/man
_ZO_DATA_DIR=/home/<USER>/.config/zoxide
SOURCE_VERIBLE=1
XSEL_HOME=/home/<USER>/xsel
LESS_TERMCAP_mb=[1;31m
SSH_AGENT_PID=2883
XDG_SESSION_ID=1
ZLIB_HOME=/home/<USER>/zlib
NOVAS_SYNC_MOTIF_DISP=
FLATBUFFERS_HOME=/home/<USER>/flatbuffers
SOURCE_GO=1
SOURCE_BEAR=1
LESS_TERMCAP_md=[1;31m
IMSETTINGS_INTEGRATE_DESKTOP=yes
ONIGURUMA_HOME=/home/<USER>/oniguruma
SOURCE_LIBTOOL=1
SOURCE_IMLIB2=1
SOURCE_GTEST=1
LESS_TERMCAP_me=[0m
VIM_HOME=/home/<USER>/vim
SOURCE_DOXYGEN=1
TERM=gnome-256color
SHELL=/home/<USER>/zsh/bin/zsh
XDG_MENU_PREFIX=gnome-
VTE_VERSION=5204
SOURCE_CMAKE=1
RIPGREP_CONFIG_PATH=/home/<USER>/.ripgreprc
LUA_HOME=/home/<USER>/lua
SOURCE_AUTOMAKE=1
SOURCE_AUTOCONF=1
SPS_FONT_PATH=/opt/synopsys/verdi/V-2023.12-SP2/font
GNOME_TERMINAL_SCREEN=/org/gnome/Terminal/screen/806a719e_8871_42e0_8c9b_d686a19754e0
VCS_STACK_EXEC=true
AUTOMAKE_HOME=/home/<USER>/automake
SPS_XFONT_PATH=/opt/synopsys/verdi/V-2023.12-SP2/XFont
SOURCE_SYSTEMC=1
SOURCE_FLATBUFFERS=1
VCS_PATHMAP_PRELOAD_DONE=1
LESS_TERMCAP_ue=[0m
SPDLOG_HOME=/home/<USER>/spdlog
AUTOCONF_HOME=/home/<USER>/autoconf
SOURCE_FEH=1
LC_ALL=C
GIT_EDITOR=vim
XCLIP_HOME=/home/<USER>/xclip
OPENSSL_HOME=/home/<USER>/openssl
SOURCE_LIBPSL=1
SOURCE_CURL=1
IMSETTINGS_MODULE=none
http_proxy=http://127.0.0.1:7890
PCP_DIR=/opt/rh/devtoolset-11/root
CMAKE_HOME=/home/<USER>/cmake
USER=harriszh
SOURCE_YODL=1
LD_LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib64:/opt/rh/devtoolset-11/root/usr/lib:/opt/rh/devtoolset-11/root/usr/lib64/dyninst:/opt/rh/devtoolset-11/root/usr/lib/dyninst:/home/<USER>/zsh/lib:/home/<USER>/zlib/lib:/home/<USER>/zeromq/lib:/home/<USER>/zeromq/lib64:/home/<USER>/tcl/lib:/home/<USER>/systemc/lib:/home/<USER>/spdlog/lib64:/home/<USER>/readline/lib:/home/<USER>/python/lib:/home/<USER>/pcre2/lib64:/home/<USER>/openssl/lib64:/home/<USER>/oniguruma/lib:/home/<USER>/node/lib:/home/<USER>/lua/lib:/home/<USER>/llvm/lib:/home/<USER>/libtool/lib:/home/<USER>/libsocket/lib:/home/<USER>/libpsl/lib:/home/<USER>/libiconv/lib:/home/<USER>/libevent/lib64:/home/<USER>/libb2/lib:/home/<USER>/iverilog/lib:/home/<USER>/imlib2/lib:/home/<USER>/gtest/lib64:/home/<USER>/graphviz/lib:/home/<USER>/go/lib:/home/<USER>/global/lib:/home/<USER>/giflib/lib:/home/<USER>/gettext/lib:/home/<USER>/flatbuffers/lib64:/home/<USER>/dtc/lib:/home/<USER>/curl/lib64:/home/<USER>/bear/lib64:/home/<USER>/abseil/lib64:/home/<USER>/.local/lib64:/home/<USER>/.local/lib:/usr/local/lib:/lib64:/usr/lib64:/usr/lib:/lib
LS_COLORS=di=1;34:ln=35:so=32:pi=33:ex=31:bd=1;36:cd=1;33:su=30;41:sg=30;46:tw=30;42:ow=30;43
VERIBLE_HOME=/home/<USER>/verible
GREP_COLORS=mt=37;45
GNOME_TERMINAL_SERVICE=:1.112
XNLSPATH=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/nls
NO_PLATFORM_REL_CHECK=1
FEH_HOME=/home/<USER>/feh
SOURCE_ZSH=1
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
MALLOC_ARENA_MAX=1
PUBLIC_TOOLS=/home/<USER>
SNPSLMD_LICENSE_FILE=27000@pc-harriszh2
SOURCE_PCRE2=1
USERNAME=harriszh
FZF_DEFAULT_OPTS=
  --bind='?:toggle-preview'
  --bind='ctrl-u:preview-page-up'
  --bind='ctrl-d:preview-page-down'
  --bind 'ctrl-/:change-preview-window(80%,border-bottom|hidden|)' 
  --bind 'ctrl-r:reload(fd --type f --follow --exclude .git --exclude \*.swp)'
  --bind 'ctrl-i:reload(fd --type f -I --follow --exclude .git --exclude \*.swp)'
  --bind 'ctrl-h:reload(fd --type f -I -H  --follow --exclude .git --exclude \*.swp)'
  --bind 'ctrl-o:execute(vim {})'
  --bind 'ctrl-w:reload(/home/<USER>/.cargo/bin/zoxide query --list)'
  --preview-window 'right:60%:wrap'
  --preview '/home/<USER>/.sh/fzf-bat.sh {}'
GETTEXT_HOME=/home/<USER>/gettext
DROPBEAR_HOME=/home/<USER>/dropbear
BITWISE_HOME=/home/<USER>/bitwise
PAGER=less
LIBICONV_HOME=/home/<USER>/libiconv
SOURCE_BITWISE=1
LESS_TERMCAP_us=[1;32m
ABSEIL_HOME=/home/<USER>/abseil
SOURCE_XCLIP=1
SOURCE_TMUX=1
SOURCE_LIBSOCKET=1
GNOME_SHELL_SESSION_MODE=classic
PATH=/opt/synopsys/vcs/V-2023.12-SP2/bin:/opt/synopsys/verdi/V-2023.12-SP2/bin:/opt/rh/devtoolset-11/root/usr/bin:/home/<USER>/git-extras/bin:/home/<USER>/zsh/bin:/home/<USER>/zeromq/bin:/home/<USER>/yodl/bin:/home/<USER>/xsel/bin:/home/<USER>/xclip/bin:/home/<USER>/vim/bin:/home/<USER>/verible/bin:/home/<USER>/tmux/bin:/home/<USER>/tcl/bin:/home/<USER>/readline/bin:/home/<USER>/python/bin:/home/<USER>/pcre2/bin:/home/<USER>/openssl/bin:/home/<USER>/oniguruma/bin:/home/<USER>/node/bin:/home/<USER>/make/bin:/home/<USER>/m4/bin:/home/<USER>/lua/bin:/home/<USER>/llvm/bin:/home/<USER>/libtool/bin:/home/<USER>/libpsl/bin:/home/<USER>/libiconv/bin:/home/<USER>/libevent/bin:/home/<USER>/iverilog/bin:/home/<USER>/imlib2/bin:/home/<USER>/graphviz/bin:/home/<USER>/gperf/bin:/home/<USER>/go/bin:/home/<USER>/global/bin:/home/<USER>/git/bin:/home/<USER>/giflib/bin:/home/<USER>/gettext/bin:/home/<USER>/flatbuffers/bin:/home/<USER>/feh/bin:/home/<USER>/dtc/bin:/home/<USER>/dropbear/bin:/home/<USER>/dropbear/sbin:/home/<USER>/doxygen/bin:/home/<USER>/curl/bin:/home/<USER>/ctags/bin:/home/<USER>/cmake/bin:/home/<USER>/bitwise/bin:/home/<USER>/bear/bin:/home/<USER>/automake/bin:/home/<USER>/autoconf/bin:/home/<USER>/local/bin:/home/<USER>/.cargo/bin:/trunk/go/bin:/usr/local/bin:/usr/local/sbin:/usr/bin:/usr/sbin:/bin:/sbin:/home/<USER>/.fzf/bin
DESKTOP_SESSION=gnome-classic
YODL_HOME=/home/<USER>/yodl
SOURCE_LIBICONV=1
CARGO_HOME=/home/<USER>/.cargo
M4_HOME=/home/<USER>/m4
QT_IM_MODULE=ibus
VERDI_HOME=/opt/synopsys/verdi/V-2023.12-SP2
GO_HOME=/home/<USER>/go
XDG_SESSION_TYPE=x11
PWD=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs
VCS_HOME=/opt/synopsys/vcs/V-2023.12-SP2
FZF_ALT_C_COMMAND=fd --type d --hidden --follow --exclude .git --exclude \*.swp
SOURCE_ZEROMQ=1
XMODIFIERS=@im=none
EDITOR=vim
BAT_STYLE=full
LIBEVENT_HOME=/home/<USER>/libevent
SOURCE_GLOBAL=1
SYS_PROG_NAME=verdi
LIBPSL_HOME=/home/<USER>/libpsl
SOURCE_READLINE=1
LANG=C
GDM_LANG=en_US.UTF-8
SYSTEMC_HOME=/home/<USER>/systemc
READLINE_HOME=/home/<USER>/readline
LIBSOCKET_HOME=/home/<USER>/libsocket
GPERF_HOME=/home/<USER>/gperf
SOURCE_PYTHON=1
SOURCE_LUA=1
SYS_INST_DIR=/opt/synopsys/verdi/V-2023.12-SP2
https_proxy=http://127.0.0.1:7890
LM_LICENSE_FILE=5280@127.0.0.1
GDMSESSION=gnome-classic
GRAPHVIZ_HOME=/home/<USER>/graphviz
GIFLIB_HOME=/home/<USER>/giflib
SOURCE_GETTEXT=1
MAKE_HOME=/home/<USER>/make
CURL_HOME=/home/<USER>/curl
MANPAGER=sh -c 'col -bx | bat -l man -p --paging always'
LIBB2_HOME=/home/<USER>/libb2
IVERILOG_HOME=/home/<USER>/iverilog
SOURCE_M4=1
XDG_SEAT=seat0
HOME=/home/<USER>
SHLVL=1
VERDI_LIB=/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64
DOXYGEN_HOME=/home/<USER>/doxygen
SOURCE_CTAGS=1
VCS_EXEC_DONE=1
LIBTOOL_HOME=/home/<USER>/libtool
SOURCE_LIBB2=1
VERDI_ORIGNAL_LD_LIBRARY_PATH=/opt/rh/devtoolset-11/root/usr/lib64:/opt/rh/devtoolset-11/root/usr/lib:/opt/rh/devtoolset-11/root/usr/lib64/dyninst:/opt/rh/devtoolset-11/root/usr/lib/dyninst:/home/<USER>/zsh/lib:/home/<USER>/zlib/lib:/home/<USER>/zeromq/lib:/home/<USER>/zeromq/lib64:/home/<USER>/tcl/lib:/home/<USER>/systemc/lib:/home/<USER>/spdlog/lib64:/home/<USER>/readline/lib:/home/<USER>/python/lib:/home/<USER>/pcre2/lib64:/home/<USER>/openssl/lib64:/home/<USER>/oniguruma/lib:/home/<USER>/node/lib:/home/<USER>/lua/lib:/home/<USER>/llvm/lib:/home/<USER>/libtool/lib:/home/<USER>/libsocket/lib:/home/<USER>/libpsl/lib:/home/<USER>/libiconv/lib:/home/<USER>/libevent/lib64:/home/<USER>/libb2/lib:/home/<USER>/iverilog/lib:/home/<USER>/imlib2/lib:/home/<USER>/gtest/lib64:/home/<USER>/graphviz/lib:/home/<USER>/go/lib:/home/<USER>/global/lib:/home/<USER>/giflib/lib:/home/<USER>/gettext/lib:/home/<USER>/flatbuffers/lib64:/home/<USER>/dtc/lib:/home/<USER>/curl/lib64:/home/<USER>/bear/lib64:/home/<USER>/abseil/lib64:/home/<USER>/.local/lib64:/home/<USER>/.local/lib:/usr/local/lib:/lib64:/usr/lib64:/usr/lib:/lib
FZF_DEFAULT_COMMAND2=fd --type f -I --follow --exclude .git --exclude \*.swp
TMUX_HOME=/home/<USER>/tmux
BEAR_HOME=/home/<USER>/bear
SOURCE_OPENSSL=1
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
FZF_DEFAULT_COMMAND3=fd --type f -I -H  --follow --exclude .git --exclude \*.swp
TCL_HOME=/home/<USER>/tcl
SOURCE_SPDLOG=1
SOURCE_GRAPHVIZ=1
GIT_HOME=/home/<USER>/git
SOURCE_GIT=1
ZSH_HOME=/home/<USER>/zsh
SOURCE_ONIGURUMA=1
LESS=--chop-long-lines --ignore-case --jump-target=4 --LONG-PROMPT --no-init --quit-if-one-screen --RAW-CONTROL-CHARS -R
XDG_SESSION_DESKTOP=gnome-classic
LOGNAME=harriszh
BATPIPE=color
FZF_CTRL_T_COMMAND=fd --type f --type d --hidden --follow --exclude .git --exclude \*.swp
GLOBAL_HOME=/home/<USER>/global
SOURCE_LLVM=1
SOURCE_GIFLIB=1
XDG_DATA_DIRS=/home/<USER>/.local/share/flatpak/exports/share/:/var/lib/flatpak/exports/share/:/usr/local/share/:/usr/share/
DBUS_SESSION_BUS_ADDRESS=unix:abstract=/tmp/dbus-jrCY1UuLJL,guid=47f535f9f7a5101a64d402b06877357b
NOVASHLPPATH=/opt/synopsys/verdi/V-2023.12-SP2/doc
SPS_RGB_PATH=/opt/synopsys/verdi/V-2023.12-SP2/etc/rgb
CDS_LIC_FILE=5280@127.0.0.1
FZF_DEFAULT_COMMAND=fd --type f --follow --exclude .git --exclude \*.swp
ZEROMQ_HOME=/home/<USER>/zeromq
CTAGS_HOME=/home/<USER>/ctags
LESSOPEN=|/home/<USER>/local/bin/batpipe %s
DTC_HOME=/home/<USER>/dtc
PKG_CONFIG_PATH=/opt/rh/devtoolset-11/root/usr/lib64/pkgconfig:/home/<USER>/zlib/lib/pkgconfig:/home/<USER>/zeromq/lib/pkgconfig:/home/<USER>/zeromq/lib64/pkgconfig:/home/<USER>/tcl/lib/pkgconfig:/home/<USER>/systemc/lib/pkgconfig:/home/<USER>/spdlog/lib64/pkgconfig:/home/<USER>/readline/lib/pkgconfig:/home/<USER>/python/lib/pkgconfig:/home/<USER>/pcre2/lib64/pkgconfig:/home/<USER>/openssl/lib64/pkgconfig:/home/<USER>/oniguruma/lib/pkgconfig:/home/<USER>/libsocket/lib/pkgconfig:/home/<USER>/libpsl/lib/pkgconfig:/home/<USER>/libiconv/lib/pkgconfig:/home/<USER>/libevent/lib64/pkgconfig:/home/<USER>/libb2/lib/pkgconfig:/home/<USER>/imlib2/lib/pkgconfig:/home/<USER>/gtest/lib64/pkgconfig:/home/<USER>/graphviz/lib/pkgconfig:/home/<USER>/flatbuffers/lib64/pkgconfig:/home/<USER>/curl/lib64/pkgconfig:/home/<USER>/abseil/lib64/pkgconfig:/home/<USER>/.local/lib64/pkgconfig:/home/<USER>/.local/lib/pkgconfig:/home/<USER>/.local/vte/lib64/pkgconfig:
GIT_EXTRAS_HOME=/home/<USER>/git-extras
INFOPATH=/opt/rh/devtoolset-11/root/usr/share/info
ACLOCAL_PATH=/home/<USER>/libtool/share/aclocal:/usr/share/aclocal
SOURCE_ZLIB=1
WINDOWPATH=1
LD_NOVERSION=1
XDG_RUNTIME_DIR=/run/user/1000
DISPLAY=:0
QT_PLUGIN_PATH=/opt/synopsys/verdi/V-2023.12-SP2/platform/LINUXAMD64/lib/Qt/plugins
BAT_THEME=gruvbox-dark
LLVM_HOME=/home/<USER>/llvm
GTEST_HOME=/home/<USER>/gtest
SOURCE_TCL=1
SOURCE_DROPBEAR=1
BITMODE=64
PYTHON_HOME=/home/<USER>/python
PCRE2_HOME=/home/<USER>/pcre2
SOURCE_MAKE=1
SOURCE_GPERF=1
XDG_CURRENT_DESKTOP=GNOME-Classic:GNOME
SOURCE_LIBEVENT=1
SOURCE_DTC=1
NODE_HOME=/home/<USER>/node
SOURCE_XSEL=1
SOURCE_VIM=1
SOURCE_NODE=1
IMLIB2_HOME=/home/<USER>/imlib2
SOURCE_IVERILOG=1
COLORTERM=24bit
XAUTHORITY=/run/gdm/auth-for-harriszh-idrDvM/database
NOVAS_LC_ALL=C
SIMV_DAIDIR_PATH=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir
NOVAS_SIGNAL_BASE_EXTRACTION=1
SIGNAL_BASE_EXTRACTION=1
NOVAS_VERDI_SVTB_BETA=1
VERDI_SVTB_BETA=1
NOVAS_VERDI_SVTB_ALPHA=1
VERDI_SVTB_ALPHA=1
NOVAS_VERDI_TB_HT=1
VERDI_TB_HT=1
NOVAS_WAVE_REDRAW_ALLVC=1
WAVE_REDRAW_ALLVC=1
NOVAS_TCL_LIBRARY=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/tcl86_library
TCL_LIBRARY=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/tcl86_library
XKEYSYMDB=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/XKeysymDB2.1
XLOCALEDIR=/opt/synopsys/verdi/V-2023.12-SP2/etc/access/locale
QTWEBENGINE_CHROMIUM_FLAGS=--log-level=3
NOVAS_SIGNAL_BASED_BA=0
SIGNAL_BASED_BA=0
SNPS_SIM_DEFAULT_GUI=verdi
FSDB_FILE=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
TBSIM_SIM_LOG_PATH=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/vcs.log
VCS_UCLI_STDIN_BLOCKING=1
FSDB_VHDL_PROTECTED=1
FSDB_RD_IR_ENABLE=1
FSDB_SVA_STATUS=1
VCS_STOP_SAFE=1
DVE_SIM_SELECT_LOOP=on
FLEXLM_BORROWFILE=/home/<USER>/.pc-harriszh2-borrow.txt
Runtime command line arguments:
argv[0]=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv
argv[1]=-sv_lib
argv[2]=../../../nonblocking/pseudo_bridge_nonblocking_basic
argv[3]=+UVM_VERDI_TRACE=UVM_AWARE
argv[4]=+fsdb+gate=off
argv[5]=-ucli2Proc
argv[6]=-ucli
argv[7]=-l
argv[8]=/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/sim.log
405 profile - 100
          CPU/Mem usage: 0.020 sys,  0.070 user,  290.56M mem
406 Elapsed time:    0:00:02    Sun Jul 20 23:17:58 2025
407 User CPU time used: 0 seconds
408 System CPU time used: 0 seconds
409 pliAppInit
410 ndpGetenv(FSDB_FILE): /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
411 ndpGetenv(FSDB_SVA_STATUS): 1
412 ndpGetenv(FSDB_VHDL_PROTECTED): 1
413 [SC]Init: MemoryConsumed=0.00MB ElapsedTime=0hour 0min. 0sec.
414 FSDB_GATE & FSDB_RTL is disabled.
415 FSDB_PACKED_MODE is enabled.
416 FSDB_GATE_PACKED is enabled.
417 FSDB_ALIAS_ARRAY_ELEM is set to 4294967295.
418 Enable Parallel Dumping.
419 pliAppMiscSet: New Sim Round
420 pliEntryInit
421 LIBSSCORE=found /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/lib/LINUXAMD64/libsscore_vcs202312.so through $NOVAS_HOME setting.
422 FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
423 (C) 1996 - 2024 by Synopsys, Inc.
424 sps_tcl_fsdbDumpfile_main at 0
425 argv[0]: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
426 *Verdi* : Create FSDB file '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb'
427 [spi_vcs_vd_ppi_create_root]: no upf option
428 compile option from '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir/vcs_rebuild'.
429   "vcs '-full64' '-sverilog' '-debug_access+all' '-timescale=1ns/1ps' '-kdb' '-lca' '+vpi' '+define+DEBUG_BFM' '+define+DUMP_FSDB' '+incdir+../../../../src' '+incdir+../../../nonblocking/src/sv' '../../../../src/sideband_out_bfm.sv' '../../../../src/fifo.sv' '../../../nonblocking/src/sv/tb_sideband_out.sv' '-top' 'tb_sideband_out' '-o' 'simv' '-l' 'vcs_comp.log' '-CFLAGS' '-g -I../../../nonblocking/src/cpp' 2>&1"
430 sps_tcl_fsdbDumpflush_vd_main at 0 : N/A(0)
431 *Verdi* : Flush all FSDB Files at 0 ps.
432 sps_tcl_fsdbDumpflush_vd_main at 0 : N/A(0)
433 *Verdi* : Flush all FSDB Files at 0 ps.
434 DVDI_is_vir_unload_enabled is enable
435 FSDB_VCS_ENABLE_NATIVE_VC is enable
436 sps_tcl_fsdbDumpvars_vd_main
437 argv[0]: 1
438 argv[1]: tb_sideband_out.u_bfm.clk
439 argv[2]: +all
440 argv[3]: +trace_process
441 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.clk).
442 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.100 sys,  0.110 user,  390.48M mem
                   incr: 0.000 sys,  0.000 user,  2.26M mem
                   accu: 0.000 sys,  0.000 user,  2.26M mem
              accu incr: 0.000 sys,  0.000 user,  2.26M mem

          Count usage: 0 var,  2 idcode,  1 callback
                 incr: 0 var,  2 idcode,  1 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  2 idcode,  1 callback
443 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
444 User CPU time used: 0 seconds
445 System CPU time used: 0 seconds
446 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.100 sys,  0.110 user,  391.60M mem
                   incr: 0.000 sys,  0.000 user,  1.12M mem
                   accu: 0.000 sys,  0.000 user,  3.38M mem
              accu incr: 0.000 sys,  0.000 user,  1.12M mem

          Count usage: 0 var,  2 idcode,  1 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
447 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
448 User CPU time used: 0 seconds
449 System CPU time used: 0 seconds
450 sps_tcl_fsdbDumpvars_vd_main
451 argv[0]: 1
452 argv[1]: tb_sideband_out.u_bfm.rst_n
453 argv[2]: +all
454 argv[3]: +trace_process
455 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.rst_n).
456 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.100 sys,  0.110 user,  391.60M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  4 idcode,  2 callback
                 incr: 0 var,  2 idcode,  1 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  2 idcode,  1 callback
457 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
458 User CPU time used: 0 seconds
459 System CPU time used: 0 seconds
460 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.100 sys,  0.110 user,  391.60M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  4 idcode,  2 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  2 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
461 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
462 User CPU time used: 0 seconds
463 System CPU time used: 0 seconds
464 sps_tcl_fsdbDumpvars_vd_main
465 argv[0]: 1
466 argv[1]: tb_sideband_out.u_bfm.sideband_in
467 argv[2]: +all
468 argv[3]: +trace_process
469 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in).
470 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.100 sys,  0.110 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  1.07M mem
                   accu: 0.000 sys,  0.000 user,  1.07M mem
              accu incr: 0.000 sys,  0.000 user,  1.07M mem

          Count usage: 0 var,  5 idcode,  3 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
471 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
472 User CPU time used: 0 seconds
473 System CPU time used: 0 seconds
474 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.100 sys,  0.110 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  1.07M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  5 idcode,  3 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
475 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
476 User CPU time used: 0 seconds
477 System CPU time used: 0 seconds
478 sps_tcl_fsdbDumpvars_vd_main
479 argv[0]: 1
480 argv[1]: tb_sideband_out.u_bfm.INSTANCE_ID
481 argv[2]: +all
482 argv[3]: +trace_process
483 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.INSTANCE_ID).
484 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.110 user,  392.68M mem
                   incr: 0.010 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.010 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  6 idcode,  3 callback
                 incr: 0 var,  1 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  1 idcode,  0 callback
485 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
486 User CPU time used: 0 seconds
487 System CPU time used: 0 seconds
488 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.110 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  6 idcode,  3 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
489 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
490 User CPU time used: 0 seconds
491 System CPU time used: 0 seconds
492 sps_tcl_fsdbDumpvars_vd_main
493 argv[0]: 1
494 argv[1]: tb_sideband_out.u_bfm.NUM_SIGNALS
495 argv[2]: +all
496 argv[3]: +trace_process
497 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.NUM_SIGNALS).
498 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.110 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  7 idcode,  3 callback
                 incr: 0 var,  1 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  1 idcode,  0 callback
499 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
500 User CPU time used: 0 seconds
501 System CPU time used: 0 seconds
502 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.110 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  7 idcode,  3 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
503 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
504 User CPU time used: 0 seconds
505 System CPU time used: 0 seconds
506 sps_tcl_fsdbDumpvars_vd_main
507 argv[0]: 1
508 argv[1]: tb_sideband_out.u_bfm.DELAY_WIDTH
509 argv[2]: +all
510 argv[3]: +trace_process
511 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.DELAY_WIDTH).
512 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.110 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  7 idcode,  3 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  0 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
513 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
514 User CPU time used: 0 seconds
515 System CPU time used: 0 seconds
516 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.110 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  7 idcode,  3 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  0 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
517 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
518 User CPU time used: 0 seconds
519 System CPU time used: 0 seconds
520 sps_tcl_fsdbDumpvars_vd_main
521 argv[0]: 1
522 argv[1]: tb_sideband_out.u_bfm.QUEUE_DEPTH
523 argv[2]: +all
524 argv[3]: +trace_process
525 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.QUEUE_DEPTH).
526 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.010 user,  0.00M mem
                   accu: 0.000 sys,  0.010 user,  0.00M mem
              accu incr: 0.000 sys,  0.010 user,  0.00M mem

          Count usage: 0 var,  8 idcode,  3 callback
                 incr: 0 var,  1 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  1 idcode,  0 callback
527 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
528 User CPU time used: 0 seconds
529 System CPU time used: 0 seconds
530 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.010 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  8 idcode,  3 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  0 callback
            accu incr: 0 var,  0 idcode,  0 callback
531 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
532 User CPU time used: 0 seconds
533 System CPU time used: 0 seconds
534 sps_tcl_fsdbDumpvars_vd_main
535 argv[0]: 1
536 argv[1]: tb_sideband_out.u_bfm.configured_delay
537 argv[2]: +all
538 argv[3]: +trace_process
539 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.configured_delay).
540 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  9 idcode,  4 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
541 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
542 User CPU time used: 0 seconds
543 System CPU time used: 0 seconds
544 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  9 idcode,  4 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
545 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
546 User CPU time used: 0 seconds
547 System CPU time used: 0 seconds
548 sps_tcl_fsdbDumpvars_vd_main
549 argv[0]: 1
550 argv[1]: tb_sideband_out.u_bfm.sideband_in_d
551 argv[2]: +all
552 argv[3]: +trace_process
553 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in_d).
554 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  10 idcode,  5 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
555 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
556 User CPU time used: 0 seconds
557 System CPU time used: 0 seconds
558 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  10 idcode,  5 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
559 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
560 User CPU time used: 0 seconds
561 System CPU time used: 0 seconds
562 sps_tcl_fsdbDumpvars_vd_main
563 argv[0]: 1
564 argv[1]: tb_sideband_out.u_bfm.proc_state
565 argv[2]: +all
566 argv[3]: +trace_process
567 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.proc_state).
568 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  11 idcode,  6 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
569 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
570 User CPU time used: 0 seconds
571 System CPU time used: 0 seconds
572 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  11 idcode,  6 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
573 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
574 User CPU time used: 0 seconds
575 System CPU time used: 0 seconds
576 sps_tcl_fsdbDumpvars_vd_main
577 argv[0]: 1
578 argv[1]: tb_sideband_out.u_bfm.delay_counter
579 argv[2]: +all
580 argv[3]: +trace_process
581 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.delay_counter).
582 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  12 idcode,  7 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
583 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
584 User CPU time used: 0 seconds
585 System CPU time used: 0 seconds
586 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  12 idcode,  7 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
587 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
588 User CPU time used: 0 seconds
589 System CPU time used: 0 seconds
590 sps_tcl_fsdbDumpvars_vd_main
591 argv[0]: 1
592 argv[1]: tb_sideband_out.u_bfm.is_blocking_mode
593 argv[2]: +all
594 argv[3]: +trace_process
595 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.is_blocking_mode).
596 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  13 idcode,  8 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
597 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
598 User CPU time used: 0 seconds
599 System CPU time used: 0 seconds
600 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  13 idcode,  8 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
601 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
602 User CPU time used: 0 seconds
603 System CPU time used: 0 seconds
604 sps_tcl_fsdbDumpvars_vd_main
605 argv[0]: 1
606 argv[1]: tb_sideband_out.u_bfm.current_transition
607 argv[2]: +all
608 argv[3]: +trace_process
609 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.current_transition).
610 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  14 idcode,  9 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
611 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
612 User CPU time used: 0 seconds
613 System CPU time used: 0 seconds
614 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  14 idcode,  9 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
615 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
616 User CPU time used: 0 seconds
617 System CPU time used: 0 seconds
618 sps_tcl_fsdbDumpvars_vd_main
619 argv[0]: 1
620 argv[1]: tb_sideband_out.u_bfm.fifo_wr_en
621 argv[2]: +all
622 argv[3]: +trace_process
623 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_en).
624 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  15 idcode,  10 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
625 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
626 User CPU time used: 0 seconds
627 System CPU time used: 0 seconds
628 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  15 idcode,  10 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
629 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
630 User CPU time used: 0 seconds
631 System CPU time used: 0 seconds
632 sps_tcl_fsdbDumpvars_vd_main
633 argv[0]: 1
634 argv[1]: tb_sideband_out.u_bfm.fifo_rd_en
635 argv[2]: +all
636 argv[3]: +trace_process
637 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_en).
638 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  16 idcode,  11 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
639 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
640 User CPU time used: 0 seconds
641 System CPU time used: 0 seconds
642 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  16 idcode,  11 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
643 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
644 User CPU time used: 0 seconds
645 System CPU time used: 0 seconds
646 sps_tcl_fsdbDumpvars_vd_main
647 argv[0]: 1
648 argv[1]: tb_sideband_out.u_bfm.fifo_full
649 argv[2]: +all
650 argv[3]: +trace_process
651 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_full).
652 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  17 idcode,  12 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
653 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
654 User CPU time used: 0 seconds
655 System CPU time used: 0 seconds
656 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  17 idcode,  12 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
657 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
658 User CPU time used: 0 seconds
659 System CPU time used: 0 seconds
660 sps_tcl_fsdbDumpvars_vd_main
661 argv[0]: 1
662 argv[1]: tb_sideband_out.u_bfm.fifo_empty
663 argv[2]: +all
664 argv[3]: +trace_process
665 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_empty).
666 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  18 idcode,  13 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
667 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
668 User CPU time used: 0 seconds
669 System CPU time used: 0 seconds
670 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.110 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  18 idcode,  13 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
671 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
672 User CPU time used: 0 seconds
673 System CPU time used: 0 seconds
674 sps_tcl_fsdbDumpvars_vd_main
675 argv[0]: 1
676 argv[1]: tb_sideband_out.u_bfm.fifo_wr_data
677 argv[2]: +all
678 argv[3]: +trace_process
679 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_data).
680 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.120 sys,  0.120 user,  392.68M mem
                   incr: 0.010 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.010 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  19 idcode,  14 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
681 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
682 User CPU time used: 0 seconds
683 System CPU time used: 0 seconds
684 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.120 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  19 idcode,  14 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
685 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
686 User CPU time used: 0 seconds
687 System CPU time used: 0 seconds
688 sps_tcl_fsdbDumpvars_vd_main
689 argv[0]: 1
690 argv[1]: tb_sideband_out.u_bfm.fifo_rd_data
691 argv[2]: +all
692 argv[3]: +trace_process
693 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_data).
694 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.120 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  20 idcode,  15 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
695 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
696 User CPU time used: 0 seconds
697 System CPU time used: 0 seconds
698 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.120 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  20 idcode,  15 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
699 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
700 User CPU time used: 0 seconds
701 System CPU time used: 0 seconds
702 sps_tcl_fsdbDumpvars_vd_main
703 argv[0]: 1
704 argv[1]: tb_sideband_out.u_bfm.vector_changed
705 argv[2]: +all
706 argv[3]: +trace_process
707 *Verdi* : Dumping the signal (tb_sideband_out.u_bfm.vector_changed).
708 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.120 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  21 idcode,  16 callback
                 incr: 0 var,  1 idcode,  1 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  1 idcode,  1 callback
709 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
710 User CPU time used: 0 seconds
711 System CPU time used: 0 seconds
712 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.120 sys,  0.120 user,  392.68M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.000 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 0 var,  21 idcode,  16 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 0 var,  1 idcode,  1 callback
            accu incr: 0 var,  0 idcode,  0 callback
713 Elapsed time:    0:00:09    Sun Jul 20 23:18:05 2025
714 User CPU time used: 0 seconds
715 System CPU time used: 0 seconds
716 sps_tcl_fsdbDumpflush_vd_main at 0 : N/A(0)
717 *Verdi* : Flush all FSDB Files at 0 ps.
718 sps_call_fsdbDumpvars_vd_main at 0 : ../../../nonblocking/src/sv/tb_sideband_out.sv(101)
719 argv[0]: (0)
720 argv[1]: (handle) tb_sideband_out
721 *Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
722 *Verdi* : End of traversing.
723 pliAppHDL_DumpVarComplete traverse var: profile - 
          CPU/Mem usage: 0.250 sys,  0.140 user,  394.51M mem
                   incr: 0.010 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.010 sys,  0.000 user,  0.00M mem

          Count usage: 27 var,  35 idcode,  29 callback
                 incr: 27 var,  14 idcode,  13 callback
                 accu: 27 var,  14 idcode,  13 callback
            accu incr: 27 var,  14 idcode,  13 callback
724 Elapsed time:    0:00:10    Sun Jul 20 23:18:06 2025
725 User CPU time used: 0 seconds
726 System CPU time used: 0 seconds
727 pliAppHDL_DumpVarComplete: profile - 
          CPU/Mem usage: 0.250 sys,  0.140 user,  394.51M mem
                   incr: 0.000 sys,  0.000 user,  0.00M mem
                   accu: 0.010 sys,  0.000 user,  0.00M mem
              accu incr: 0.000 sys,  0.000 user,  0.00M mem

          Count usage: 27 var,  35 idcode,  29 callback
                 incr: 0 var,  0 idcode,  0 callback
                 accu: 27 var,  14 idcode,  13 callback
            accu incr: 0 var,  0 idcode,  0 callback
728 Elapsed time:    0:00:10    Sun Jul 20 23:18:06 2025
729 User CPU time used: 0 seconds
730 System CPU time used: 0 seconds
731 sps_call_fsdbDumpon_vd_main at 0 : ../../../nonblocking/src/sv/tb_sideband_out.sv(102)
732 *Verdi* : fsdbDumpon - All FSDB files at 0 ps.
733 sps_tcl_fsdbDumpflush_vd_main at 475000 : N/A(0)
734 *Verdi* : Flush all FSDB Files at 475,000 ps.
