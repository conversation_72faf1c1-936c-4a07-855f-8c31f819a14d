simSetSimulator "-vcssv" -exec \
           "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv" -args \
           "-sv_lib ../../../nonblocking/pseudo_bridge_nonblocking_basic -l vcs.log" \
           -uvmDebug on -simDelim
debImport "-i" "-simflow" "-dbdir" \
          "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir"
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcTBInvokeSim
verdiSetActWin -dock widgetDock_<Member>
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
srcSignalViewSelect "tb_sideband_out.u_bfm.clk"
verdiSetActWin -dock widgetDock_<Signal_List>
srcSignalViewSelect "tb_sideband_out.u_bfm.clk" "tb_sideband_out.u_bfm.rst_n" \
           "tb_sideband_out.u_bfm.sideband_in\[7:0\]" \
           "tb_sideband_out.u_bfm.INSTANCE_ID" \
           "tb_sideband_out.u_bfm.NUM_SIGNALS" \
           "tb_sideband_out.u_bfm.DELAY_WIDTH" \
           "tb_sideband_out.u_bfm.QUEUE_DEPTH" \
           "tb_sideband_out.u_bfm.configured_delay\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_in_d\[7:0\]" \
           "tb_sideband_out.u_bfm.proc_state\[1:0\]" \
           "tb_sideband_out.u_bfm.delay_counter\[7:0\]" \
           "tb_sideband_out.u_bfm.is_blocking_mode" \
           "tb_sideband_out.u_bfm.current_transition" \
           "tb_sideband_out.u_bfm.fifo_wr_en" \
           "tb_sideband_out.u_bfm.fifo_rd_en" \
           "tb_sideband_out.u_bfm.fifo_full" \
           "tb_sideband_out.u_bfm.fifo_empty" \
           "tb_sideband_out.u_bfm.fifo_wr_data" \
           "tb_sideband_out.u_bfm.fifo_rd_data" \
           "tb_sideband_out.u_bfm.vector_changed"
wvCreateWindow
srcSignalViewAddSelectedToWave -clipboard
wvDrop -win $_nWave3
srcTBRunSim
wvSetCursor -win $_nWave3 474295.009980 -snap {("G1" 8)}
wvZoomAll -win $_nWave3
verdiDockWidgetSetCurTab -dock widgetDock_<Message>
verdiDockWidgetSetCurTab -dock windowDock_InteractiveConsole_2
verdiSetActWin -win $_InteractiveConsole_2
verdiDockWidgetSetCurTab -dock windowDock_nWave_3
verdiSetActWin -win $_nWave3
wvScrollUp -win $_nWave3 5
wvSelectSignal -win $_nWave3 {( "G1" 3 )} 
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
wvScrollDown -win $_nWave3 1
verdiSetActWin -win $_nWave3
wvSetCursor -win $_nWave3 74452.220559 -snap {("G1" 14)}
wvSetCursor -win $_nWave3 185771.457086 -snap {("G1" 14)}
srcDeselectAll -win $_nTrace1
srcSelect -signal "current_transition" -line 85 -pos 1 -win $_nTrace1
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
wvSelectSignal -win $_nWave3 {( "G1" 13 )} 
verdiSetActWin -win $_nWave3
wvSelectSignal -win $_nWave3 {( "G1" 13 )} 
wvSetPosition -win $_nWave3 {("G1" 13)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 22)}
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvSetCursor -win $_nWave3 90020.524458 -snap {("G1" 10)}
wvSetCursor -win $_nWave3 207095.089511 -snap {("G1" 10)}
wvSetCursor -win $_nWave3 215474.659607 -snap {("G1" 10)}
wvSetCursor -win $_nWave3 223614.813414 -snap {("G1" 10)}
wvSetCursor -win $_nWave3 235106.795260 -snap {("G1" 10)}
wvSetCursor -win $_nWave3 244922.863086 -snap {("G1" 10)}
