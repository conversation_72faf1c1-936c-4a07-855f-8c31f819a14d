simSetSimulator "-vcssv" -exec \
           "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv" -args \
           "-sv_lib ../../../nonblocking/pseudo_bridge_nonblocking -l vcs.log" \
           -uvmDebug on -simDelim
debImport "-i" "-simflow" "-dbdir" \
          "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir"
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcTBInvokeSim
verdiSetActWin -dock widgetDock_<Member>
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSignalViewSelect "tb_sideband_out.u_bfm.clk"
verdiSetActWin -dock widgetDock_<Signal_List>
srcSignalViewSelect "tb_sideband_out.u_bfm.clk" "tb_sideband_out.u_bfm.rst_n" \
           "tb_sideband_out.u_bfm.sideband_in\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid\[7:0\]" \
           "tb_sideband_out.u_bfm.INSTANCE_ID" \
           "tb_sideband_out.u_bfm.NUM_SIGNALS" \
           "tb_sideband_out.u_bfm.DELAY_WIDTH" \
           "tb_sideband_out.u_bfm.channel_state\[7:0\]" \
           "tb_sideband_out.u_bfm.stored_signal_val\[7:0\]" \
           "tb_sideband_out.u_bfm.delay_counters\[7:0\]" \
           "tb_sideband_out.u_bfm.configured_delays\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_in_d\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid_d\[7:0\]" \
           "tb_sideband_out.u_bfm.is_blocking_mode" \
           "tb_sideband_out.u_bfm.sideband_in_edge\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid_edge\[7:0\]"
wvCreateWindow
srcSignalViewAddSelectedToWave -clipboard
wvDrop -win $_nWave3
srcTBRunSim
wvSetCursor -win $_nWave3 594395.393150 -snap {("G1" 12)}
wvZoomAll -win $_nWave3
wvScrollDown -win $_nWave3 0
wvSelectSignal -win $_nWave3 {( "G1" 11 )} 
wvSelectSignal -win $_nWave3 {( "G1" 11 )} 
wvSetPosition -win $_nWave3 {("G1" 11)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 24)}
wvSelectSignal -win $_nWave3 {( "G1" 11 )} 
wvSetPosition -win $_nWave3 {("G1" 11)}
wvCollapseBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 11)}
wvSetPosition -win $_nWave3 {("G1" 16)}
wvSelectSignal -win $_nWave3 {( "G1" 10 )} 
wvSetPosition -win $_nWave3 {("G1" 10)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 24)}
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvSelectSignal -win $_nWave3 {( "G1" 24 )} 
wvExpandBus -win $_nWave3
wvScrollDown -win $_nWave3 1
wvSelectSignal -win $_nWave3 {( "G1" 23 )} 
wvSetPosition -win $_nWave3 {("G1" 23)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 40)}
wvScrollUp -win $_nWave3 13
wvScrollUp -win $_nWave3 1
wvSelectSignal -win $_nWave3 {( "G1" 4 )} 
wvSetPosition -win $_nWave3 {("G1" 4)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 48)}
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvSetCursor -win $_nWave3 86680.545214 -snap {("G1" 11)}
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
verdiSetActWin -win $_nWave3
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollUp -win $_nWave3 1
debReload
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 10
wvSelectSignal -win $_nWave3 {( "G1" 16 )} 
wvSetPosition -win $_nWave3 {("G1" 16)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 56)}
wvScrollDown -win $_nWave3 1
wvSetCursor -win $_nWave3 75317.101219 -snap {("G1" 23)}
wvSetCursor -win $_nWave3 92832.706154 -snap {("G1" 23)}
wvSetCursor -win $_nWave3 143336.033716 -snap {("G1" 23)}
wvSetCursor -win $_nWave3 267696.828752 -snap {("G1" 22)}
wvSetCursor -win $_nWave3 368995.410624 -snap {("G1" 21)}
wvSetCursor -win $_nWave3 387386.795806 -snap {("G1" 21)}
srcTBInvokeSim
srcTBRunSim
wvSetCursor -win $_nWave3 588154.690619 -snap {("G1" 13)}
wvZoomAll -win $_nWave3
wvScrollDown -win $_nWave3 0
wvScrollUp -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
wvSelectSignal -win $_nWave3 {( "G1" 6 )} 
verdiSetActWin -win $_nWave3
wvSelectAll -win $_nWave3
wvCut -win $_nWave3
wvSetPosition -win $_nWave3 {("G2" 0)}
srcSignalViewSelect "tb_sideband_out.u_bfm.clk"
verdiSetActWin -dock widgetDock_<Signal_List>
srcSignalViewSelect "tb_sideband_out.u_bfm.clk" "tb_sideband_out.u_bfm.rst_n" \
           "tb_sideband_out.u_bfm.sideband_in\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid\[7:0\]" \
           "tb_sideband_out.u_bfm.INSTANCE_ID" \
           "tb_sideband_out.u_bfm.NUM_SIGNALS" \
           "tb_sideband_out.u_bfm.DELAY_WIDTH" \
           "tb_sideband_out.u_bfm.FIFO_DEPTH" \
           "tb_sideband_out.u_bfm.configured_delays\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid_d\[7:0\]" \
           "tb_sideband_out.u_bfm.proc_state\[1:0\]" \
           "tb_sideband_out.u_bfm.delay_counter\[7:0\]" \
           "tb_sideband_out.u_bfm.is_blocking_mode" \
           "tb_sideband_out.u_bfm.SIGNAL_IDX_WIDTH" \
           "tb_sideband_out.u_bfm.current_event" \
           "tb_sideband_out.u_bfm.fifo_wr_en" \
           "tb_sideband_out.u_bfm.fifo_rd_en" \
           "tb_sideband_out.u_bfm.fifo_full" \
           "tb_sideband_out.u_bfm.fifo_empty" \
           "tb_sideband_out.u_bfm.fifo_wr_data" \
           "tb_sideband_out.u_bfm.fifo_rd_data" \
           "tb_sideband_out.u_bfm.valid_edge\[7:0\]"
wvAddSignal -win $_nWave3 "/tb_sideband_out/u_bfm/clk" \
           "/tb_sideband_out/u_bfm/rst_n" \
           "/tb_sideband_out/u_bfm/sideband_in\[7:0\]" \
           "/tb_sideband_out/u_bfm/sideband_valid\[7:0\]" \
           "/tb_sideband_out/u_bfm/INSTANCE_ID\[31:0\]" \
           "/tb_sideband_out/u_bfm/NUM_SIGNALS\[31:0\]" \
           "/tb_sideband_out/u_bfm/DELAY_WIDTH\[31:0\]" \
           "/tb_sideband_out/u_bfm/FIFO_DEPTH\[31:0\]" \
           "/tb_sideband_out/u_bfm/configured_delays\[7:0\]" \
           "/tb_sideband_out/u_bfm/sideband_valid_d\[7:0\]" \
           "/tb_sideband_out/u_bfm/proc_state\[1:0\]" \
           "/tb_sideband_out/u_bfm/delay_counter\[7:0\]" \
           "/tb_sideband_out/u_bfm/is_blocking_mode" \
           "/tb_sideband_out/u_bfm/SIGNAL_IDX_WIDTH\[31:0\]" \
           "/tb_sideband_out/u_bfm/current_event" \
           "/tb_sideband_out/u_bfm/fifo_wr_en" \
           "/tb_sideband_out/u_bfm/fifo_rd_en" \
           "/tb_sideband_out/u_bfm/fifo_full" \
           "/tb_sideband_out/u_bfm/fifo_empty" \
           "/tb_sideband_out/u_bfm/fifo_wr_data" \
           "/tb_sideband_out/u_bfm/fifo_rd_data" \
           "/tb_sideband_out/u_bfm/valid_edge\[7:0\]"
wvSetPosition -win $_nWave3 {("G2" 0)}
wvSetPosition -win $_nWave3 {("G2" 22)}
wvSetPosition -win $_nWave3 {("G2" 22)}
verdiSetActWin -win $_nWave3
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvSetCursor -win $_nWave3 101957.584830 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 158334.131737 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 171828.517964 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 164931.387226 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 237201.322355 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 247097.205589 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 276484.980040 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 258792.340319 -snap {("G2" 11)}
wvZoom -win $_nWave3 87863.448104 371245.558882
wvSetCursor -win $_nWave3 167052.061894 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 177516.271574 -snap {("G2" 11)}
wvSetCursor -win $_nWave3 167334.878372 -snap {("G2" 11)}
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
