Start to rebuild KDB and simv binary

                         Chronologic VCS (TM)
       Version V-2023.12-SP2_Full64 -- Sun Jul 20 16:21:49 2025

                    Copyright (c) 1991 - 2024 Synopsys, Inc.
   This software and the associated documentation are proprietary to Synopsys,
 Inc. This software may only be used in accordance with the terms and conditions
 of a written license agreement with Synopsys, Inc. All other use, reproduction,
   or distribution of this software is strictly prohibited.  Licensed Products
     communicate with Synopsys servers for the purpose of providing software
    updates, detecting software piracy and verifying that customers are using
    Licensed Products in conformity with the applicable License Key for such
  Licensed Products. Synopsys will use information gathered in connection with
    this process to deliver software updates and pursue software pirates and
                                   infringers.

 Inclusivity & Diversity - Visit SolvNetPlus to read the "Synopsys Statement on
            Inc
lusivity and Diversity" (Refer to article 000036315 at
                        https://solvnetplus.synopsys.com)


Warning-[LCA_FEATURES_ENABLED] Usage warning
  LCA features enabled by '-lca' argument on the command line.  For more 
  information regarding list of LCA features please refer to Chapter "LCA 
  features" in the VCS Release Notes


Parsing design file '../../../../src/sideband_out_bfm.sv'
Parsing design file '../../../nonblocking/src/sv/tb_sideband_out.sv'
Top Level Modules:
       tb_sideband_out
TimeScale is 1 ns / 1 ps

Error-[ICPD] Illegal combination of drivers
../../../../src/sideband_out_bfm.sv, 67
  Illegal combination of procedural drivers
  Variable "configured_delays" is driven by an invalid combination of 
  procedural drivers. Variables written on left-hand of "always_ff" cannot be 
  written to by any other processes, including other "always_ff" processes.
  This variable is declared at "../../../../src/sideband_out_bfm.sv", 67: bit 
  [(DELAY_WIDTH - 1):0] configured_delays[(NUM_SIGNALS - 1):0];
  The first driver is at "../../../nonblocking/src/sv/tb_sideband_out.sv", 58:
  tb_sideband_out.u_bfm.s2h_set_delay(1, 5);
  The second driver is at "../../../../src/sideband_out_bfm.sv", 107: 
  always_ff @(posedge clk) begin
  if (!rst_n) begin
   ...

1 warning
1 error

CPU time: .690 seconds to compile
Verdi KDB elaboration done and the database successfully generated: 0 error(s), 0 warning(s)

debExit
