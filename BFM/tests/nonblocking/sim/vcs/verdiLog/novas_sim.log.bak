Invoking simulator...

/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking -l vcs.log +UVM_VERDI_TRACE=UVM_AWARE +fsdb+gate=off -ucli2Proc -ucli
debImport "-i" "-simflow" "-dbdir" \
          "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir"
Chronologic VCS simulator copyright 1991-2023
Contains Synopsys proprietary information.
Compiler version V-2023.12-SP2_Full64; Runtime version V-2023.12-SP2_Full64;  Jul 20 23:12 2025

verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
*Verdi* Loading libsscore_vcs202312.so

FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
(C) 1996 - 2024 by Synopsys, Inc.
*Verdi* : Create FSDB file '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb'
*Verdi* : Flush all FSDB Files at 0 ps.

*Verdi* : Enable RPC Server(113454)

srcTBInvokeSim
Verdi>fsdbDumpvars 1 "tb_sideband_out.u_bfm.clk"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.rst_n"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.INSTANCE_ID"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.NUM_SIGNALS"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.DELAY_WIDTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.QUEUE_DEPTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.configured_delay"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in_d"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.proc_state"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.delay_counter"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.is_blocking_mode"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.current_transition"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_wr_en"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_rd_en"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_full"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_empty"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_wr_data"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_rd_data"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.vector_changed"  +all +trace_process;fsdbDumpflush 

*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.clk).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.rst_n).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.INSTANCE_ID).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.NUM_SIGNALS).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.DELAY_WIDTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.QUEUE_DEPTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.configured_delay).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in_d).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.proc_state).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.delay_counter).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.is_blocking_mode).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.current_transition).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_en).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_en).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_full).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_empty).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_data).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_data).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.vector_changed).
*Verdi* : Flush all FSDB Files at 0 ps.

Verdi>run
[SW Bridge] Initializing for BFM ID 0 with 8 signals.
[SW Bridge] Setting mode to non-blocking (0).
[SW Bridge] Setting delay for signal 1 to 5 cycles.
[SW Bridge] Setting delay for signal 3 to 5 cycles.
[SW Bridge] Setting delay for signal 5 to 5 cycles.
[SW Bridge] Setting delay for signal 7 to 5 cycles.
*Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
*Verdi* : End of traversing.
*Verdi* : fsdbDumpon - All FSDB files at 0 ps.
Test Started: Non-blocking mode with vector-based notifications
@55000 TC1: Changing bit 1 (0->1)

Error-[DPI-DIFNF] DPI import function not found
../../../../src/sideband_out_bfm.sv, 200
  The definition of DPI import function/task 'h2s_sideband_notify_vector_nb' 
  does not exist.
  Please check the stated DPI import function/task is defined, and its 
  definition is either passed in a source file at compile-time, or provided in
  a shared library specified using the LRM Annex-J options at run-time.

*** FATAL: Simulation stopped due to a runtime error (such as null object access) in the HDL code.  Please see above in the log for more details, and/or recompile with '-check_all' to get more information printed to the log.
The simulation cannot be continued. The current simulation state can be observed in GUI/UCLI.
tb_sideband_out.sv, 3 : module tb_sideband_out;
           V C S   S i m u l a t i o n   R e p o r t 
Time: 105000 ps
CPU Time:      0.420 seconds;       Data structure size:   0.0Mb
Sun Jul 20 23:14:05 2025
debExit
