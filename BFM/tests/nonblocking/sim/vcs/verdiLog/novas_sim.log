Invoking simulator...
<PERSON>>/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking +UVM_VERDI_TRACE=UVM_AWARE +fsdb+gate=off -ucli2Proc -ucli
Info: [VCS_SAVE_RESTORE_INFO] ASLR (Address Space Layout Randomization) is detected on the machine. To enable $save functionality, ASLR will be switched off and simv re-executed.
Please use '-no_save' simv switch to avoid re-execution or '-suppress=ASLR_DETECTED_INFO' to suppress this message.
Chronologic VCS simulator copyright 1991-2023
Contains Synopsys proprietary information.
Compiler version V-2023.12-SP2_Full64; Runtime version V-2023.12-SP2_Full64;  Jul 20 16:23 2025
*Verdi* Loading libsscore_vcs202312.so
FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
(C) 1996 - 2024 by Synopsys, Inc.
*Verdi* : Create FSDB file '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb'
*Verdi* : Flush all FSDB Files at 0 ps.
*Verdi* : Enable RPC Server(22661)
Verdi>fsdbDumpvars 1 "tb_sideband_out.u_bfm.clk"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.rst_n"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_valid"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.INSTANCE_ID"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.NUM_SIGNALS"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.DELAY_WIDTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.channel_state"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.stored_signal_val"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.delay_counters"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.configured_delays"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in_d"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_valid_d"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.is_blocking_mode"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in_edge"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_valid_posedge"  +all +trace_process;fsdbDumpflush 

*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.clk).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.rst_n).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_valid).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.INSTANCE_ID).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.NUM_SIGNALS).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.DELAY_WIDTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.channel_state).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.stored_signal_val).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.delay_counters).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.configured_delays).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in_d).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_valid_d).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.is_blocking_mode).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in_edge).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_valid_posedge).
*Verdi* : Flush all FSDB Files at 0 ps.

Verdi>run
[SW Bridge] Initializing for BFM ID 0 with 8 signals.
[SW Bridge] Setting mode to non-blocking (0).
[SW Bridge] Setting delay for signal 1 to 5 cycles.
[SW Bridge] Setting delay for signal 3 to 5 cycles.
[SW Bridge] Setting delay for signal 5 to 5 cycles.
[SW Bridge] Setting delay for signal 7 to 5 cycles.
*Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
*Verdi* : End of traversing.
*Verdi* : fsdbDumpon - All FSDB files at 0 ps.
Test Started: Non-blocking mode
@55000 TC1: Triggering signal 1
@75000 TC1: Asserting valid for signal 1
[SW Bridge] Received event from BFM ID 0: Signal 1 -> 1
@185000 TC2: Triggering signal 2
@205000 TC2: Asserting valid for signal 2
[SW Bridge] Received event from BFM ID 0: Signal 2 -> 1
@315000 TC3: Triggering signal 3 and valid simultaneously
[SW Bridge] Received event from BFM ID 0: Signal 3 -> 1
Test Finished
$finish called from file "../../../nonblocking/src/sv/tb_sideband_out.sv", line 101.
$finish at simulation time               525000
Simulation complete, time is 525000 ps.
tb_sideband_out.sv, 101 :     $finish;
           V C S   S i m u l a t i o n   R e p o r t 
Time: 525000 ps
CPU Time:      0.710 seconds;       Data structure size:   0.0Mb
Sun Jul 20 16:33:26 2025
debExit
