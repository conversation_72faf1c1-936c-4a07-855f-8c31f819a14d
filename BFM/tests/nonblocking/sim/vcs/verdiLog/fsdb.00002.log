#######################################################################################
# fsdb.xxxxx.log : log primitive debug message of FSDB Reader
# This is for R&D to analyze when there are issues happening when FSDB reading
# The corresponding FSDB file is /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
#######################################################################################
[BASIC]Reader version : Chronologic Simulation VCS Release V-2023.12-SP2_Full64
[BASIC]simulator version : Chronologic Simulation VCS Release V-2023.12-SP2_Full64
[BASIC]dumper version : FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
Async open disabled.
[READSCOPE]ReadScopeTree calling time is 3
[READSCOPE]ReadScope_PartialLoad calling time is 0
[READSCOPE]Scope read by partial load count is 0
[READSCOPE]Scope read count is 110
[READSCOPE]Decompressed blk is 37
[MAXCHAIN]Decompressed cnt 0
[PERF]VC decompress count : 1
[PERF]VCChain decompress count : 124
[FSDBGate Info] start
[FSDBGate Info] Total fhdb_compute_count = 1
[FSDBGate Info] end
