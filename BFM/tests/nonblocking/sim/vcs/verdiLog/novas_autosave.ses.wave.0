Magic 271485
Revision Verdi_V-2023.12-SP2

; Window Layout <x> <y> <width> <height> <signalwidth> <valuewidth>
viewPort 0 29 2560 586 410 120

; File list:
; openDirFile [-d delimiter] [-s time_offset] [-rf auto_bus_rule_file] path_name file_name
openDirFile -d / "" "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb"

; file time scale:
; fileTimeScale ### s|ms|us|ns|ps

; signal spacing:
signalSpacing 5

; windowTimeUnit is used for zoom, cursor & marker
; waveform viewport range
zoom 0.000000 106050.000000
cursor 65000.000000
marker 0.000000

; user define markers
; userMarker time_pos marker_name color linestyle
; visible top row signal index
top 0
; marker line index
markerPos 20

; event list
; addEvent event_name event_expression
; curEvent event_name



COMPLEX_EVENT_BEGIN


COMPLEX_EVENT_END



; toolbar current search type
; curSTATUS search_type
curSTATUS ByChange


addGroup "G1"
activeDirFile "" "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb"
addSignal -h 23 /tb_sideband_out/u_bfm/clk
addSignal -h 23 /tb_sideband_out/u_bfm/rst_n
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_in[7:0]
addSignal -h 23 -UNSIGNED -UDEC /tb_sideband_out/u_bfm/INSTANCE_ID[31:0]
addSignal -h 23 -UNSIGNED -UDEC /tb_sideband_out/u_bfm/NUM_SIGNALS[31:0]
addSignal -h 23 -UNSIGNED -UDEC /tb_sideband_out/u_bfm/DELAY_WIDTH[31:0]
addSignal -h 23 -UNSIGNED -UDEC /tb_sideband_out/u_bfm/QUEUE_DEPTH[31:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delay[7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_in_d[7:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/proc_state[1:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counter[7:0]
addSignal -h 23 /tb_sideband_out/u_bfm/is_blocking_mode
addSignal -h 23 /tb_sideband_out/u_bfm/current_transition
addSignal -h 23 /tb_sideband_out/u_bfm/fifo_wr_en
addSignal -h 23 /tb_sideband_out/u_bfm/fifo_rd_en
addSignal -h 23 /tb_sideband_out/u_bfm/fifo_full
addSignal -h 23 /tb_sideband_out/u_bfm/fifo_empty
addSignal -h 23 /tb_sideband_out/u_bfm/fifo_wr_data
addSignal -h 23 /tb_sideband_out/u_bfm/fifo_rd_data
addSignal -h 23 /tb_sideband_out/u_bfm/vector_changed
addGroup "G2"

; getSignalForm Scope Hierarchy Status
; active file of getSignalForm

