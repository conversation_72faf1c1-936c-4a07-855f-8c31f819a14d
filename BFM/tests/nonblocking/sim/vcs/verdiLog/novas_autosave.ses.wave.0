Magic 271485
Revision Verdi_V-2023.12-SP2

; Window Layout <x> <y> <width> <height> <signalwidth> <valuewidth>
viewPort 0 29 2560 687 468 65

; File list:
; openDirFile [-d delimiter] [-s time_offset] [-rf auto_bus_rule_file] path_name file_name
openDirFile -d / "" "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb"

; file time scale:
; fileTimeScale ### s|ms|us|ns|ps

; signal spacing:
signalSpacing 5

; windowTimeUnit is used for zoom, cursor & marker
; waveform viewport range
zoom 0.000000 530250.000000
cursor 315000.000000
marker 0.000000

; user define markers
; userMarker time_pos marker_name color linestyle
; visible top row signal index
top 3
; marker line index
markerPos 56

; event list
; addEvent event_name event_expression
; curEvent event_name



COMPLEX_EVENT_BEGIN


COMPLEX_EVENT_END



; toolbar current search type
; curSTATUS search_type
curSTATUS ByChange


addGroup "G1"
activeDirFile "" "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb"
addSignal -h 23 /tb_sideband_out/u_bfm/clk
addSignal -h 23 /tb_sideband_out/u_bfm/rst_n
addSignal -expanded -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_in[7:0]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[7]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[6]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[5]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[4]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[3]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[2]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[1]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_in[0]
addSignal -expanded -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_valid[7:0]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[7]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[6]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[5]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[4]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[3]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[2]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[1]
addSignal -h 23 /tb_sideband_out/u_bfm/sideband_valid[0]
addSignal -h 23 -UNSIGNED -UDEC /tb_sideband_out/u_bfm/INSTANCE_ID[31:0]
addSignal -h 23 -UNSIGNED -UDEC /tb_sideband_out/u_bfm/NUM_SIGNALS[31:0]
addSignal -h 23 -UNSIGNED -UDEC /tb_sideband_out/u_bfm/DELAY_WIDTH[31:0]
addSignal -expanded -h 23 /tb_sideband_out/u_bfm/channel_state[7:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[7][1:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[6][1:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[5][1:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[4][1:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[3][1:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[2][1:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[1][1:0]
addSignal -h 23 -UNSIGNED /tb_sideband_out/u_bfm/channel_state[0][1:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/stored_signal_val[7:0]
addSignal -expanded -h 23 /tb_sideband_out/u_bfm/delay_counters[7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[7][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[6][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[5][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[4][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[3][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[2][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[1][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/delay_counters[0][7:0]
addSignal -expanded -h 23 /tb_sideband_out/u_bfm/configured_delays[7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[7][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[6][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[5][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[4][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[3][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[2][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[1][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/configured_delays[0][7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_in_d[7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_valid_d[7:0]
addSignal -h 23 /tb_sideband_out/u_bfm/is_blocking_mode
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_in_edge[7:0]
addSignal -h 23 -UNSIGNED -HEX /tb_sideband_out/u_bfm/sideband_valid_posedge[7:0]
addGroup "G2"

; getSignalForm Scope Hierarchy Status
; active file of getSignalForm

