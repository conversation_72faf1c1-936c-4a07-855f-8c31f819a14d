Command Line: /opt/synopsys/verdi/V-2023.12-SP2/platform/LINUXAMD64/bin/Novas -i -simflow -simBin /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv -simDelim -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking_basic -l vcs.log
uname(Linux pc-harriszh2 3.10.0-1160.119.1.el7.x86_64 #1 SMP Tue Jun 4 14:43:51 UTC 2024 x86_64)
type= DS_SIGNAL element= 20 field= 2
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG clk
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG rst_n
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_in[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG INSTANCE_ID
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG NUM_SIGNALS
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG DELAY_WIDTH
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG QUEUE_DEPTH
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG configured_delay[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_in_d[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG proc_state[1:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG delay_counter[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG is_blocking_mode
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG current_transition
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG fifo_wr_en
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG fifo_rd_en
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG fifo_full
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG fifo_empty
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG fifo_wr_data
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG fifo_rd_data
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG vector_changed
-----
second field= signal
type= DS_STRING
clk rst_n sideband_in[7:0] INSTANCE_ID NUM_SIGNALS DELAY_WIDTH QUEUE_DEPTH configured_delay[7:0] sideband_in_d[7:0] proc_state[1:0] delay_counter[7:0] is_blocking_mode current_transition fifo_wr_en fifo_rd_en fifo_full fifo_empty fifo_wr_data fifo_rd_data vector_changed
