Command Line: /opt/synopsys/verdi/V-2023.12-SP2/platform/LINUXAMD64/bin/Novas -i -simflow -simBin /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv -simDelim -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking -l vcs.log
uname(Linux pc-harriszh2 3.10.0-1160.119.1.el7.x86_64 #1 SMP Tue Jun 4 14:43:51 UTC 2024 x86_64)
type= DS_SIGNAL element= 16 field= 2
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG clk
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG rst_n
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_in[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_valid[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG INSTANCE_ID
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG NUM_SIGNALS
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG DELAY_WIDTH
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG channel_state[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG stored_signal_val[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG delay_counters[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG configured_delays[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_in_d[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_valid_d[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG is_blocking_mode
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_in_edge[7:0]
-----
second field= signal
VERILOG tb_sideband_out
VERILOG u_bfm
VERILOG sideband_valid_edge[7:0]
-----
second field= signal
type= DS_STRING
clk rst_n sideband_in[7:0] sideband_valid[7:0] INSTANCE_ID NUM_SIGNALS DELAY_WIDTH channel_state[7:0] stored_signal_val[7:0] delay_counters[7:0] configured_delays[7:0] sideband_in_d[7:0] sideband_valid_d[7:0] is_blocking_mode sideband_in_edge[7:0] sideband_valid_edge[7:0]
