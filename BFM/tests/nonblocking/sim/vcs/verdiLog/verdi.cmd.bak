verdiSetActWin -dock widgetDock_<Message>
simSetSimulator "-vcssv" -exec \
           "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv" -args \
           "-sv_lib ../../../nonblocking/pseudo_bridge_nonblocking"
debImport "-dbdir" \
          "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir"
debLoadSimResult /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/novas.fsdb
wvCreateWindow
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcTBInvokeSim
verdiSetActWin -win $_InteractiveConsole_3
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
verdiDockWidgetSetCurTab -dock widgetDock_<Local>
verdiSetActWin -dock widgetDock_<Local>
verdiDockWidgetSetCurTab -dock widgetDock_<Inst._Tree>
verdiSetActWin -dock widgetDock_<Inst._Tree>
verdiSetActWin -dock widgetDock_<Local>
verdiDockWidgetSetCurTab -dock widgetDock_<Inst._Tree>
verdiSetActWin -dock widgetDock_<Inst._Tree>
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSignalView -on
verdiSetActWin -dock widgetDock_<Signal_List>
srcSignalViewSelect "tb_sideband_out.u_bfm.clk"
srcSignalViewSelect "tb_sideband_out.u_bfm.clk" "tb_sideband_out.u_bfm.rst_n" \
           "tb_sideband_out.u_bfm.sideband_in\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid\[7:0\]" \
           "tb_sideband_out.u_bfm.INSTANCE_ID" \
           "tb_sideband_out.u_bfm.NUM_SIGNALS" \
           "tb_sideband_out.u_bfm.DELAY_WIDTH" \
           "tb_sideband_out.u_bfm.channel_state\[7:0\]" \
           "tb_sideband_out.u_bfm.stored_signal_val\[7:0\]" \
           "tb_sideband_out.u_bfm.delay_counters\[7:0\]" \
           "tb_sideband_out.u_bfm.configured_delays\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_in_d\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid_d\[7:0\]" \
           "tb_sideband_out.u_bfm.is_blocking_mode" \
           "tb_sideband_out.u_bfm.sideband_in_edge\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_valid_posedge\[7:0\]"
verdiDockWidgetSetCurTab -dock windowDock_nWave_2
verdiSetActWin -win $_nWave2
wvAddSignal -win $_nWave2 "/tb_sideband_out/u_bfm/clk" \
           "/tb_sideband_out/u_bfm/rst_n" \
           "/tb_sideband_out/u_bfm/sideband_in\[7:0\]" \
           "/tb_sideband_out/u_bfm/sideband_valid\[7:0\]" \
           "/tb_sideband_out/u_bfm/INSTANCE_ID\[31:0\]" \
           "/tb_sideband_out/u_bfm/NUM_SIGNALS\[31:0\]" \
           "/tb_sideband_out/u_bfm/DELAY_WIDTH\[31:0\]" \
           "/tb_sideband_out/u_bfm/channel_state\[7:0\]" \
           "/tb_sideband_out/u_bfm/stored_signal_val\[7:0\]" \
           "/tb_sideband_out/u_bfm/delay_counters\[7:0\]" \
           "/tb_sideband_out/u_bfm/configured_delays\[7:0\]" \
           "/tb_sideband_out/u_bfm/sideband_in_d\[7:0\]" \
           "/tb_sideband_out/u_bfm/sideband_valid_d\[7:0\]" \
           "/tb_sideband_out/u_bfm/is_blocking_mode" \
           "/tb_sideband_out/u_bfm/sideband_in_edge\[7:0\]" \
           "/tb_sideband_out/u_bfm/sideband_valid_posedge\[7:0\]"
wvSetPosition -win $_nWave2 {("G1" 0)}
wvSetPosition -win $_nWave2 {("G1" 16)}
wvSetPosition -win $_nWave2 {("G1" 16)}
verdiSetActWin -dock widgetDock_<Signal_List>
srcTBRunSim
wvSetCursor -win $_nWave2 518125.310174 -snap {("G1" 8)}
wvZoomAll -win $_nWave2
verdiSetActWin -win $_nWave2
wvSetPosition -win $_nWave2 {("G1" 10)}
wvExpandBus -win $_nWave2
wvSetPosition -win $_nWave2 {("G1" 24)}
wvScrollDown -win $_nWave2 2
wvScrollDown -win $_nWave2 5
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
wvSetCursor -win $_nWave2 95523.945409 -snap {("G1" 17)}
verdiSetActWin -win $_nWave2
wvSelectSignal -win $_nWave2 {( "G1" 19 )} 
wvSetPosition -win $_nWave2 {("G1" 19)}
wvExpandBus -win $_nWave2
wvSetPosition -win $_nWave2 {("G1" 32)}
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvSelectSignal -win $_nWave2 {( "G1" 17 )} 
wvSelectSignal -win $_nWave2 {( "G1" 16 )} 
wvSelectSignal -win $_nWave2 {( "G1" 15 )} 
wvSelectSignal -win $_nWave2 {( "G1" 8 )} 
wvSelectSignal -win $_nWave2 {( "G1" 8 )} 
wvSetPosition -win $_nWave2 {("G1" 8)}
wvExpandBus -win $_nWave2
wvSetPosition -win $_nWave2 {("G1" 40)}
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 0
wvScrollDown -win $_nWave2 0
wvScrollDown -win $_nWave2 0
wvScrollUp -win $_nWave2 2
wvScrollUp -win $_nWave2 4
wvSelectSignal -win $_nWave2 {( "G1" 23 )} 
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollUp -win $_nWave2 1
wvScrollDown -win $_nWave2 0
wvScrollDown -win $_nWave2 0
wvScrollDown -win $_nWave2 0
wvScrollDown -win $_nWave2 0
wvScrollDown -win $_nWave2 0
wvScrollDown -win $_nWave2 0
wvSelectSignal -win $_nWave2 {( "G1" 4 )} 
wvSetPosition -win $_nWave2 {("G1" 4)}
wvExpandBus -win $_nWave2
wvSetPosition -win $_nWave2 {("G1" 48)}
wvSelectSignal -win $_nWave2 {( "G1" 3 )} 
wvSetPosition -win $_nWave2 {("G1" 3)}
wvExpandBus -win $_nWave2
wvSetPosition -win $_nWave2 {("G1" 56)}
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvScrollDown -win $_nWave2 1
wvSetCursor -win $_nWave2 314465.880893 -snap {("G1" 8)}
verdiDockWidgetSetCurTab -dock windowDock_InteractiveConsole_3
verdiSetActWin -win $_InteractiveConsole_3
verdiDockWidgetSetCurTab -dock windowDock_nWave_2
verdiSetActWin -win $_nWave2
debExit
