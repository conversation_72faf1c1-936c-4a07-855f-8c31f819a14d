simSetSimulator "-vcssv" -exec \
           "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv" -args \
           "-sv_lib ../../../nonblocking/pseudo_bridge_nonblocking -l vcs.log" \
           -uvmDebug on -simDelim
debImport "-i" "-simflow" "-dbdir" \
          "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir"
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcTBInvokeSim
verdiSetActWin -dock widgetDock_<Member>
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
srcSetScope "tb_sideband_out.u_bfm" -delim "." -win $_nTrace1
srcHBSelect "tb_sideband_out.u_bfm" -win $_nTrace1
verdiSetActWin -dock widgetDock_<Inst._Tree>
srcSignalViewSelect "tb_sideband_out.u_bfm.clk"
verdiSetActWin -dock widgetDock_<Signal_List>
srcSignalViewSelect "tb_sideband_out.u_bfm.clk" "tb_sideband_out.u_bfm.rst_n" \
           "tb_sideband_out.u_bfm.sideband_in\[7:0\]" \
           "tb_sideband_out.u_bfm.INSTANCE_ID" \
           "tb_sideband_out.u_bfm.NUM_SIGNALS" \
           "tb_sideband_out.u_bfm.DELAY_WIDTH" \
           "tb_sideband_out.u_bfm.QUEUE_DEPTH" \
           "tb_sideband_out.u_bfm.configured_delay\[7:0\]" \
           "tb_sideband_out.u_bfm.sideband_in_d\[7:0\]" \
           "tb_sideband_out.u_bfm.proc_state\[1:0\]" \
           "tb_sideband_out.u_bfm.delay_counter\[7:0\]" \
           "tb_sideband_out.u_bfm.is_blocking_mode" \
           "tb_sideband_out.u_bfm.current_transition" \
           "tb_sideband_out.u_bfm.fifo_wr_en" \
           "tb_sideband_out.u_bfm.fifo_rd_en" \
           "tb_sideband_out.u_bfm.fifo_full" \
           "tb_sideband_out.u_bfm.fifo_empty" \
           "tb_sideband_out.u_bfm.fifo_wr_data" \
           "tb_sideband_out.u_bfm.fifo_rd_data" \
           "tb_sideband_out.u_bfm.vector_changed"
wvCreateWindow
srcSignalViewAddSelectedToWave -clipboard
wvDrop -win $_nWave3
srcTBRunSim
wvSetCursor -win $_nWave3 104418.762475 -snap {("G1" 13)}
wvZoomAll -win $_nWave3
srcDeselectAll -win $_nTrace1
verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
verdiSetActWin -win $_nWave3
wvScrollUp -win $_nWave3 5
wvSelectSignal -win $_nWave3 {( "G1" 3 )} 
wvSelectSignal -win $_nWave3 {( "G1" 3 )} 
wvSetPosition -win $_nWave3 {("G1" 3)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 28)}
wvSelectSignal -win $_nWave3 {( "G1" 3 )} 
wvSetPosition -win $_nWave3 {("G1" 3)}
wvCollapseBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 3)}
wvSetPosition -win $_nWave3 {("G1" 20)}
wvSetCursor -win $_nWave3 65196.407186 -snap {("G1" 3)}
verdiDockWidgetSetCurTab -dock windowDock_InteractiveConsole_2
verdiSetActWin -win $_InteractiveConsole_2
debExit
