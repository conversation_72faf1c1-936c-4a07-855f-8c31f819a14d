Command: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking_basic +UVM_VERDI_TRACE=UVM_AWARE +fsdb+gate=off -ucli2Proc -ucli -l /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/sim.log
Chronologic VCS simulator copyright 1991-2023
Contains Synopsys proprietary information.
Compiler version V-2023.12-SP2_Full64; Runtime version V-2023.12-SP2_Full64;  Jul 20 23:17 2025

ucli% synUtils::getArch
linux64
ucli% loaddl -simv /opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUXAMD64/libnovas.so LoadFSDBDumpCmd;LoadFSDBDumpCmd
LoadFSDBDumpCmd success
ucli% config ckptfsdbcheck off;config endofsim noexit;config onfail {enable all};config followactivescope on;catch {setUcliVerdiConnected};set watch::resultTagsForVerdiBP {<ucli_break_bp_status_begin> <ucli_break_bp_status_end>};set ucliCore::support_restore_through_stask_in_verdi 1;cbug::config pretty_print auto;fsdbDumpfile {/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb} ;fsdbDumpflush ; 
*Verdi* Loading libsscore_vcs202312.so
FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
(C) 1996 - 2024 by Synopsys, Inc.
*Verdi* : Create FSDB file '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb'
*Verdi* : Flush all FSDB Files at 0 ps.

ucli% synEnv::hasFataled
0
ucli% ucliCore::getToolPID
115928
ucli% sps_interactive
*Verdi* : Enable RPC Server(115928)

ucli% ucliCore::getToolPID
115928
ucli% ucliCore::getToolPID
115928
ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% puts $ucliCore::nativeUcliMode
0

ucli% ucliCore::getToolTopPID
115928
ucli% pid
115943
ucli% synUtils::sendTool -active {_icl_createSharedMemory /tmp/vcs_dve_general.harriszh.115928 }

ucli% if { [info vars watch::vcbp_str_len_limit_of_get_value] != ""} {set watch::vcbp_str_len_limit_of_get_value 1024}
1024
ucli% info command stateVerdiChangeCB

ucli% proc stateVerdiChangeCB args { if {$ucliGUI::state eq "terminated"} {puts "\n<VERDI_Terminated>\n";catch {setVerdiSimTerminated}}}

ucli% trace variable ucliGUI::state wu stateVerdiChangeCB

ucli% if {[catch {rename synopsys::restore verdiHack::restore} ]} {puts "0"}

ucli% proc synopsys::restore {args} { verdiHack::restore $args; puts "\n<VERDI_RESTORE>\n"}

ucli% if {[catch {rename quit verdiHack::quit} ]} {puts "0"}

ucli% proc quit {args} { if {[string length $args] == 0} { verdiHack::quit; } elseif {([string equal "-h" $args] == 1)||([string equal "-he" $args] == 1)||([string equal "-hel" $args] == 1)||([string equal "-help" $args] == 1)} { puts "\n quit           # Exit the simulation.\n \[-noprompt\]        (Exit the simulation and Verdi.)\n"} elseif {([string equal "-n" $args] == 1)||([string equal "-no" $args] == 1)||([string equal "-nop" $args] == 1)||([string equal "-nopr" $args] == 1)||([string equal "-nopro" $args] == 1)||([string equal "-noprom" $args] == 1)||([string equal "-nopromp" $args] == 1)||([string equal "-noprompt" $args] == 1)} { puts "\nVERDI_EXIT_N\n" } else { verdiHack::quit $args; } }

ucli% if {[catch {rename exit verdiHack::exit} ]} {puts "0"}

ucli% proc exit {args} { if {[string length $args] == 0} { verdiHack::exit; } elseif {([string equal "-h" $args] == 1)||([string equal "-he" $args] == 1)||([string equal "-hel" $args] == 1)||([string equal "-help" $args] == 1)} { puts "\n exit           # Exit the simulation.\n \[-noprompt\]        (Exit the simulation and Verdi.)\n"} elseif {([string equal "-n" $args] == 1)||([string equal "-no" $args] == 1)||([string equal "-nop" $args] == 1)||([string equal "-nopr" $args] == 1)||([string equal "-nopro" $args] == 1)||([string equal "-noprom" $args] == 1)||([string equal "-nopromp" $args] == 1)||([string equal "-noprompt" $args] == 1)} { puts "\nVERDI_EXIT_N\n" } else { verdiHack::exit $args; } }

ucli% proc checkpoint::beforeRecreate {} { sps_interactive }

ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% save::getUserdefinedProcs
::stateVerdiChangeCB ::LoadFSDBDumpCmd
ucli% info procs
ipi_get_str fsdbDumpMDAByFile fsdbDumpMDA echo fsdbDumpMemNow fsdbAutoSwitchDumpfile unknown sps_interactive auto_import stat fsdbDumpfile setenv auto_execok pkg_mkIndex stateVerdiChangeCB fsdbDumpSingle proc_body ipi_begin fsdbDumpoff getenv fsdbDumplimit fsdbDumpPattern ipi_handle ipi_get_prop fsdbDumpvarsByFile fsdbDumpMDAInScope lminus ipi_sim_get ls auto_load_index proc_args fsdbAddRuntimeSignal fsdbDumpSC print_message_info ridbDump fsdbDumpSVAoff fsdbSuppress fsdbDumpvars help fsdbDumpMDAOnChange ipi_control auto_qualify fsdbDumpMem sourceSavedOnbreakHandler tclPkgUnknown printenv ipi_handle_by_name helpdoc fsdbDumpMemInScope fsdbDumpFinish is_true fsdbDumpon sh fsdbQueryInfo LoadFSDBDumpCmd fsdbDumpPSL fsdbDumpSVA ipi_end wrapperSpecmanSn fsdbDumpSVAon fsdbDumpClassObjectByFile is_false auto_load fsdbDumpPSLon ipi_get_int64 fsdbSubstituteHier ipi_get_value ipi_iterate exit fsdbDumpMemInFile tclLog fsdbDumpflush get_unix_variable mem_debug ipi_scan fsdbDumpPSLoff fsdbDumpClassObject fsdbDumpvarsToFile set_unix_variable bgerror fsdbDumpStrength add_group fsdbSwitchDumpfile source add_wave sos_fsdbDumpvars ipi_set_prop unsetenv fsdbDumpvarsES readline fsdbDisplay ipi_handle_free set_group ipi_get quit define_proc_attributes tclPkgSetup fsdbDumpMDANow ipi_init_play_tcl fsdbDumpIO
ucli% info commands
ipi_get_str cchange subst fsdbDumpMDAByFile pwd cshow guiSource fsdbDumpMDA echo fsdbDumpMemNow fsdbAutoSwitchDumpfile ucliConnectToSharedMem unknown ucliIsScriptPauseByVerdi lrange sps_interactive auto_import setVerdiSimTerminated coverage case stat fsdbDumpfile break setenv dump auto_execok msglog linsert pkg_mkIndex dumplist catch stateVerdiChangeCB mpformat fsdbDumpSingle if join detach_sim halt proc_body abortScriptAfterDetachVerdi report_violations which lp_show ipi_begin pause dve_help fsdbDumpoff format getenv listing fsdbDumplimit package binary trace ctimeout onbreak after flush fsdbDumpPattern step restore ipi_handle power continue get_command_option_values force try foreach ipi_get_prop fsdbDumpvarsByFile lrepeat tailcall unset fsdbDumpMDAInScope lminus ipi_sim_get interp write_app_var lindex stack yield ls resetInterruptCount runAfterDetachVerdi eof saif list printvar create_command_group keysFileChangeCB virtual auto_load_index checkpoint proc_args finish fsdbAddRuntimeSignal cgettimeout sexpr lsearch fblocked fsdbDumpSC fanin lappend print_message_info proc cextract ridbDump writelist get_defined_commands cdump fsdbDumpSVAoff fsdbSuppress man set_sig_vas_in_waiting_input fconfigure fsdbDumpvars help fsdbDumpMDAOnChange ipi_control switch auto_qualify fsdbDumpMem mpexpr status sourceSavedOnbreakHandler tclPkgUnknown close printenv ipi_handle_by_name helpdoc thread lreverse set read fsdbDumpMemInScope initAttachableFile unalias fsdbDumpFinish setVerdiAttachable seek while set_cle_options is_true set_message_info writeKeyLog cbug fsdbDumpon find_forces yieldto sh start_verdi load fsdbQueryInfo memory sn vcsExecFuncInSharedObject cenable history puts gdb loadSharedObject tblog assertion resume _synopsys_cci_unalias socket tell open get_message_ids cadd exec pid stop LoadFSDBDumpCmd unloadSharedObject redirect fsdbDumpPSL throw fsdbDumpSVA ipi_end return ace wrapperSpecmanSn error fsdbDumpSVAon split fsdbDumpClassObjectByFile is_false array ucliAttachedByVerdi vbus get lreplace concat coroutine fcopy update parse_proc_arguments report_app_var run search setUcliVerdiStatus for append auto_load fsdbDumpPSLon ucliLaunchVerdi alias logFileChangeCB scan setUcliVerdiConnected apply abort set_current_command_mode release zlib chan loads _spw_define_proc_attributes ipi_get_int64 onerror print_suppressed_messages scope fileevent fsdbSubstituteHier inputFileChangeCB regexp resultTagsForVerdiCB set_app_var ipi_get_value cdelete upvar ipi_iterate save encoding onfail exit fsdbDumpMemInFile incr tclLog fsdbDumpflush get_message_info get_unix_variable show senv glob mem_debug ipi_scan GUIsource config fsdbDumpPSLoff time eval lassign fsdbDumpClassObject gets fsdbDumpvarsToFile tcl_exit set_unix_variable restart start change llength variable constraints xprop bgerror fsdbDumpStrength call clock info apropos add_group fsdbSwitchDumpfile ucliDetachVerdi source GUI_tcl_shell_puts global tcheck initreg add_wave report_timing sos_fsdbDumpvars ipi_set_prop next drivers cd file lmap unload namespace loaddl unsetenv get_app_var fsdbDumpvarsES suppress_message vwait dict readline fsdbDisplay uplevel ipi_handle_free date set_group lset rename ipi_get quit sh_list_key_bindings do unsuppress_message define_proc_attributes error_info tclPkgSetup expr printPromptStr regsub onCheckpointCB csave fsdbDumpMDANow dtl_load ipi_init_play_tcl lsort fsdbDumpIO doFileChangeCB _synopsys_cci_alias writeHistoryLog cdisable string
ucli% set ucliCore::resultTagsForVerdi [list <?special_verdi_begin?> <?special_verdi_end?>]
<?special_verdi_begin?> <?special_verdi_end?>
ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% if {[catch {get uvm_pkg.UVM_VERSION_STRING} result] == 0} {puts $result}

ucli% senv hasTB
0
ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% fsdbDumpflush 
*Verdi* : Flush all FSDB Files at 0 ps.

ucli% senv
activeDomain: Verilog
activeFile: ../../../nonblocking/src/sv/tb_sideband_out.sv
activeFrame: -1
activeLine: 3
activeScope: tb_sideband_out
activeThread: 2
endCol: 0
file: ../../../nonblocking/src/sv/tb_sideband_out.sv
frame: -1
fsdbFilename: 
hasTB: 0
inputFilename: 
keyFilename: ucli.key
line: 3
logFilename: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/sim.log
macroIndex: -1
macroOffset: -1
pid: 115928
scope: tb_sideband_out
startCol: 0
state: stopped
thread: 2
time: 0
timePrecision: 1 ps
vcdFilename: 
vpdFilename:
ucli% synUtils::resolveSourceFilename ../../../nonblocking/src/sv/tb_sideband_out.sv
../../../nonblocking/src/sv/tb_sideband_out.sv
ucli% puts $::ucliCore::cbug_active
0

ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% checkpoint -list -all
There are no checkpoints present.
ucli% stop
No stop points are set
ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% fsdbDumpvars 1 "tb_sideband_out.u_bfm.clk"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.rst_n"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.INSTANCE_ID"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.NUM_SIGNALS"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.DELAY_WIDTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.QUEUE_DEPTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.configured_delay"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in_d"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.proc_state"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.delay_counter"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.is_blocking_mode"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.current_transition"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_wr_en"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_rd_en"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_full"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_empty"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_wr_data"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_rd_data"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.vector_changed"  +all +trace_process;fsdbDumpflush 
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.clk).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.rst_n).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.INSTANCE_ID).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.NUM_SIGNALS).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.DELAY_WIDTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.QUEUE_DEPTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.configured_delay).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in_d).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.proc_state).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.delay_counter).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.is_blocking_mode).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.current_transition).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_en).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_en).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_full).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_empty).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_data).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_data).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.vector_changed).
*Verdi* : Flush all FSDB Files at 0 ps.

ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% run
[SW Bridge] Initializing for BFM ID 0 with 8 signals.
[SW Bridge] Setting mode to non-blocking (0).
[SW Bridge] Setting unified delay to 5 cycles for all signals.
*Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
*Verdi* : End of traversing.
*Verdi* : fsdbDumpon - All FSDB files at 0 ps.
Test Started: Non-blocking mode with vector-based notifications
@55000 TC1: Changing bit 1 (0->1)
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
@175000 TC2: Sequential bit changes
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
@355000 TC3: Multiple bits changing simultaneously
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
Test Finished
$finish called from file "../../../nonblocking/src/sv/tb_sideband_out.sv", line 96.
$finish at simulation time               475000
Simulation complete, time is 475000 ps.
tb_sideband_out.sv, 96 :     $finish;

ucli% synEnv::hasFataled
0
ucli% ucliCore::getToolPID
115928
ucli% save::getUserdefinedProcs
::stateVerdiChangeCB ::LoadFSDBDumpCmd
ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% fsdbDumpflush 
*Verdi* : Flush all FSDB Files at 475,000 ps.

ucli% senv
activeDomain: Verilog
activeFile: ../../../nonblocking/src/sv/tb_sideband_out.sv
activeFrame: -1
activeLine: 96
activeScope: tb_sideband_out
activeThread: 1
endCol: 0
file: ../../../nonblocking/src/sv/tb_sideband_out.sv
frame: -1
fsdbFilename: 
hasTB: 0
inputFilename: 
keyFilename: ucli.key
line: 96
logFilename: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/sim.log
macroIndex: -1
macroOffset: -1
pid: 115928
scope: tb_sideband_out
startCol: 0
state: stopped
thread: 1
time: 475000
timePrecision: 1 ps
vcdFilename: 
vpdFilename:
ucli% synUtils::resolveSourceFilename ../../../nonblocking/src/sv/tb_sideband_out.sv
../../../nonblocking/src/sv/tb_sideband_out.sv
ucli% puts $::ucliCore::cbug_active
0

ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% checkpoint -list -all
There are no checkpoints present.
ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% stop
No stop points are set
ucli% if {[catch {ucliCore::setFocus tool}]} {}

ucli% 