[qBaseWindowStateGroup]
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\Verdi=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\nWave=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\hdlHier=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\hdlSrc=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\messageWindow=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\svtbHier=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1=13
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_0=widgetDock_hdlHier_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_1=widgetDock_messageWindow_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_2=widgetDock_hdlSrc_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_3=widgetDock_signalList_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_4=widgetDock_svtbHier_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_5=widgetDock_svtbClassBrowser_1
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_encode_to_relative_window_id_name=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_restoreNewChildState=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlHier_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlHier_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlHier_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlHier_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_messageWindow_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_messageWindow_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_messageWindow_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_messageWindow_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlSrc_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlSrc_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlSrc_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_hdlSrc_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_signalList_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbHier_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbHier_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbHier_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbHier_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_nWave_1\isNestedWindow=1
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_nWave_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_nWave_1\SELECTION_MESSAGE_TOOLBAR=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_nWave_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_nWave_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_nWave_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\ProductVersion=202312
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\Layout="@ByteArray(\0\0\0\xff\0\0\0\0\xfd\0\0\0\x2\0\0\0\x2\0\0\n\0\0\0\x2K\xfc\x1\0\0\0\x3\xfc\0\0\0\0\0\0\x2\x3\0\0\x1Q\0\xff\xff\xff\xfa\0\0\0\0\x1\0\0\0\x5\xfb\0\0\0(\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0h\0\x64\0l\0H\0i\0\x65\0r\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0^\0\xff\xff\xff\xfb\0\0\0*\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0v\0t\0\x62\0H\0i\0\x65\0r\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0^\0\xff\xff\xff\xfb\0\0\0,\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0v\0t\0\x62\0S\0t\0\x61\0\x63\0k\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0^\0\xff\xff\xff\xfb\0\0\0:\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0v\0t\0\x62\0\x43\0l\0\x61\0s\0s\0\x42\0r\0o\0w\0s\0\x65\0r\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\xed\0\xff\xff\xff\xfb\0\0\0<\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0v\0t\0\x62\0O\0\x62\0j\0\x65\0\x63\0t\0\x42\0r\0o\0w\0s\0\x65\0r\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\x1Q\0\xff\xff\xff\xfc\0\0\x2\t\0\0\x1\x8d\0\0\0\xbb\0\xff\xff\xff\xfa\0\0\0\0\x1\0\0\0\x3\xfb\0\0\0.\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0i\0g\0n\0\x61\0l\0L\0i\0s\0t\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\xa4\0\xff\xff\xff\xfb\0\0\0,\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0v\0t\0\x62\0L\0o\0\x63\0\x61\0l\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\xbb\0\xff\xff\xff\xfb\0\0\0.\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0v\0t\0\x62\0M\0\x65\0m\0\x62\0\x65\0r\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\x9d\0\xff\xff\xff\xfb\0\0\0&\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0h\0\x64\0l\0S\0r\0\x63\0_\0\x31\x1\0\0\x3\x9c\0\0\x6\x64\0\0\0\xd7\0\xff\xff\xff\0\0\0\x3\0\0\n\0\0\0\x2\xec\xfc\x1\0\0\0\x2\xfc\0\0\0\0\0\0\n\0\0\0\x1y\0\xff\xff\xff\xfa\0\0\0\x5\x1\0\0\0\b\xfb\0\0\0\x34\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0m\0\x65\0s\0s\0\x61\0g\0\x65\0W\0i\0n\0\x64\0o\0w\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\xac\0\xff\xff\xff\xfb\0\0\0$\0w\0i\0n\0\x64\0o\0w\0\x44\0o\0\x63\0k\0_\0n\0W\0\x61\0v\0\x65\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\xfb\0\0\0>\0w\0i\0n\0\x64\0o\0w\0\x44\0o\0\x63\0k\0_\0I\0n\0t\0\x65\0r\0\x61\0\x63\0t\0i\0v\0\x65\0\x43\0o\0n\0s\0o\0l\0\x65\0_\0\x32\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\xfb\0\0\0$\0w\0i\0n\0\x64\0o\0w\0\x44\0o\0\x63\0k\0_\0n\0W\0\x61\0v\0\x65\0_\0\x33\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\xfb\0\0\0(\0w\0i\0n\0\x64\0o\0w\0\x44\0o\0\x63\0k\0_\0O\0n\0\x65\0S\0\x65\0\x61\0r\0\x63\0h\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\xfb\0\0\0$\0w\0i\0n\0\x64\0o\0w\0\x44\0o\0\x63\0k\0_\0n\0W\0\x61\0v\0\x65\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\x1s\0\xff\xff\xff\xfb\0\0\0>\0w\0i\0n\0\x64\0o\0w\0\x44\0o\0\x63\0k\0_\0I\0n\0t\0\x65\0r\0\x61\0\x63\0t\0i\0v\0\x65\0\x43\0o\0n\0s\0o\0l\0\x65\0_\0\x31\x1\0\0\0\0\xff\xff\xff\xff\0\0\x1y\0\xff\xff\xff\xfb\0\0\0 \0w\0i\0n\0\x64\0o\0w\0\x44\0o\0\x63\0k\0_\0n\0W\0\x61\0v\0\x65\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\xfb\0\0\0,\0w\0i\0\x64\0g\0\x65\0t\0\x44\0o\0\x63\0k\0_\0s\0v\0t\0\x62\0W\0\x61\0t\0\x63\0h\0_\0\x31\0\0\0\x6\xab\0\0\x3U\0\0\0\x8d\0\xff\xff\xff\0\0\n\0\0\0\0\0\0\0\0\x4\0\0\0\x4\0\0\0\b\0\0\0\b\xfc\0\0\0\a\0\0\0\x1\0\0\0\x1\0\0\0\"\0V\0\x45\0R\0\x44\0I\0_\0S\0\x45\0\x41\0R\0\x43\0H\0_\0P\0\x41\0N\0\x45\x2\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x2\0\0\0\x11\0\0\0.\0H\0\x42\0_\0I\0M\0P\0O\0R\0T\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\0_\0P\0\x41\0N\0\x45\0L\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0,\0H\0\x42\0_\0T\0R\0\x41\0\x43\0\x45\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\0_\0P\0\x41\0N\0\x45\0L\x1\0\0\x1)\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0,\0t\0o\0o\0l\0\x62\0\x61\0r\0H\0\x42\0_\0T\0O\0G\0G\0L\0\x45\0_\0P\0\x41\0N\0\x45\0L\0\0\0\x2\xe5\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x32\0t\0o\0o\0l\0\x62\0\x61\0r\0H\0\x42\0_\0\x45\0M\0U\0L\0\x41\0T\0I\0O\0N\0_\0P\0\x41\0N\0\x45\0L\0\0\0\x2\xd6\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0&\0t\0o\0o\0l\0\x62\0\x61\0r\0\x46\0u\0s\0\x61\0\x45\0x\0\x63\0l\0M\0\x65\0n\0u\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x30\0t\0o\0o\0l\0\x62\0\x61\0r\0H\0\x42\0_\0P\0R\0O\0\x44\0T\0Y\0P\0\x45\0_\0P\0\x41\0N\0\x45\0L\0\0\0\x2\xfa\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0<\0\x41\0\x42\0V\0_\0\x41\0\x44\0\x44\0_\0T\0\x45\0M\0P\0O\0R\0\x41\0R\0Y\0_\0\x41\0S\0S\0\x45\0R\0T\0_\0P\0\x41\0N\0\x45\0L\0\0\0\x2\xe8\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x1e\0U\0V\0M\0_\0\x41\0W\0\x41\0R\0\x45\0_\0\x44\0\x45\0\x42\0U\0G\0\0\0\x3\x3\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0 \0V\0\x43\0_\0\x41\0P\0P\0S\0_\0T\0O\0O\0L\0_\0\x42\0O\0X\x1\0\0\x2\x87\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x32\0t\0o\0o\0l\0\x42\0\x61\0r\0\x46\0o\0r\0m\0\x61\0l\0V\0\x65\0r\0i\0\x66\0i\0\x63\0\x61\0t\0i\0o\0n\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x1e\0t\0o\0o\0l\0\x42\0\x61\0r\0\x43\0o\0v\0\x65\0r\0\x61\0g\0\x65\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x16\0t\0o\0o\0l\0\x42\0\x61\0r\0\x45\0x\0\x63\0l\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x1c\0t\0o\0o\0l\0\x42\0\x61\0r\0V\0\x63\0\x66\0T\0o\0o\0l\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0$\0t\0o\0o\0l\0\x42\0\x61\0r\0S\0h\0o\0w\0H\0i\0\x64\0\x65\0\x43\0o\0v\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x14\0L\0O\0G\0_\0V\0I\0\x45\0W\0\x45\0R\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x1e\0t\0o\0o\0l\0\x42\0\x61\0r\0I\0\x63\0o\0\x45\0x\0\x63\0l\0u\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0@\0S\0\x45\0\x41\0R\0\x43\0H\0P\0\x41\0N\0\x45\0_\0Q\0U\0I\0\x43\0K\0L\0\x41\0U\0N\0\x43\0H\0\x45\0R\0_\0T\0O\0O\0L\0\x42\0\x41\0R\x1\0\0\x2\xce\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x2\0\0\0\x2\0\0\0\x30\0N\0O\0V\0\x41\0S\0_\0T\0\x42\0\x42\0R\0_\0\x44\0\x45\0\x42\0U\0G\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0&\0H\0\x42\0_\0\x42\0\x41\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\0_\0P\0\x41\0N\0\x45\0L\0\0\0\x1\xfb\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x2\0\0\0\x4\0\0\0>\0N\0O\0V\0\x41\0S\0_\0T\0\x42\0\x42\0R\0_\0\x44\0\x45\0\x42\0U\0G\0_\0R\0\x45\0W\0I\0N\0\x44\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\x1\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0R\0N\0O\0V\0\x41\0S\0_\0T\0\x42\0\x42\0R\0_\0\x44\0\x45\0\x42\0U\0G\0_\0R\0\x45\0W\0I\0N\0\x44\0_\0U\0N\0\x44\0O\0_\0R\0\x45\0\x44\0O\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\x1\0\0\x1\x1c\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0@\0N\0O\0V\0\x41\0S\0_\0T\0\x42\0\x42\0R\0_\0\x44\0\x45\0\x42\0U\0G\0_\0R\0\x45\0V\0\x45\0R\0S\0\x45\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x38\0H\0\x42\0_\0P\0O\0W\0\x45\0R\0_\0T\0R\0\x41\0\x43\0\x45\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\0_\0P\0\x41\0N\0\x45\0L\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x2\0\0\0\x1\0\0\0:\0N\0O\0V\0\x41\0S\0_\0T\0\x42\0\x42\0R\0_\0\x44\0\x45\0\x42\0U\0G\0_\0V\0S\0I\0M\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x2\0\0\0\x1\0\0\0:\0N\0O\0V\0\x41\0S\0_\0\x45\0M\0U\0L\0\x41\0T\0I\0O\0N\0_\0\x44\0\x45\0\x42\0U\0G\0_\0\x43\0O\0M\0M\0\x41\0N\0\x44\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\x2\0\0\0\x1\0\0\0\x1a\0\x43\0V\0G\0_\0\x43\0\x45\0R\0_\0P\0\x41\0N\0\x45\0L\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0)"
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\isNestedWindow=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\size=@Size(2560 1497)
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\geometry_x=-1
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\geometry_y=27
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\geometry_width=2560
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\geometry_height=1497
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\svtbClassBrowser=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\svtbObjectBrowser=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\svtbMember=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\svtbStack=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\svtbLocal=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\InteractiveConsole=1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_6=widgetDock_svtbObjectBrowser_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_7=widgetDock_svtbMember_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_8=widgetDock_svtbStack_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_9=widgetDock_svtbLocal_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_10=windowDock_nWave_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_11=windowDock_InteractiveConsole_1
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindowMgr_saveDockerChildList\Verdi_1_12=widgetDock_svtbWatch_1
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_InteractiveConsole_1\isNestedWindow=1
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_InteractiveConsole_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_InteractiveConsole_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_InteractiveConsole_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\windowDock_InteractiveConsole_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbLocal_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbLocal_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbLocal_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbLocal_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbWatch_1\isVisible=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbWatch_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbWatch_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbWatch_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbStack_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbStack_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbStack_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbStack_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbClassBrowser_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbClassBrowser_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbClassBrowser_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbClassBrowser_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbObjectBrowser_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbObjectBrowser_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbObjectBrowser_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbObjectBrowser_1\dockIsFloating=false
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbMember_1\isVisible=true
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbMember_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbMember_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_svtbMember_1\dockIsFloating=false
qDockerWindowMgr_C\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qDockerWindow_qDockContentType\signalList=1
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_signalList_1\qBaseWindowBeMax=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_signalList_1\qBaseWindowBeFix=0
Verdi_1\qBaseWindowRestoreStateGroup\qBaseWindow_saveRestoreSession_group\qBaseDockWidgetGroup\widgetDock_signalList_1\dockIsFloating=false
