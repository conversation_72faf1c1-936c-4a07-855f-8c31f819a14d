@verdi rc file Version 1.0
[General]
saveDB = TRUE
relativePath = FALSE
saveSingleView = FALSE
saveNWaveWinId = 
VerdiVersion = Verdi_V-2023.12-SP2
[KeyNote]
Line1 = Automatic Backup 0
Line2 = Save Open Database Information: Yes
Line3 = Path Option: Absolute Paths
Line4 = Windows Option: All Windows
[OneSearch]
isExisted = TRUE
[TestBench]
ConstrViewShow = 0
InherViewShow = 0
FSDBMsgShow = 0
AnnotationShow = 0
Console = TRUE
powerDumped = 0
[fusa]
fdb_server = 
fdb_proj = 
fdb_campaign = 
fdb_tool = 
[hb]
postSimFile = /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
syncTime = 65000
viewport = 0 0 2560 1497 0 0 551 2558
activeNode = "tb_sideband_out"
activeScope = "tb_sideband_out"
activeFile = "../../../nonblocking/src/sv/tb_sideband_out.sv"
interactiveMode = False
viewType = Source
simulatorMode = False
sourceBeginLine = 81
baMode = False
srcLineNum = True
AutoWrap = True
IdentifyFalseLogic = False
syncSignal = False
traceMode = Hierarchical
showTraceInSchema = True
paMode = False
funcMode = False
powerAwareAnnot = True
safetyAwareAnnot = True
amsAnnot = True
DnDtraceCrossHierOnly = True
leadingZero = False
signalPane = True
SourceVerticalScrollLines = 5
Scope1 = "tb_sideband_out"
Scope2 = "tb_sideband_out.u_bfm"
sdfCheckUndef = FALSE
simFlow = FALSE
[hb.design]
importCmd = "-simflow" "-dbdir" "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir" 
invokeDir = /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs
[hb.sourceTab.1]
scope = tb_sideband_out
File = /trunk/hj/sideband_out/BFM/tests/nonblocking/src/sv/tb_sideband_out.sv
Line = 82
[nMemoryManager]
WaveformFile = /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb
UserActionNum = 0
nMemWindowNum = 0
[wave.0]
viewPort = 0 29 2560 586 410 120
primaryWindow = TRUE
SessionFile = /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/novas_autosave.ses.wave.0
displayGrid = FALSE
hierarchicalName = FALSE
waveformToolTip = FALSE
snap = TRUE
displayLeadingZeros = FALSE
fixDelta = FALSE
displayCursorMarker = FALSE
autoUpdate = FALSE
highlightGlitchs = FALSE
waveformSyncCursorMarker = FALSE
waveformSyncHorizontalRange = FALSE
waveformSyncVerticalscroll = FALSE
displayErrors = TRUE
displayMsgSymbols = TRUE
showMsgDescriptions = TRUE
autoFit = FALSE
displayDeltaY = FALSE
centerCursor = FALSE
fsaRegMask = TRUE
fsaCoreMask = TRUE
fsaSfsmMask = TRUE
fsaEcrMask = TRUE
tPropMask = TRUE
