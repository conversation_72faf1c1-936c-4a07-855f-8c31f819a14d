Command: /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking -l vcs.log
Chronologic VCS simulator copyright 1991-2023
Contains Synopsys proprietary information.
Compiler version V-2023.12-SP2_Full64; Runtime version V-2023.12-SP2_Full64;  Jul 20 17:51 2025
*Verdi* Loading libsscore_vcs202312.so
FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
(C) 1996 - 2024 by Synopsys, Inc.
*Verdi* FSDB WARNING: The FSDB file already exists. Overwriting the FSDB file may crash the programs that are using this file.
*Verdi* : Create FSDB file 'novas.fsdb'
*Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
*Verdi* : End of traversing.
*Verdi* : fsdbDumpon - All FSDB files at 0 ps.
Test Started: Non-blocking mode
@55000 TC1: Triggering signal 1
@75000 TC1: Asserting valid for signal 1
@205000 TC2: Triggering signal 2
@225000 TC2: Asserting valid for signal 2
@365000 TC3: Triggering signal 3 and valid simultaneously
Test Finished
$finish called from file "../../../nonblocking/src/sv/tb_sideband_out.sv", line 111.
$finish at simulation time               595000
           V C S   S i m u l a t i o n   R e p o r t 
Time: 595000 ps
CPU Time:      0.350 seconds;       Data structure size:   0.0Mb
Sun Jul 20 17:51:13 2025
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        V C S   S i m u l a t i o n   R e p o r t 
Time: 595000 ps
CPU Time:      0.720 seconds;       Data structure size:   0.0Mb
Sun Jul 20 19:05:11 2025
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 1
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollDown -win $_nWave3 0
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 1
wvScrollUp -win $_nWave3 10
wvSelectSignal -win $_nWave3 {( "G1" 16 )} 
wvSetPosition -win $_nWave3 {("G1" 16)}
wvExpandBus -win $_nWave3
wvSetPosition -win $_nWave3 {("G1" 56)}
wvScrollDown -win $_nWave3 1
wvSetCursor -win $_nWave3 75317.101219 -snap {("G1" 23)}
wvSetCursor -win $_nWave3 92832.706154 -snap {("G1" 23)}
wvSetCursor -win $_nWave3 143336.033716 -snap {("G1" 23)}
wvSetCursor -win $_nWave3 267696.828752 -snap {("G1" 22)}
wvSetCursor -win $_nWave3 368995.410624 -snap {("G1" 21)}
wvSetCursor -win $_nWave3 387386.795806 -snap {("G1" 21)}
srcTBInvokeSim
Verdi>/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking -l vcs.log +UVM_VERDI_TRACE=UVM_AWARE +fsdb+gate=off -ucli2Proc -ucli
Chronologic VCS simulator copyright 1991-2023
Contains Synopsys proprietary information.
Compiler version V-2023.12-SP2_Full64; Runtime version V-2023.12-SP2_Full64;  Jul 20 19:06 2025
*Verdi* Loading libsscore_vcs202312.so
FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
(C) 1996 - 2024 by Synopsys, Inc.
*Verdi* : Create FSDB file '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb'
*Verdi* : Flush all FSDB Files at 0 ps.
*Verdi* : Enable RPC Server(62329)
Verdi>fsdbDumpvars 0 "tb_sideband_out.u_bfm.clk"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.rst_n"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.sideband_in"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.sideband_valid"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.INSTANCE_ID"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.NUM_SIGNALS"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.DELAY_WIDTH"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.configured_delays"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.sideband_valid_d"  +all +trace_process;fsdbDumpvars 0 "tb_sideband_out.u_bfm.is_blocking_mode"  +all +trace_process
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.clk).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.rst_n).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_valid).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.INSTANCE_ID).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.NUM_SIGNALS).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.DELAY_WIDTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.configured_delays).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_valid_d).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.is_blocking_mode).
*Verdi* : Flush all FSDB Files at 0 ps.
Verdi>run
[SW Bridge] Initializing for BFM ID 0 with 8 signals.
[SW Bridge] Setting mode to non-blocking (0).
[SW Bridge] Setting delay for signal 1 to 5 cycles.
[SW Bridge] Setting delay for signal 3 to 5 cycles.
[SW Bridge] Setting delay for signal 5 to 5 cycles.
[SW Bridge] Setting delay for signal 7 to 5 cycles.
*Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
*Verdi* : End of traversing.
*Verdi* : fsdbDumpon - All FSDB files at 0 ps.
Test Started: Non-blocking mode
@55000 TC1: Triggering signal 1
@75000 TC1: Asserting valid for signal 1
[SW Bridge] Received event from BFM ID 0: Signal 1 -> 1
@205000 TC2: Triggering signal 2
@225000 TC2: Asserting valid for signal 2
[SW Bridge] Received event from BFM ID 0: Signal 1 -> 1
[SW Bridge] Received event from BFM ID 0: Signal 2 -> 0
[SW Bridge] Received event from BFM ID 0: Signal 2 -> 0
@365000 TC3: Triggering signal 3 and valid simultaneously
[SW Bridge] Received event from BFM ID 0: Signal 3 -> 0
[SW Bridge] Received event from BFM ID 0: Signal 3 -> 0
Test Finished
$finish called from file "../../../nonblocking/src/sv/tb_sideband_out.sv", line 111.
$finish at simulation time               595000
Simulation complete, time is 595000 ps.
tb_sideband_out.sv, 111 :     $finish;
Verdi>fsdbDumpvars 1 "tb_sideband_out.u_bfm.FIFO_DEPTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.SIGNAL_IDX_WIDTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.current_event"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_wr_data"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_rd_data"  +all +trace_process;fsdbDumpflush 

*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.FIFO_DEPTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.SIGNAL_IDX_WIDTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.current_event).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_data).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_data).
*Verdi* : Flush all FSDB Files at 595,000 ps.

