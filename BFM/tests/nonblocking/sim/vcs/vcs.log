/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking_basic -l vcs.log +UVM_VERDI_TRACE=UVM_AWARE +fsdb+gate=off -ucli2Proc -ucli
debImport "-i" "-simflow" "-dbdir" \
          "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/./simv.daidir"
Chronologic VCS simulator copyright 1991-2023
Contains Synopsys proprietary information.
Compiler version V-2023.12-SP2_Full64; Runtime version V-2023.12-SP2_Full64;  Jul 20 23:17 2025

verdiSetActWin -dock widgetDock_MTB_SOURCE_TAB_1
*Verdi* Loading libsscore_vcs202312.so

FSDB Dumper for VCS, Release Verdi_V-2023.12-SP2, Linux x86_64/64bit, 05/26/2024
(C) 1996 - 2024 by Synopsys, Inc.
*Verdi* : Create FSDB file '/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb'
*Verdi* : Flush all FSDB Files at 0 ps.

*Verdi* : Enable RPC Server(115928)

srcTBInvokeSim
Verdi>fsdbDumpvars 1 "tb_sideband_out.u_bfm.clk"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.rst_n"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.INSTANCE_ID"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.NUM_SIGNALS"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.DELAY_WIDTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.QUEUE_DEPTH"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.configured_delay"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.sideband_in_d"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.proc_state"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.delay_counter"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.is_blocking_mode"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.current_transition"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_wr_en"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_rd_en"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_full"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_empty"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_wr_data"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.fifo_rd_data"  +all +trace_process;fsdbDumpvars 1 "tb_sideband_out.u_bfm.vector_changed"  +all +trace_process;fsdbDumpflush 

*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.clk).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.rst_n).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.INSTANCE_ID).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.NUM_SIGNALS).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.DELAY_WIDTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.QUEUE_DEPTH).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.configured_delay).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.sideband_in_d).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.proc_state).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.delay_counter).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.is_blocking_mode).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.current_transition).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_en).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_en).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_full).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_empty).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_wr_data).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.fifo_rd_data).
*Verdi* : Dumping the signal (tb_sideband_out.u_bfm.vector_changed).
*Verdi* : Flush all FSDB Files at 0 ps.

Verdi>run
[SW Bridge] Initializing for BFM ID 0 with 8 signals.
[SW Bridge] Setting mode to non-blocking (0).
[SW Bridge] Setting unified delay to 5 cycles for all signals.
*Verdi* : Begin traversing the scope (tb_sideband_out), layer (0).
*Verdi* : End of traversing.
*Verdi* : fsdbDumpon - All FSDB files at 0 ps.
Test Started: Non-blocking mode with vector-based notifications
@55000 TC1: Changing bit 1 (0->1)
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
@175000 TC2: Sequential bit changes
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
@355000 TC3: Multiple bits changing simultaneously
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
[SW Bridge] Received vector transition from BFM ID 0: 0xFFFF72CC -> 0xFFFF72D0
[SW Bridge]   Bit 2: 1 -> 0
[SW Bridge]   Bit 3: 1 -> 0
[SW Bridge]   Bit 4: 0 -> 1
Test Finished
$finish called from file "../../../nonblocking/src/sv/tb_sideband_out.sv", line 96.
$finish at simulation time               475000
Simulation complete, time is 475000 ps.
tb_sideband_out.sv, 96 :     $finish;
