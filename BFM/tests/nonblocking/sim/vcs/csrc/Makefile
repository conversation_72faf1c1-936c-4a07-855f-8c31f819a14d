# Makefile generated by VCS to build your model
# This file may be modified; VCS will not overwrite it unless -Mupdate is used

# define default verilog source directory 
VSRC=..

# Override TARGET_ARCH
TARGET_ARCH=

# Choose name of executable 
PRODUCTBASE=$(VSRC)/simv

PRODUCT=$(PRODUCTBASE)

# Product timestamp file. If product is newer than this one,
# we will also re-link the product.
PRODUCT_TIMESTAMP=product_timestamp

# Path to runtime library
DEPLIBS=
VCSUCLI=-lvcsucli
LLVM_FLAGS=
RUNTIME=-lvcsnew -lsimprofile -luclinative /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_tls.o $(DEPLIBS) 

VCS_SAVE_RESTORE_OBJ=/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_save_restore_new.o

# Select your favorite compiler

# Linux:
# Compiler: 
VCS_CC=gcc
# Internal CC for gen_c flow:
CC_CG=gcc
# Loader
LD=g++
STDLIB = 

# Strip Flags for target product
STRIPFLAGS= 

PRE_LDFLAGS= # Loader Flags
LDFLAGS= -rdynamic  -Wl,-rpath='$$ORIGIN'/simv.daidir -Wl,-rpath=./simv.daidir -Wl,-rpath=/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib -L/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib 
# Picarchive Flags
PICLDFLAGS= -Wl,-rpath-link=./ 
PIC_LD=ld

# C run time startup
CRT0=
# C run time startup
CRTN=
# Machine specific libraries
SYSLIBS=/opt/synopsys/verdi/V-2023.12-SP2/share/PLI/VCS/LINUX64/pli.a -ldl  -lc -lm -lpthread -ldl

# Default defines
SHELL=/bin/sh

VCSTMPSPECARG=
VCSTMPSPECENV=
# NOTE: if you have little space in $TMPDIR, but plenty in /foo,
#and you are using gcc, uncomment the next line
#VCSTMPSPECENV=SNPS_VCS_TMPDIR=/foo

TMPSPECARG=$(VCSTMPSPECARG)
TMPSPECENV=$(VCSTMPSPECENV)
CC=$(TMPSPECENV) $(VCS_CC) $(TMPSPECARG)

# C flags for compilation
CFLAGS=$(STDLIB)-w  -pipe -fPIC -g -I../../../nonblocking/src/cpp -I/opt/synopsys/vcs/V-2023.12-SP2/include   

CFLAGS_O0=-w  -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include -O0  -fno-strict-aliasing -fno-optimize-sibling-calls   

CFLAGS_CG=-w  -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include -O  -fno-strict-aliasing -fno-optimize-sibling-calls   

CFLAGS_CG_CLIB=-w  -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include -O  -fno-strict-aliasing -fno-optimize-sibling-calls   

CFLAGS_RMAR=-w  -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include -O  -fno-strict-aliasing -fno-optimize-sibling-calls   

CFLAGS_CG+=$(STDLIB)
CFLAGS_CG_CLIB+=$(STDLIB)
CFLAGS_O0+=$(STDLIB)
LD_PARTIAL_LOADER=ld
# Partial linking
LD_PARTIAL=$(LD_PARTIAL_LOADER) -r -o
LIBS=-lnuma -lvirsim -lerrorinf -lsnpsmalloc -lvfs 
# Note: if make gives you errors about include, either get gmake, or
# replace the following line with the contents of the file filelist,
# EACH TIME IT CHANGES
# included file defines OBJS, and is automatically generated by vcs
include filelist

OBJS=$(VLOG_OBJS)  $(SYSC_OBJS)   $(VHDL_OBJS) 

product : $(PRODUCT_TIMESTAMP)
	@echo $(PRODUCT) up to date

objects : $(OBJS) $(DPI_STUB_OBJS) $(PLI_STUB_OBJS) 

clean   :
	rm -f $(VCS_OBJS) $(CU_OBJS)

clobber : clean
	rm -f $(PRODUCT) $(PRODUCT_TIMESTAMP) 

elabMhIdDidWork_concat : 
	@ls ../elabMhIdDidWork.txt_* &>/dev/null && cat ../elabMhIdDidWork.txt_* >> ../elabMhIdDidWork.txt || true

picclean : 
	rm -f _cuarc*.so _csrc*.so pre_vcsobj_*.so share_vcsobj_*.so
	@rm -f $(PRODUCT).daidir/_[0-9]*_archive_*.so $(PRODUCT).daidir/_cuarc*.so 2>/dev/null

product_clean_order : 
	@$(MAKE) -f Makefile --no-print-directory picclean
	@$(MAKE) -f Makefile --no-print-directory product_order

product_order : $(PRODUCT)

$(PRODUCT_TIMESTAMP) : elabMhIdDidWork_concat product_clean_order
	-if [ -x $(PRODUCT) ]; then chmod a-x $(PRODUCT); fi
	$(LD) $(CRT0) -o $(PRODUCT) $(LLVM_FLAGS) $(STDLIB) $(PRE_LDFLAGS) $(STRIPFLAGS) $(PCLDFLAGS) $(LDFLAGS) $(PICLDFLAGS) $(OBJS)  $(LIBS)   $(RUNTIME) -Wl,-whole-archive $(VCSMXSAIF) $(VCSUCLI) $(PLILIBS) $(DECLIBS) $(BALIBS) -Wl,-no-whole-archive     $(LINK_TB) $(DPI_STUB_OBJS) $(PLI_STUB_OBJS)   $(VCS_SAVE_RESTORE_OBJ) $(SYSLIBS) $(CRTN)
	@rm -f csrc[0-9]*.o
	@rm -f cuarc[0-9]*.o
	@rm -f cuarcall.o
	@rm -f localsym.txt
	@touch $(PRODUCT_TIMESTAMP)
	@-if [ -d ./objs ]; then find ./objs -type d -empty -delete; fi

$(PRODUCT) :  $(LD_VERSION_CHECK)  $(OBJS) $(DOTLIBS) $(DPI_STUB_OBJS) $(PLI_STUB_OBJS) $(CMODLIB) $(SYSC_STUB_LIB) $(DPI_ALLOW_MAIN_SO)  /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvcsnew.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libsimprofile.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libuclinative.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/vcs_tls.o /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvcsucli.so  $(VCS_SAVE_RESTORE_OBJ) 
	@touch $(PRODUCT)

