{"vcs1":{"timestamp_begin":1753005065.000678869, "rt":2.58, "ut":0.33, "st":0.53}}
{"vcselab":{"timestamp_begin":1753005067.677702186, "rt":2.10, "ut":0.35, "st":0.29}}
{"link":{"timestamp_begin":1753005069.869840927, "rt":0.31, "ut":0.16, "st":0.16}}
{"SNPS_VCS_INTERNAL_ROOT_START_TIME": 1753005064.271608156}
{"VCS_COMP_START_TIME": 1753005064.271608156}
{"VCS_COMP_END_TIME": 1753005070.392297508}
{"VCS_USER_OPTIONS": "-full64 -sverilog -debug_access+all -timescale=1ns/1ps -kdb -lca +vpi +define+DEBUG_BFM +define+DUMP_FSDB +incdir+../../../../src +incdir+../../../nonblocking/src/sv ../../../../src/sideband_out_bfm.sv ../../../../src/fifo.sv ../../../nonblocking/src/sv/tb_sideband_out.sv -top tb_sideband_out -o simv -l vcs_comp.log -CFLAGS -g -I../../../nonblocking/src/cpp"}
{"vcs1": {"peak_mem": 701472}}
{"vcselab": {"peak_mem": 426472}}
