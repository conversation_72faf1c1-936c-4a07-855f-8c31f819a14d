#ifndef __DO_RMAHDR_
#define __DO_RMAHDR_

#ifdef __cplusplus
  extern "C" {
#endif

#define VCS_RTLIB_TLS_MODEL  __attribute__((tls_model("initial-exec")))

typedef unsigned long UP;
typedef unsigned U;
typedef unsigned char UB;
typedef unsigned short US;
typedef unsigned long long ULL;
typedef unsigned char scalar;
typedef struct vec32 vec32;
typedef unsigned char SVAL;
typedef unsigned char TYPEB;
typedef struct etype ETYPE;
typedef struct qird QIRD;
typedef unsigned char UST_e;
typedef unsigned uscope_t;
typedef U  NumLibs_t;
struct vec32 {
    U  I1;
    U  I2;
};
typedef unsigned long long InstNumType;
typedef long long InstNumSigned;
typedef InstNumType  InstNum;
typedef U  InstNum32;
typedef unsigned long RP;
typedef unsigned long RO;
typedef U  GateCount;
typedef U  NodeCount;
typedef unsigned short HsimEdge;
typedef unsigned char HsimExprChar;
typedef struct {
    U  I650;
    RP  I651;
} RmaReceiveClock1;
typedef NodeCount  FlatNodeNum;
typedef unsigned ProcessNum;
typedef unsigned long long TimeStamp64;
typedef unsigned long long TimeStamp;
typedef enum {
    PD_SING = 0,
    PD_RF = 1,
    PD_PLSE = 2,
    PD_PLSE_RF = 3,
    PD_NULL = 4
} PD_e;
typedef TimeStamp64  RmaTimeStamp64;
typedef TimeStamp  RmaTimeStamp;
typedef struct {
    scalar  I653;
    U  I654;
    RmaTimeStamp64  I655;
} MsidInfo;
typedef union {
    double I662;
    unsigned long long I663;
    unsigned  I664[2];
} rma_clock_struct;
typedef struct eblk EBLK;
typedef int (* E_fn)(void);
typedef struct eblk {
    E_fn  I671;
    struct iptmpl * I672;
    struct eblk * I674;
    unsigned I675;
    unsigned I676;
    struct eblk * I677;
    unsigned I256;
} eblk_struct;
typedef struct {
    RP  I671;
    RP  I672;
    RP  I674;
    unsigned I675;
    unsigned I676;
    RP  I677;
    unsigned I256;
} RmaEblk;
typedef struct {
    RP  I671;
    RP  I672;
    RP  I674;
    unsigned I675;
    unsigned I676;
    RP  I677;
    unsigned I256;
    unsigned val;
} RmaEblklq;
typedef struct _DEblkLaneT {
    struct _DEblkLaneT * I684;
    struct eblk * I686;
} DEblkLaneT;
typedef struct _DEblkPoolT {
    struct _DEblkLaneT * I687;
    struct _DEblkLaneT * I688;
    unsigned I689;
    unsigned I690;
} DEblkPoolT;
typedef union {
    double I662;
    unsigned long long I663;
    unsigned  I664[2];
} clock_struct;
typedef struct RmaRetain_t RmaRetain;
struct RmaRetain_t {
    RP  I722;
    RmaEblk  I670;
    ULL  I724;
    US  I725 :1;
    US  I726 :4;
    US  I234 :2;
    US  state :2;
    US  I728 :1;
    US  I729 :2;
    US  I730 :2;
    US  fHsim :1;
    US  I565 :1;
    scalar  newval;
    scalar  I733;
    RP  I734;
};
struct retain_t {
    struct retain_t * I722;
    EBLK  I670;
    ULL  I724;
    US  I725 :1;
    US  I726 :4;
    US  I234 :2;
    US  state :2;
    US  I728 :1;
    US  I729 :2;
    US  I730 :2;
    US  fHsim :1;
    US  I731 :1;
    scalar  newval;
    scalar  I733;
    void * I734;
};
typedef struct MPSched MPS;
typedef struct RmaMPSched RmaMps;
struct MPSched {
    MPS  * I712;
    scalar  I713;
    scalar  I714;
    scalar  I715;
    scalar  fHsim :1;
    scalar  I717 :1;
    scalar  I234 :6;
    U  I718;
    EBLK  I719;
    void * I720;
    UP   I721[1];
};
struct RmaMPSched {
    RP  I712;
    scalar  I713;
    scalar  I714;
    scalar  I715;
    scalar  fHsim :1;
    scalar  I717 :1;
    scalar  I234 :6;
    U  I718;
    RmaEblk  I719;
    RP  I720;
    RP   I721[1];
};
typedef struct RmaMPSchedPulse RmaMpsp;
struct RmaMPSchedPulse {
    RP  I712;
    scalar  I713;
    scalar  I714;
    scalar  I715;
    scalar  I717 :1;
    scalar  I234 :6;
    scalar  I739 :1;
    U  I718;
    RmaEblk  I719;
    scalar  I730;
    scalar  I740;
    scalar  I741;
    scalar  I742;
    U  I743;
    U  I744;
    U  I745;
    U  state;
    U  I746;
    RP  I672;
    RP  I747;
    RP  I748;
    RP   I721[1];
};
typedef struct MPItem MPI;
struct MPItem {
    U  * I750;
    void * I751;
};
typedef struct {
    RmaEblk  I670;
    RP  I752;
    scalar  I753;
    scalar  I730;
    scalar  I754;
} RmaTransEventHdr;
typedef struct RmaMPSchedPulseNewCsdf RmaMpspNewCsdf;
struct RmaMPSchedPulseNewCsdf {
    RP  I712;
    scalar  I713;
    scalar  I714;
    scalar  I715;
    scalar  fHsim :1;
    scalar  I717 :1;
    scalar  I234 :6;
    U  I718;
    RmaEblk  I719;
    scalar  I730;
    scalar  I740;
    scalar  I741;
    scalar  I742;
    U  state :4;
    U  I756 :28;
    U  I744;
    U  I745;
    RP  I757;
    RP  I672;
    RP  I758;
    RP   I721[1];
};
typedef struct red_t {
    U  I759;
    U  I760;
    U  I628;
} RED;
typedef struct predd {
    PD_e  I234;
    RED   I761[0];
} PREDD;
union rhs_value {
    vec32  I762;
    scalar  I753;
    vec32  * I730;
    double I763;
    U  I764;
};
typedef struct nbs_t {
    struct nbs_t * I765;
    struct nbs_t * I767;
    void (* I768)(struct nbs_t * I734);
    U  I769 :1;
    U  I770 :1;
    U  I771 :1;
    U  I772 :1;
    U  I773 :1;
    U  I774 :1;
    U  I775 :26;
    U  I776;
    void * I777;
    union rhs_value I778;
    vec32  I662;
    union {
        struct nbs_t * I1270;
        struct nbs_t * last;
    } I780;
} NBS;
typedef struct {
    RP  I781;
    RP  I747;
    RP  I672;
    RP  I748;
    RmaEblk  I670;
    RmaEblk  I782;
    RP  I783;
    scalar  I753;
    scalar  I730;
    char state;
    uscope_t  I784;
    U  I785;
    RP  I786;
    scalar  I740;
    scalar  I741;
    scalar  I742;
    U  I744;
    U  I745;
    RP  I720;
} RmaPulse;
typedef enum {
    QIRDModuleC = 1,
    QIRDSVPackageC = 2,
    QIRDSpiceModuleC = 3
} QIRDModuleType;
typedef struct {
    U  I790 :1;
    U  I791 :1;
    U  I792 :1;
    U  I793 :1;
    U  I794 :1;
    U  I795 :1;
    U  I796 :1;
    U  I797 :1;
    U  I798 :1;
    U  I799 :1;
    U  I800 :1;
    U  I801 :1;
    U  I802 :1;
    U  I803 :1;
    U  I804 :1;
    U  I805 :1;
    U  I806 :1;
    U  I807 :1;
    U  I808 :1;
    QIRDModuleType  I809 :2;
    U  I810 :1;
    U  I811 :1;
    U  I812 :1;
    U  I813 :1;
    U  I814 :1;
    U  I815 :1;
    U  I816 :1;
    U  I817 :1;
    U  I818 :1;
    U  I819 :1;
    U  I820 :1;
    U  I821 :1;
    U  I822 :1;
    U  I823 :1;
    U  I824 :1;
    U  I825 :1;
    U  I826 :1;
    U  I827 :1;
    U  I828 :1;
    U  I829 :1;
    U  I830 :1;
    U  I831 :1;
    U  I832 :1;
    U  I833 :1;
    U  I834 :1;
    U  I835 :1;
    U  I836 :1;
    U  I837 :1;
    U  I838 :1;
    U  I839 :1;
    U  I840 :1;
    U  I841 :1;
    U  I842 :1;
    U  I843 :1;
    U  I844 :1;
    U  I845 :8;
} BitFlags;
typedef struct qird_dbg_flags_s {
    U  I846 :29;
    U  I847 :1;
    U  I848 :1;
    U  I849 :1;
} qird_dbg_flags_t;
typedef struct nlpInfoT {
    U  * I850;
    U  * I851;
    U  * I852;
    UP  * I853;
    UP  * I854;
    UP  * I855;
    UP  * I856;
    char * I857;
    UP  * I858;
    UP  * I859;
    U  * I860;
    UP  * I861;
    UP  * I862;
    UP  * I863;
    UP  * I864;
    UP  * I865;
    UP  * I866;
    UP  * I867;
    UP  * I868;
    UP  * I869;
    UP  * I870;
    UP  * I871;
    UP  * I872;
    UP  * I873;
    UP  * I874;
    char * I875;
    char * I876;
    U  * I877;
    UP  * I878;
    UP  * I879;
    UP  * I880;
    UP  * I881;
    UP  * I882;
    UP  * I883;
    UP  * I884;
    UP  * I885;
    UP  * I886;
    UP  * I887;
    UP  * I888;
    UP  * I889;
    UP  * I890;
    UP  * I891;
    UP  * I892;
    UP  * I893;
    U  I894;
    U  I895;
    U  I896;
    U  I897;
    U  I898;
    U  I899;
    U  I900;
    U  I901;
    U  I902;
    U  I903;
    U  I904;
    U  I905;
    U  I906;
    U  I907;
    U  I908;
    U  I909;
    U  I910;
    U  I911;
} nlpInfoT;
typedef struct txpInfoT {
    U  I912;
    U  I913;
    void * I914;
    void * I915;
    int (* I916)(void);
    int (* I917)(void);
    void * I918;
    void * I919;
    void * I920;
} txpInfoT;
typedef struct fsimInfoT {
    U  I921;
    U  I922;
    U  * I923;
    U  * I924;
    UP  * I925;
    UP  * I926;
    U  * I927;
    U  * I928;
    U  * I929;
} fsimInfoT;
typedef struct fgpInfoT {
    U  I930;
    U  I931;
    U  * I932;
    U  * I933;
    char * I934;
} fgpInfoT;
typedef struct peDummyModInfoT {
    char * I935;
    char * I936;
    char * I937;
} peDummyModInfoT;
typedef struct peModInfoT {
    char * I62;
    char * I938;
    char * I939;
    U  I940;
    peDummyModInfoT  * I941;
} peModInfoT;
typedef struct simProfInfoT {
    U  I942;
    U  I943;
    U  I944;
    U  * I945;
    U  * I946;
    U  * I947;
    U  * I948;
} simProfInfoT;
typedef struct hybridSimInfoT {
    U  I949;
    U  I631;
    U  I950;
    U  I951;
    U  I952;
    void * I953;
    U  * I954;
    struct hybridSimReferrerData * I955;
    struct hybridSimReferrerData * I957;
} hybridSimInfoT;
typedef struct classInfoT {
    void * I958;
    void * I959;
    void * I960;
    U  I961;
    U  I962;
    U  I963;
    U  I964;
    UP  I965;
    UP  I966;
    void * I967;
} classInfoT;
struct qird {
    US  I58;
    US  I59;
    U  I60;
    U  I61;
    char * I62;
    char * I63;
    U  * I64;
    char * I65;
    char * I66;
    U  I67;
    U  I68;
    struct vcd_rt * I69;
    U  I71;
    struct _vcdOffset_rt * I72;
    U  I74;
    U  I75;
    ETYPE  * I76;
    U  * I77;
    classInfoT  * I78;
    int I79;
    U  I80;
    U  * I81;
    U  * I82;
    UP  I83;
    UP  * I84;
    BitFlags  I85;
    U  I86;
    U  I87;
    U  I88;
    U  I89;
    U  * I90;
    U  I91;
    U  I92;
    U  * I93;
    U  I94;
    U  I95;
    U  I96;
    U  I97;
    U  I98;
    U  I99;
    U  I100;
    U  I101;
    U  * I102;
    U  * I103;
    U  * I104;
    U  I105;
    U  I106;
    U  * I107;
    U  I108;
    U  I109;
    U  I110;
    U  I111;
    U  * I112;
    U  I113;
    U  * I114;
    U  I115;
    U  I116;
    U  I117;
    U  I118;
    U  I119;
    U  I120;
    U  * I121;
    char * I122;
    U  I123;
    U  I124;
    U  I125;
    U  I126;
    U  * I127;
    U  I128;
    U  I129;
    U  I130;
    U  I131;
    UP  * I132;
    U  I133;
    U  I134;
    U  I135;
    U  I136;
    U  I137;
    U  I138;
    U  * I139;
    U  I140;
    U  I141;
    U  * I142;
    U  * I143;
    U  * I144;
    U  * I145;
    U  * I146;
    U  I147;
    U  I148;
    struct taskInfo * I149;
    U  I151;
    U  I152;
    U  I153;
    U  I154;
    int * I155;
    U  * I156;
    UP  * I157;
    U  * I158;
    U  I159;
    U  I160;
    int * I161;
    U  I162;
    U  I163;
    U  I164;
    U  I165;
    U  I166;
    U  I167;
    struct qrefer * I168;
    U  * I170;
    unsigned * I171;
    void * I172;
    U  I173;
    struct classStaticReferData * I174;
    U  I176;
    U  * I177;
    U  I178;
    U  * I179;
    U  I180;
    struct wakeupInfoStruct * I181;
    U  I183;
    U  I184;
    U  I185;
    U  * I186;
    U  I187;
    U  * I188;
    U  * I189;
    U  I190;
    U  I191;
    U  I192;
    U  I193;
    U  * I194;
    U  I195;
    U  I196;
    U  * I197;
    U  I198;
    U  I199;
    U  * I200;
    U  * I201;
    U  * I202;
    U  I203;
    U  I204;
    U  I205;
    U  I206;
    U  I207;
    U  I208;
    struct qdefrefee * I209;
    struct qrefee * I211;
    U  * I213;
    U  * I214;
    int (* I215)(void);
    char * I216;
    U  I217;
    U  I218;
    void * I219;
    void * I220;
    NumLibs_t  I221;
    char * I222;
    U  * I223;
    U  I224;
    U  I225;
    U  I226;
    U  I227;
    U  I228;
    U  * I229;
    U  * I230;
    int I231;
    struct clock_load * I232;
    int I247;
    struct clock_data * I248;
    int I264;
    struct clock_hiconn * I265;
    U  I269;
    U  I270;
    U  I271;
    U  I272;
    U  * I273;
    U  I274;
    U  I275;
    U  * I276;
    U  I277;
    void * I278;
    U  I279;
    U  I280;
    UP  * I281;
    void * I282;
    U  I283;
    UP  * I284;
    U  * I285;
    int (* I286)(void);
    U  * I287;
    UP  * I288;
    U  * I289;
    U  I290 :1;
    U  I291 :31;
    U  I292;
    U  I293;
    UP  * I294;
    U  * I295;
    U  I296 :1;
    U  I297 :1;
    U  I298 :1;
    U  I299 :1;
    U  I300 :28;
    U  I301;
    U  I302;
    U  I303;
    U  I304 :31;
    U  I305 :1;
    UP  * I306;
    UP  * I307;
    fsimInfoT  * I308;
    nlpInfoT  * I309;
    U  I310;
    U  I311;
    UP  * I312;
    UP  * I313;
    UP  * I314;
    UP  * I315;
    struct daidirInfo * I316;
    struct vcs_tftable * I318;
    U  I320;
    UP  * I321;
    UP  * I322;
    U  I323;
    U  I324;
    U  I325;
    UP  * I326;
    U  * I327;
    UP  * I328;
    UP  * I329;
    struct qird_hil_data * I330;
    U  I332;
    U  * I333;
    UP  (* I334)(void);
    UP  (* I335)(void);
    UP  (* I336)(void);
    UP  (* I337)(void);
    UP  (* I338)(void);
    int * I339;
    int (* I340)(void);
    char * I341;
    UP  * I342;
    UP  * I343;
    UP  (* I344)(void);
    int (* I345)(void);
    int * I346;
    int (* I347)(void);
    int (* I348)(void);
    int * I349;
    char * I350;
    U  * I351;
    U  * I352;
    U  * I353;
    U  * I354;
    void * I355;
    int (* I356)(void);
    U  I357;
    UP  * I358;
    UP  * I359;
    U  I360;
    void * I361;
    U  I362;
    U  I363;
    U  I364;
    U  I365;
    U  I366;
    U  I367;
    char * I368;
    UP  * I369;
    U  * I370;
    UP  * I371;
    U  * I372;
    U  * I373;
    U  I374 :15;
    U  I375 :14;
    U  I376 :1;
    U  I377 :1;
    U  I378 :1;
    U  I379 :3;
    U  I380 :1;
    U  I381 :1;
    U  I382 :17;
    U  I383 :3;
    U  I384 :5;
    U  I385 :1;
    U  I386 :1;
    U  I387;
    qird_dbg_flags_t  I388;
    struct scope * I389;
    U  I391;
    U  I392;
    U  I393;
    U  * I394;
    U  * I395;
    U  * I396;
    U  I397;
    U  I398;
    U  I399;
    U  I400;
    struct pcbt * I401;
    UP  * I410;
    U  I411;
    U  I412;
    U  I413;
    U  I414;
    void * I415;
    void * I416;
    void * I417;
    int I418;
    U  * I419;
    U  I420;
    U  I421;
    UP  * I422;
    U  I423;
    U  I424;
    U  I425;
    U  I426;
    U  I427;
    void * I428;
    UP  * I429;
    U  I430;
    U  I431;
    void * I432;
    U  I433;
    txpInfoT  * I434;
    U  I435;
    U  I436;
    U  I437;
    U  I438;
    U  I439;
    U  I440;
    char * I441;
    U  I442;
    U  * I443;
    U  I444;
    U  * I445;
    U  I446;
    U  I447;
    U  I448;
    U  I449;
    U  I450;
    U  I451;
    U  * I452;
    U  I453;
    U  I454;
    U  * I455;
    U  I456;
    U  I457;
    U  I458;
    U  * I459;
    char * I460;
    U  I461;
    U  I462;
    U  I463;
    simProfInfoT  * I464;
    U  I465;
    U  I466;
    UP  * I467;
    U  I468;
    U  I469;
    U  I470;
    struct cosim_info * I471;
    U  I473;
    U  * I474;
    hybridSimInfoT  * I475;
    U  * I476;
    U  I477;
    U  * I478;
    U  I479;
    U  I480;
    U  * I481;
    U  I482;
    U  I483;
    U  * I484;
    U  I485;
    U  * I486;
    U  I487;
    U  * I488;
    U  I489;
    U  I490;
    U  * I491;
    U  I492;
    U  * I493;
    U  I494;
    U  I495;
    U  * I496;
    struct mhdl_outInfo * I497;
    UP  * I499;
    U  I500;
    UP  * I501;
    U  I502;
    void * I503;
    U  * I504;
    void * I505;
    char * I506;
    U  I507;
    U  * I508;
    char * I509;
    U  I510;
    struct regInitInfo * I511;
    UP  * I513;
    U  * I514;
    char * I515;
    U  I516;
    U  I517;
    U  I518;
    U  I519;
    U  I520;
    U  I521;
    U  I522;
    U  I523;
    UP  * I524;
    U  I525;
    U  I526;
    U  I527;
    UP  * I528;
    U  I529;
    UP  * I530;
    U  I531;
    UP  * I532;
    U  I533;
    U  I534;
    U  I535;
    U  * I536;
    U  I537;
    U  I538;
    U  I539;
    U  * I540;
    U  * I541;
    UP  * I542;
    UP  * I543;
    void * I544;
    UP  I545;
    void * I546;
    void * I547;
    void * I548;
    void * I549;
    void * I550;
    UP  I551;
    U  * I552;
    U  * I553;
    void * I554;
    U  I555 :1;
    U  I556 :31;
    U  I557;
    U  I558;
    U  I559;
    int I560;
    U  I561 :1;
    U  I562 :1;
    U  I563 :1;
    U  I564 :1;
    U  I565 :28;
    void * I566;
    void * I567;
    void * I568;
    void * I569;
    void * I570;
    void * I571;
    void * I572;
    UP  * I573;
    U  * I574;
    U  I575;
    char * I576;
    U  * I577;
    U  * I578;
    U  * I579;
    char * I580;
    int * I581;
    int * I582;
    UP  * I583;
    struct etype * I584;
    U  I585;
    U  I586;
    U  * I587;
    struct etype * I588;
    U  I589;
    U  I590;
    U  I591;
    U  * I592;
    void * I593;
    U  I594;
    U  I595;
    void * I596;
    fgpInfoT  * I597;
    U  I598;
    peModInfoT  * I599;
    U  I600;
    struct covreg_rt * I601;
    U  I603;
    U  I604;
    U  * I605;
    U  * I606;
    U  I607;
    U  I608;
};
typedef struct pcbt {
    U  * I403;
    UP  I78;
    U  I404;
    U  I405;
    U  I406;
    U  I407;
    U  I408;
    U  I409;
} PCBT;
typedef struct {
    int * I968;
    int * I969;
    int I970;
    union {
        long long enumDesc;
        long long classId;
    } I971;
} TypeData;
struct etype {
    U  I4 :8;
    U  I5;
    U  I6;
    U  I7 :1;
    U  I8 :1;
    U  I9 :1;
    U  I10 :1;
    U  I11 :1;
    U  I12 :1;
    U  I13 :1;
    U  I14 :1;
    U  I15 :1;
    U  I16 :4;
    U  I17 :1;
    U  I18 :1;
    U  I19 :1;
    U  I20 :1;
    U  I21 :1;
    U  I22 :1;
    U  I23 :1;
    U  I24 :1;
    U  I25 :2;
    U  I26 :1;
    U  I27 :2;
    U  I28 :1;
    U  I29 :1;
    U  I30 :1;
    U  I31 :1;
    U  I32 :1;
    U  I33 :1;
    TypeData  * I34;
    U  I35;
    U  I36;
    U  I37 :1;
    U  I38 :1;
    U  I39 :1;
    U  I40 :1;
    U  I41 :2;
    U  I42 :2;
    U  I43 :1;
    U  I44 :1;
    U  I45 :1;
    U  I46 :1;
    U  I47 :1;
    U  I48 :1;
    U  I49 :1;
    U  I50 :1;
    U  I51 :1;
    U  I52 :1;
    U  I53 :1;
    U  I54 :1;
    U  I55 :1;
    U  I56 :11;
};
struct iptmpl {
    QIRD  * I678;
    void * I679;
    UP  I680;
    UP  I681;
    UP  I682;
    struct iptmpl * I683[2];
};
typedef unsigned long long FileOffset;
typedef struct _HsCgPeriod {
    U  I1032;
    U  I1033;
} HsCgPeriod;
typedef struct {
    U   I1034[2];
    U  I1035 :1;
    U  I1036 :1;
    U  I1037 :8;
    U  I1038 :8;
    U  I1039 :8;
    U  I1040 :4;
    U  I1041 :1;
    U  I1042 :1;
    unsigned long long I1043;
    unsigned long long I1044;
    unsigned long long I1045;
    unsigned long long I1046;
    unsigned long long I1033;
    U  I1032;
    U  I1047;
    U  I1048;
    U  I1049;
    U  I1050;
    U  I1051;
    HsCgPeriod  * I1052[10];
} HsimSignalMonitor;
typedef struct {
    ULL   I1053[4];
    ULL   I1054[4];
} HsimPostNbaClkMonitor;
typedef struct {
    InstNum  I1055;
    FlatNodeNum  I1056;
    U  I987;
    scalar  I1057;
    UB  I1058;
    UB  I1059;
    UB  I1060;
    UB  I1061;
    UB  I1062;
    UB  I1063;
    ULL  I1064;
    U  I1065;
    U  I1066;
    U  I1067;
    U  I1068;
    U  I1069;
    U  I1070;
    U  I1071;
    U  I1072;
    HsimSignalMonitor  * I1073;
    HsimPostNbaClkMonitor  * I1074;
    RP  I1075;
    RmaTimeStamp64  I655;
    U  I1076;
    RmaTimeStamp64  I1077;
    U  I1078;
    UB  I1079 :1;
    UB  I1080 :1;
    UB  I1081 :1;
    ULL  I1082;
    ULL  I1083;
    ULL  I1084;
    ULL  I1085;
    ULL  I1032;
    U  I1086;
    U  I1087;
    ULL  I1088;
    ULL  I1089;
    ULL  I1090;
    ULL  I1091;
    RP  I1092;
} HsimNodeRecord;
typedef struct {
    RP  I1100;
    RP  pcode;
} RmaIbfPcode;
typedef struct {
    RP  I1100;
    RP  I672;
} RmaIbfIp;
typedef struct {
    RP  I1100;
    RP  I672;
    U  I256;
} RmaIbfIp_RTS;
typedef struct {
    U  I768;
    InstNum32  I634;
} RmaIbfIpById;
typedef struct {
    U  I675;
    U  val;
} RmaDynamicEblklq;
typedef struct {
    U  I675;
    U  I676;
} RmaFgpDynamicEblklq;
typedef struct {
    U  val;
} RmaDynamicEblk;
typedef struct {
    scalar  I1101;
    UB  I1102;
    US  I1103;
} JaguarClockGlitch;
typedef struct {
    U  I658 :28;
    U  I1104 :1;
    U  I1105 :1;
    U  I1106 :1;
    U  I1107 :1;
    U  I1108;
    RP  I1109;
    RP  I1110;
    RP  I1111;
    U   I1112[1];
} RmaDbsedRtlLoads;
typedef struct {
    RP  I1113;
    RP  pfn;
    RP  pcode;
} RmaSeqPrimOutputEblkData;
typedef RP  RCICODE;
typedef struct {
    RmaEblk  I670;
} RmaEvTriggeredOrSyncLoadCg;
typedef struct {
    RO  I976;
    RP  pcode;
} SchedGateFanout;
typedef struct {
    RO  I976;
    RP  pcode;
    U   I1010[4];
} SchedSelectGateFanout;
typedef struct {
    RP  pcode;
    RmaEblklq  I670;
} SchedGateEblk;
typedef struct {
    RP  pcode;
    RmaEblklq  I670;
    UB  * I1114;
} SchedSelectGateEblk;
typedef struct {
    RP  pcode;
    RP  I671;
    RP  I672;
    UB  * I1114;
} SchedSelectGateCall;
typedef struct {
    RmaEblk  I670;
    RP  I1115;
} RmaAnySchedSampleSCg;
typedef struct {
    RmaEblk  I670;
    RP  I1114;
    RP  I1115;
    vec32  I1116;
} RmaAnySchedVCg;
typedef struct {
    RmaEblk  I670;
    RP  I1114;
    RP  I1115;
    vec32   I729[1];
} RmaAnySchedWCg;
typedef struct {
    RmaEblk  I670;
    RP  I1114;
    RP  I1115;
    scalar   I1117[1];
} RmaAnySchedECg;
typedef struct {
    U  I1118;
    U  I658;
    U  I987;
    U  I1119;
    RmaIbfIp  * I1120;
    EBLK  I670;
    void * val;
} RmaThreadSchedCompiledLoads;
typedef struct {
    U  I658;
    U  I666;
    RmaThreadSchedCompiledLoads  * I1121;
} RmaSchedCompileLoadsCg;
typedef RmaThreadSchedCompiledLoads  ** RmaThreadSchedCompiledLoadsArr;
typedef struct {
    U  I1122;
    U  I1123;
    RmaIbfIp  * I1124;
    RmaThreadSchedCompiledLoadsArr  I1125;
} RmaJaguarIbnSchedCg;
typedef struct {
    RP  I1126;
} RmaRootCbkCg;
typedef struct {
    RP  I1127;
} RmaRootForceCbkCg;
typedef struct {
    RmaEblk  I670;
    RP  I1128;
} RmaForceCbkJmpCg;
typedef struct {
    U  I59;
    U  I666 :31;
    U  I1129 :1;
    vec32  I762;
    U  I1130;
    RP  I1131;
    RP  I1132;
} RmaForceSelectorV;
typedef struct {
    U  I59;
    RmaIbfPcode  I1139;
} RmaNetTypeDriverGate;
typedef struct {
    U  I59;
    U  I612;
    RmaIbfPcode   I1139[1];
} RmaNetTypeScatterGate;
typedef struct {
    U  I59;
    RmaIbfPcode  I1139;
} RmaNetTypeGatherGate;
typedef struct {
    RmaIbfPcode  I1140;
    U  I1141 :3;
    U  I1142 :1;
    U  I1143 :1;
    U  I1144 :16;
} RmaNbaGateOfn;
typedef struct {
    U  I59;
    NBS  I1145;
    RmaIbfPcode  I1140;
} RmaNbaGate1;
typedef struct {
    RP  ptable;
    RP  pfn;
    RP  pcode;
} Rma1InputGateFaninCgS;
typedef struct RmaSeqPrimOutputS_ RmaSeqPrimOutputOnClkS;
struct RmaSeqPrimOutputS_ {
    RP  pfn;
    U  state;
    U  I1148;
    RP  I1149;
    scalar  val;
    scalar  I1150;
    U  I1151 :1;
    U  I1152 :1;
    U  I1153 :1;
    union {
        U  I650;
        U  I632;
    } I643;
    RP   I1154[1];
};
typedef struct RmaSimonFlopOutputV_ RmaSimonFlopOutputV;
typedef struct RmaSimonLatchOutputV_ RmaSimonLatchOutputV;
struct RmaSimonFlopOutputV_ {
    RP  pfn;
    RP  I1156;
    RP  I1157;
};
typedef struct RmaSimonResetFlopOutputV_ RmaSimonResetFlopOutputV;
struct RmaSimonResetFlopOutputV_ {
    RP  pfn;
    RP  I1156;
    RP  I1157;
    RP  I1161;
    scalar  I1162;
};
struct RmaSimonLatchOutputV_ {
    RP  pfn;
    RP  I1156;
    RP  I1157;
    scalar  I1159;
};
typedef struct {
    U  I59;
    U  iinput;
    UB  I1164;
    RP  I1165;
} RmaCondOptLoad;
typedef struct {
    U  I59;
    U  iinput;
    UB  I1164;
    RP  I1165;
} RmaMacroStateUpdate;
typedef struct {
    U  I59;
    U  state;
    U  I1166;
    UB  I1164;
    U  * I1167;
} RmaMacroState;
typedef struct {
    U  iinput;
    RP  I1168;
} RmaMultiInputLogicGateCg;
typedef struct {
    U  iinput;
    RP  ptable;
    RP  I1168;
} RmaSeqPrimEdgeInputCg;
typedef struct {
    RP  I1168;
} RmaSimonCellEdgeInputCg;
typedef struct {
    RmaEblk  I670;
    RP  pcode;
} RmaSched0GateCg;
typedef struct {
    RmaEblk  I670;
    RP  pcode;
    RP  pfn;
    scalar  I649;
    scalar  I1169;
} RmaUdpDeltaGateCg;
typedef struct {
    RmaEblk  I670;
    RP  pcode;
    RP  pfn;
    scalar  I1170;
} RmaSchedDeltaGateCg;
typedef struct {
    RP  pcode;
    RP  pfn;
    RmaDynamicEblk  I670;
    scalar  I1170;
    scalar  I1171;
    scalar  I1172;
    scalar  I565;
} RmaDynamicSchedDeltaGateCg;
typedef struct {
    UB  I1173;
    RP  I1174;
    RP  I1175;
} RmaPropNodeSeqLhsSCg;
typedef struct {
    RmaEblk  I670;
    RP  pcode;
    U  I987;
    U   I659[1];
} RmaBitEdgeEblk;
typedef struct {
    U  I59;
    U  I1176;
} RmaDelay;
typedef struct {
    U  I59;
    U  I1176;
    RP  I761;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaGateDelay;
typedef struct {
    U  I59;
    RP  I761;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaGateBehavioralDelay;
typedef struct {
    U  I59;
    U  I1176;
    union {
        RP  I1444;
        RP  I1760;
        RP  I1777;
    } I734;
    RmaIbfPcode  I1140;
} RmaMPDelay;
typedef struct {
    U  I59;
    U  I1176;
    RmaPulse  I1177;
    RmaIbfPcode  I1140;
} RmaMPPulseHybridDelay;
typedef struct {
    U  I59;
    U  I1176;
    RmaIbfPcode  I1140;
    RmaMps  I1178;
} RmaMPHybridDelay;
typedef struct {
    U  I59;
    U  I1179;
    RmaIbfPcode  I1140;
    RmaEblk  I719;
} RmaMPHybridDelayPacked;
typedef struct {
    U  I59;
    U  I1176;
    RmaIbfPcode  I1140;
    RmaMpspNewCsdf  I1180;
} RmaMPPulseDelay;
typedef struct {
    U  I59;
    U  I1176;
    RmaMpsp  I1180;
    RmaIbfPcode  I1140;
} RmaMPPulseOptHybridDelay;
typedef struct _RmaBehavioralTransportDelay {
    U  I59;
    U  I1176;
    RP  I628;
    RmaTransEventHdr  I993;
    RP  I758;
    RmaIbfPcode  I1140;
} RmaBehavioralTransportDelayS;
typedef struct {
    U  I59;
    U  I1176;
    RP  I628;
    RmaTransEventHdr  I993;
    RP  I758;
    RmaIbfPcode  I1140;
} RmaNtcTransDelay;
typedef struct {
    U  I59;
    U  I1176;
    RP  I628;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaNtcTransMpwOptDelay;
typedef struct {
    U  I59;
    U  I1176;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaNtcTransZeroDelay;
typedef struct {
    U  I59;
    U  I1176;
    RP  I1181;
    RP  I1182;
    RmaTransEventHdr  I993;
    RP  I758;
    RmaIbfPcode  I1140;
} RmaNtcTransDelayRF;
typedef struct {
    U  I59;
    U  I1176;
    RP  I1181;
    RP  I1182;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaNtcTransMpwOptDelayRF;
typedef struct {
    U  I59;
    U  I1176;
    RP  I1183;
    RmaTransEventHdr  I993;
    RP  I758;
    RmaIbfPcode  I1140;
} RmaICTransDelay;
typedef struct {
    U  I59;
    U  I1176;
    RP  I1183;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaICTransMpwOptDelay;
typedef struct {
    U  I59;
    U  I1176;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaICTransZeroDelay;
typedef struct {
    U  I59;
    U  I1176;
    RP  I761;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaICSimpleDelay;
typedef struct {
    U  I59;
    U  I1176;
    union {
        RP  psimple;
        RP  I1760;
        RP  I1777;
    } I734;
    RmaIbfPcode  I1140;
} RmaICDelay;
typedef struct {
    U  I59;
    U  I1176;
    RP  I761;
    RmaEblk  I670;
    RmaIbfPcode  I1140;
} RmaPortDelay;
typedef struct {
    U  I59;
    U  I743;
} RmaDelays;
typedef struct {
    U  I1144;
    RP  I1184;
} RmaRtlXEdgesLoad;
typedef struct {
    U  I59;
    RmaRtlXEdgesLoad   I1184[(5)];
    void * I1185;
} RmaRtlXEdgesHdr;
typedef struct {
    U  I59;
    US  I1186;
    US  I1187 :1;
    US  I845 :15;
    RP  I1188;
    RP  I1189;
    RP  I1190;
} RmaRtlEdgeBlockHdr;
typedef struct {
    RP  I1191;
    RP  I1192;
} RemoteDbsedLoad;
typedef struct {
    RmaEblk  I670;
    RP  I1193;
    RP  I1194;
    US  I1195;
    UB  I1196;
    UB  I1197 :2;
    UB  I1198 :2;
    UB  I1199 :1;
    UB  I845 :3;
    U  I631;
    RP  I1200;
    RP   I765[(5)];
    RP   I767[(5)];
    US  I1201;
    US  I1202;
    RP  I1203;
    RemoteDbsedLoad   I1204[1];
} RmaRtlEdgeBlock;
typedef struct TableAssign_ {
    struct TableAssign_ * I979;
    struct TableAssign_ * I752;
    U  I59;
    U  I1206 :1;
    U  I1207 :1;
    U  I1208 :2;
    U  I1209 :1;
    U  I650 :8;
    U  I1210 :1;
    U  I1211 :1;
    U  I1212 :1;
    U  I1213 :1;
    U  I1214 :1;
    U  I1215 :1;
    U  I1216 :1;
    U  I1217 :1;
    U  I1218 :1;
    U  I1219 :1;
    U  I845 :9;
    RP  ptable;
    RP  I1168;
} TableAssign;
typedef struct TableAssignLayoutOnClk_ {
    struct TableAssignLayoutOnClk_ * I979;
    struct TableAssignLayoutOnClk_ * I752;
    U  I59;
    U  I1206 :1;
    U  I1207 :1;
    U  I1208 :2;
    U  I1209 :1;
    U  I650 :8;
    U  I1210 :1;
    U  I1211 :1;
    U  I1212 :1;
    U  I1213 :1;
    U  I1214 :1;
    U  I1215 :1;
    U  I1216 :1;
    U  I1217 :1;
    U  I1218 :1;
    U  I1219 :1;
    U  I845 :9;
    RP  ptable;
    RmaSeqPrimOutputOnClkS  I1221;
    RmaEblk  I670;
} TableAssignLayoutOnClk;
typedef struct {
    U  state;
    U  I1222;
} RmaSeqPrimOutputOnClkOpt;
typedef struct TableAssignLayoutOnClkOpt_ {
    struct TableAssignLayoutOnClkOpt_ * I979;
    struct TableAssignLayoutOnClkOpt_ * I752;
    U  I739;
    U  I1206 :1;
    U  I1207 :1;
    U  I1208 :2;
    U  I1209 :1;
    U  I650 :8;
    U  I1210 :1;
    U  I1211 :1;
    U  I1212 :1;
    U  I1213 :1;
    U  I1214 :1;
    U  I1215 :1;
    U  I1216 :1;
    U  I1217 :1;
    U  I1218 :1;
    U  I1219 :1;
    U  I845 :9;
    union {
        U  I1225;
        RP  ptable;
    } table;
    RmaSeqPrimOutputOnClkOpt  I1221;
    RmaSeqPrimOutputEblkData  I1226;
} TableAssignLayoutOnClkOpt;
typedef struct {
    U  I59;
    RP  I752;
    RP  I1227;
} RmaTableAssignList;
typedef struct {
    U  I59;
    RP  I752;
    RP  I1227;
    RP  I1228;
    RP  I1149;
    RP  I1229;
    US  I650;
    UB  I1057;
    UB  I1230;
    UB  I1231;
    UB  I725;
    RP   I1232[0];
} RmaThreadTableAssignList;
typedef struct {
    RP  I1228;
    RP  I1149;
    RP  I1229;
    U  I1233 :1;
    U  I1111 :8;
    U  I1234 :8;
    U  I1235 :1;
    U  I1236 :1;
    U  I1237 :2;
    U  I1238 :1;
    U  I845 :10;
    US  I650;
    UB  I1057;
    UB  I1230;
    UB  I1231;
    UB  I725;
} RmaThreadTableHeader;
typedef struct {
    RP  I1191;
} RmaWakeupListCg;
typedef struct {
    RP  I1191;
} RmaWakeupArrayCg;
typedef struct {
    RP  I1191;
    RP  I1239;
} RmaPreCheckWakeupListCg;
typedef struct {
    RP  I1191;
    RP  I1239;
} RmaPreCheckWakeupArrayCg;
typedef struct {
    U  I1240;
    U  I650;
    RmaTimeStamp   I1241[1];
} RmaTsArray;
typedef struct {
    U  iinput;
    RP  I1242;
} RmaConditionsMdb;
typedef struct {
    RP  I1243;
    RP  I1244;
    U  I1245;
} RmaTcListHeader;
typedef struct {
    RP  I979;
    RP  I1246;
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
} RmaTcCoreSimple;
typedef struct {
    RP  I979;
    RP  I1246;
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1258;
} RmaTcCoreConditional;
typedef struct {
    RP  I979;
    RP  I1246;
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1258;
    RP  I1259;
} RmaTcCoreConditionalOpt;
typedef struct {
    RP  I979;
    RP  I1246;
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1259;
    RP  I1260;
    U  I1261;
    RmaConditionsMdb   arr[1];
} RmaTcCoreConditionalMtc;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
} RmaTcCoreSimpleNoList;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1258;
} RmaTcCoreConditionalNoList;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1258;
    RP  I1259;
} RmaTcCoreConditionalOptNoList;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1259;
    RP  I1260;
    U  I1261;
    RmaConditionsMdb   arr[1];
} RmaTcCoreConditionalMtcNoList;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1259;
    RP  I1260;
    RP  I1154;
    U  I1261;
    RmaConditionsMdb   arr[1];
} RmaTcCoreConditionalMtcNoListMdb;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1258;
    RP  I1154;
} RmaTcCoreConditionalNoListMdb;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    U  I1263;
    RP  I1264;
    RP  I1265;
    RP  I1258;
    RP  I1266;
    RP  I1267;
    RmaTimeStamp  I1268;
} RmaTcCoreNochange;
typedef struct {
    RP  I1269;
    RP  I979;
} RmaTcCoreNochangeList;
typedef struct {
    RP  I1247;
    RP  I665;
    U  I1248;
    scalar  I1144;
    scalar  I1249;
    US  I1250 :1;
    US  I1251 :1;
    US  I1252 :1;
    US  I1253 :1;
    US  I1254 :1;
    US  I1255 :1;
    US  I1256 :1;
    US  I1257 :5;
    RP  I1258;
    RmaEblk  I670;
    U  I1270;
} RmaTcCoreFullskew;
typedef struct {
    RP  I1271;
    RP  I979;
} RmaTcCoreFullskewList;
typedef struct {
    RP  I1242;
    RmaTimeStamp  I1272;
    scalar  I1273;
    scalar  I1274 :1;
    scalar  I739 :7;
} RmaConditionalTSLoadNoList;
typedef struct {
    RP  I979;
    RP  I1242;
    RmaTimeStamp  I1272;
    scalar  I1273;
    scalar  I1274 :1;
    scalar  I739 :7;
} RmaConditionalTSLoad;
typedef struct {
    RmaTimeStamp  I1272;
    scalar  I1273;
    US  I1144;
    RP  I1259;
} RmaConditionalTSLoadOptNoList;
typedef struct {
    RP  I979;
    RmaTimeStamp  I1272;
    scalar  I1273;
    US  I1144;
    RP  I1259;
} RmaConditionalTSLoadOpt;
typedef struct {
    RP  I1259;
    RP  I1275;
    U  I1261;
    RmaConditionsMdb   arr[1];
} RmaConditionalTSLoadMtcNoList;
typedef struct {
    RP  I1154;
    RP  I1259;
    RP  I1275;
    U  I1261;
    RmaConditionsMdb   arr[1];
} RmaConditionalTSLoadMtcNoListMdb;
typedef struct {
    RP  I979;
    RP  I1259;
    RP  I1275;
    U  I1261;
    RmaConditionsMdb   arr[1];
} RmaConditionalTSLoadMtc;
typedef struct {
    U  I1276;
    U  I1277;
    FlatNodeNum  I1099;
    U  I987;
    U  I1278;
    U  I1279;
    RmaIbfPcode  I1140;
    union {
        scalar  I1280;
        vec32  I1281;
        scalar  * I1282;
        vec32  * I1283;
    } val;
} RmaScanSwitchData;
typedef struct {
    U  I1276;
    U  I1277;
    U  I987;
    U  I1284;
    FlatNodeNum  I1099;
    RmaIbfPcode  I1140;
    union {
        scalar  I1280;
        vec32  I1281;
        scalar  * I1282;
        vec32  * I1283;
        double I1285;
        scalar  * I1286;
    } val;
} RmaRtvsGateData;
typedef struct {
    RP  I979;
    RP  I752;
    RP  I1287;
} RmaDoublyLinkedListElem;
typedef struct {
    RP  I1288;
    RP  I1289;
    U  I1290 :1;
    U  finterpreted :1;
    U  I1292 :1;
    U  I1293 :4;
    U  I1294 :1;
    U  I1081 :1;
    U  I1295 :1;
    U  I1296;
} RmaSwitchGateInCbkListInfo;
typedef struct {
    union {
        RmaDoublyLinkedListElem  I1826;
        RmaSwitchGateInCbkListInfo  I2;
    } I643;
    RmaIbfPcode  I1140;
} RmaSwitchGate;
typedef struct RmaNonEdgeLoadData1_ {
    US  I1297;
    scalar  val;
    scalar  I1298 :1;
    scalar  I1299 :1;
    scalar  I1300 :3;
    scalar  I1301 :1;
    scalar  I1302 :1;
    scalar  I1303 :1;
    U  I1304;
    U  I1305;
    RP  I765;
    RP  I1306;
    RP  I1099;
    RP  I1307;
    RP  I1308;
} RmaNonEdgeLoadData1;
typedef struct RmaNonEdgeLoadHdr1_ {
    UB  I1298;
    UB  I1309;
    US  I1057;
    RmaNonEdgeLoadData1  * I1184;
    RmaNonEdgeLoadData1  * I752;
    RP  I1203;
    void * I1185;
} RmaNonEdgeLoadHdr1;
typedef struct RmaNonEdgeLoadHdrPrl1_ {
    U  I1310;
    RP  I665;
} RmaNonEdgeLoadHdrPrl1;
typedef struct RmaNonEdgeLoadArrayHdr1_ {
    UB  I1298 :1;
    UB  I1309 :1;
    UB  I1057;
    int I631;
    RmaNonEdgeLoadData1  * I635;
    U  * I1311;
    RP  I1203;
    void * I1185;
} RmaNonEdgeLoadArrayHdr1;
typedef struct RmaNonEdgeLoadDataArray1_ {
    U  I1312;
} RmaNonEdgeLoadDataArray1;
typedef struct RmaChildClockProp_ {
    RP  I765;
    RP  I1313;
    RP  I1099;
    RP  pcode;
    scalar  val;
} RmaChildClockProp;
typedef struct RmaChildClockPropList1_ {
    RmaChildClockProp  * I1184;
    RmaChildClockProp  * I752;
} RmaChildClockPropList1;
typedef struct {
    UB  I1314;
    UB  I1315 :1;
    UB  I1316 :1;
    UB  I1317 :1;
    UB  I1318 :1;
    UB  I845 :4;
    US  cedges;
    RP  I1319;
} RmaMasterXpropLoadHdr;
typedef struct {
    UB  I1314;
    UB  I1320;
    UB  I1321;
    UB  I1322 :1;
    UB  I1323 :1;
    UB  I845 :6;
    U  cedges;
} RmaXpropLoadHdr;
typedef struct {
    UB  I1320;
    UB  I1321;
    UB  I1324;
    UB  I1325;
    U  cedges :29;
    U  I1316 :1;
    U  I1326 :1;
    U  I1327 :1;
    U  I1328;
    U  I1329;
    RP  I1330;
    RP  I1331;
    RmaRtlEdgeBlockHdr  * I1332;
} RmaChildXpropLoadHdr;
struct clock_load {
    U  I234 :5;
    U  I235 :12;
    U  I236 :1;
    U  I237 :2;
    U  I238 :1;
    U  I239 :1;
    U  I240 :1;
    U  I241 :9;
    U  I242;
    U  I243;
    void (* pfn)(void * I245, char val);
};
typedef struct clock_data {
    U  I250 :1;
    U  I251 :1;
    U  I252 :1;
    U  I253 :1;
    U  I234 :5;
    U  I235 :12;
    U  I254 :6;
    U  I255 :1;
    U  I237 :2;
    U  I238 :1;
    U  I241 :1;
    U  I256;
    U  I257;
    U  I258;
    U  I242;
    U  I259;
    U  I260;
    U  I261;
    U  I262;
    U  I263;
} HdbsClockData;
struct clock_hiconn {
    U  I267;
    U  I268;
    U  I242;
    U  I237;
};
typedef union _RmaCbkMemOptUnion {
    RP  I1333;
    RP  I1334;
    RP  I1335;
} RmaCbkMemOptUnion;
typedef struct _RmaDaiOptCg {
    RmaCbkMemOptUnion  I1336;
} RmaDaiOptCg;
struct futq_slot2 {
    U  I710;
    U   I711[32];
};
struct futq_slot1 {
    U  I707;
    struct futq_slot2  I708[32];
};
struct futq_info {
    scalar  * I702;
    U  I703;
    U  I704;
    struct futq_slot1  I705[32];
};
struct futq {
    struct futq * I691;
    struct futq * I693;
    RmaEblk  * I694;
    RmaEblk  * I695;
    U  I675;
    U  I1;
    DEblkPoolT  * I696;
};
struct sched_table {
    struct futq * I697;
    struct futq I698;
    struct hash_bucket * I699;
    struct hash_bucket * I701;
};
struct dummyq_struct {
    clock_struct  I1337;
    EBLK  * I1338;
    EBLK  * I1339;
    EBLK  * I1340;
    struct futq * I1341;
    struct futq * I1342;
    struct futq * I1343;
    struct sched_table * I1344;
    struct futq_info * I1346;
    struct futq_info * I1348;
    U  I1349;
    U  I1350;
    U  I1351;
    U  I1352;
    U  I1353;
    U  I1354;
    U  I1355;
    struct millenium * I1356;
    EBLK  * I1358;
    EBLK  * I1359;
    EBLK  * I1360;
    EBLK  * I1361;
    EBLK  * I1362;
    EBLK  * I1363;
    EBLK  * I1364;
    EBLK  * I1365;
    EBLK  * I1366;
    EBLK  * I1367;
    EBLK  * I1368;
    EBLK  * I1369;
    EBLK  * I1370;
    EBLK  * I1371;
    EBLK  * I1372;
    EBLK  * I1373;
    EBLK  * I1374;
    MPS  * I1375;
    struct retain_t * I1376;
    EBLK  * I1377;
    EBLK  * I1378;
    EBLK  * I1379;
    EBLK  * I1380;
    EBLK  * I1381;
    EBLK  * I1382;
    EBLK  * I1383;
    EBLK  * I1384;
    EBLK  * I1385;
    EBLK  * I1386;
    EBLK  * I1387;
    EBLK  * I1388;
    EBLK  * I1389;
    EBLK  * I1390;
    EBLK  * I1391;
    EBLK  * I1392;
    EBLK  * I1393;
    EBLK  * I1394;
    EBLK  * I1395;
    EBLK  * I1396;
    EBLK  * I1397;
    EBLK  * I1398;
    EBLK  * I1399;
    EBLK  * I1400;
    EBLK  * I1401;
    EBLK  * I1402;
    EBLK  I1403;
    EBLK  * I1404;
    EBLK  * I1405;
    int I1406;
    struct vcs_globals_t * I1407;
    clock_struct  I1409;
    unsigned long long I1410;
    EBLK  * I1411;
    EBLK  * I1412;
    void * I1413;
    U  semilerOptQueuesFlag;
    U  I1415;
};
typedef void (* FP)(void *  , scalar   );
typedef void (* FP1)(void *  );
typedef void (* FPRAP)(void *  , vec32  *  , U   );
typedef U  (* FPU1)(void *  );
typedef void (* FPV)(void *  , UB  *  );
typedef void (* FPVU)(void *  , UB  *  , U   );
typedef void (* FPDP)(void *  , void *  , scalar   );
typedef void (* FPVDP)(void *  , void *  , void *  );
typedef void (* FPLSEL)(void *  , scalar   , U   );
typedef void (* FPLSELV)(void *  , vec32  *  , U   , U   );
typedef void (* FPFPV)(UB  *  , UB  *  , U   , U   , U   , U   , U   , UB  *  , U   );
typedef void (* FPFA)(UB  *  , UB  *  , U   , U   , U   , U   , U   , U   , UB  *  , U   );
typedef void (* FPRPV)(UB  *  , U   , U   , U   );
typedef void (* FPEVCDLSEL)(void *  , scalar   , U   , UB  *  );
typedef void (* FPEVCDLSELV)(void *  , vec32  *  , U   , U   , UB  *  );
typedef void (* FPNTYPE_L)(void *  , void *  , U   , U   , UB  *  , UB  *  , UB  *  , UB  *  , UB  *  , UB  *  , UB  *  , U   );
typedef void (* FPNTYPE_H)(void *  , void *  , U   , U   , UB  *  , UB  *  , UB  *  , UB  *  , U   );
typedef void (* FPNTYPE_LPAP)(void *  , void *  , void *  , U   , U   , UB  *  , UB  *  , U   );
typedef void (* FPNTYPE_HPAP)(void *  , void *  , void *  , U   , U   , UB  *  , UB  *  , UB  *  , UB  *  , U   );
typedef struct _lqueue {
    int I1416;
    struct _lqueue * I722;
    EBLK  * I1418;
    EBLK  * I674;
    DEblkPoolT  * I696;
} Queue;
typedef struct {
    void * I1419;
    void * I1420;
    void * I1421;
    void * I1422;
    void * I1423[2];
    void * I1424;
} ClkLevel;
typedef struct {
    unsigned long long I1425;
    EBLK  I1326;
    U  I1426;
    U  I1427;
    union {
        void * pHeap;
        Queue  * pList;
    } I643;
    unsigned long long I1428;
    ClkLevel  I1429;
    EBLK  * I1430;
    Queue   I1431[1];
} Qhdr;
extern UB   Xvalchg[];
extern UB   X4val[];
extern UB   X3val[];
extern UB   X2val[];
extern UB   XcvtstrTR[];
extern UB   Xcvtstr[];
extern UB   Xbuf[];
extern UB   Xbitnot[];
extern UB   Xwor[];
extern UB   Xwand[];
extern U   Xbitnot4val[];
extern UB   globalTable1Input[];
extern __thread unsigned long long vcs_clocks;
extern __thread unsigned long long ref_vcs_clocks;
extern UB   Xunion[];
extern UB   vcsXunionCg[];
extern UB   vcsXworCg[];
extern UB   vcsXwandCg[];
extern U  fRTFrcRelCbk;
extern FP  txpFnPtr;
extern UP   bepatsFunctionArray[];
extern FP   rmaFunctionArray[];
extern UP   rmaFunctionRtlArray[];
extern UP   rmaFunctionHsRtlArray[];
extern FP   rmaFunctionLRArray[];
extern U  rmaFunctionCount;
extern U  rmaFunctionLRCount;
extern U  rmaFunctionLRDummyCount;
extern UP  rmaFunctionDummyEndPtr;
extern FP   rmaFunctionFanoutArray[];
extern __thread UB  dummyScalar;
extern __thread UB  fScalarIsForced;
extern __thread UB  fScalarIsReleased;
extern __thread UB  fScalarIsDeposited;
extern U  fNotimingchecks;
extern U  fFsdbDumpOn;
extern RP  * iparr;
extern FP1  * rmaPostAnySchedFnPtr;
extern FP1  * rmaPostAnySchedFnSamplePtr;
extern FP1  * rmaPostAnySchedVFnPtr;
extern FP1  * rmaPostAnySchedWFnPtr;
extern FP1  * rmaPostAnySchedEFnPtr;
extern FP1  * rmaPostSchedUpdateClockStatusFnPtr;
extern FP1  * rmaPostSchedUpdateClockStatusNonCongruentFnPtr;
extern FP1  * rmaPostSchedUpdateEvTrigFnPtr;
extern FP1  * rmaSched0UpdateEvTrigFnPtr;
extern FP1  * rmaPostSchedRecoveryResetDbsFnPtr;
extern U  fGblDataOrTime0Prop;
extern UB   rmaEdgeStatusValArr[];
extern UB   rmaposEdgeStatusArray[];
extern UB   rmanegEdgeStatusArray[];
extern FP1  * propForceCbkSPostSchedCgFnPtr;
extern FP1  * propForceSPostSchedCgFnPtr;
extern FP1  * propForceSPostSchedNativeVcCgFnPtr;
extern U  VCS_NCBK_Enabled;
extern UB  * ptableGbl;
extern U  * vcs_ptableOffsetsGbl;
extern UB  * expandedClkValues;
extern __thread Qhdr  * lvlQueue;
extern __thread unsigned threadIndex;
extern __thread unsigned fBufferingEvent;
extern int cPeblkThreads;
extern US   xedges[];
extern U  mhdl_delta_count;
extern U  ignoreSchedForScanOpt;
extern U  fignoreSchedForDeadComboCloud;
extern int fZeroUser;
extern U  fEveBusPullVal;
extern U  fEveBusPullFlag;
extern U  fFutEventPRL;
extern U  fParallelEBLK;
extern __thread UB  fNettypeIsForced;
extern __thread UB  fNettypeIsReleased;
extern EBLK  * peblkFutQ1Head;
extern EBLK  * peblkFutQ1Tail;
extern US  * edgeActionT;
extern unsigned long long * derivedClk;
extern U  fHashTableSize;
extern U  fSkipStrChangeOnDelay;
extern U  fHsimRuntimeSdfOpt;
extern U  fSkipMpTsUpdateOnStr;
extern scalar   edgeChangeLookUp[4][4];
extern U  fDoingTime0Prop;
extern U  fLoopDetectMode;
extern int gFLoopDectCodeEna;
extern U  fLoopReportRT;
extern U  fIsCalledFromWaveform;
extern U  rmaProfEvtProp;
extern U  semilerOptQueuesFlag;
extern int semilerOpt;


extern void *mempcpy(void* s1, void* s2, unsigned n);
extern RP* rmaGetDynamicVcdLocation(U archiveId, U patIdx, UB* pupdate);
extern UB* rmaEvalDelays(UB* pcode, scalar val, scalar *preVal, void* pwaveform, U* wf_cloads, U isPortOrICDelayEvalSkipReqd);
extern UB* rmaEvalDelaysV(UB* pcode, vec32* pval);
extern void rmaPopTransEvent(UB* pcode);
extern void rmaSetupFuncArray(UP* ra, U c, U w);
extern void rmaSetupRTLoopReportPtrs(UP* funcs, UP* rtlFuncs, U cnt, U cntDummy, UP end);
extern void SinitHsimPats(void);
extern void VVrpDaicb(void* ip, U nIndex);
extern int SDaicb(void *ip, U nIndex);
extern void SDaicbForHsimNoFlagScalar(void* pDaiCb, unsigned char value, unsigned char isStrength);
extern void SDaicbForHsimWithFlagScalar(void* pDaiCb, unsigned char value, unsigned char isStrength);
extern void SDaicbForHsimNoFlagDynElabScalar(U* mem, unsigned char value, unsigned char isStrength);
extern void SDaicbForHsimWithFlagDynElabScalar(U* mem, unsigned char value, unsigned char isStrength);
extern void SDaicbForHsimNoFlagDynElabFrcRel(U* mem, unsigned char reason, int msb, int lsb, int ndx);
extern void SDaicbForHsimNoFlagFrcRel(void* pDaiCb, unsigned char reason, int msb, int lsb, int ndx);
extern void hsimDispatchForVcd(RP p, U val);
extern void* hsimGetCbkMemOptCallback(RP p);
extern void hsimDispatchNoDynElabS(RP* p, U val, U isStrength);
extern void hsimDispatchDANoDynElabS(RP* p, U val, UB* da_byte, U isStrength);
extern U slaveTogglesThisTime;
extern void* hsimGetCbkPtrNoDynElab(RP p);
extern void hsimDispatchDynElabS(U** pvcdarr, U** pcbkarr, U val, U isScalForced, U isScalReleased, U isStrength);
extern void hsimDispatchNoDynElabVector(RP* /*RmaDaiOptCg* */p, void* pval, U /*RmaValueType*/ vt, U cbits);
extern void copyAndPropRootCbkCgS(RmaRootCbkCg* pRootCbk, scalar val);
extern void copyAndPropRootCbkCgV(RmaRootCbkCg* rootCbk, vec32* pval);
extern void copyAndPropRootCbkCgW(RmaRootCbkCg* rootCbk, vec32* pval);
extern void copyAndPropRootCbkCgE(RmaRootCbkCg* rootCbk, scalar* pval);
extern void Wsvvar_callback_non_dynamic1(RP* ptr, int);
extern void rmaExecEvSyncList(RP plist);
extern void Wsvvar_callback_virt_intf(RP* ptr);
extern void Wsvvar_callback_hsim_var(RP* ptr);
extern void checkAndConvertVec32To2State(vec32* value, vec32* svalue, U cbits, U* pforcedBits);
extern unsigned int fGblDataOrTime0Prop;
extern void SchedSemiLerMP1(UB* pmps, U partId);
extern void SchedSemiLerMPO(UB* pmpso, U partId);
extern void rmaDummyPropagate(void);
extern RP rmaTestCg(RP pcode, U vt, UB* value);
extern void hsUpdateModpathTimeStamp(UB* pmps);
extern void doMpd32One(UB* pmps);
extern void doMpdCommon(MPS* pmps);
extern TimeStamp GET_DIFF_DELAY_FUNC(TimeStamp ts);
extern void SchedSemiLerMP(UB* ppulse, U partId);
extern void SchedSemiLerMP_front(UB* ppulse, U partId);
extern EBLK *peblkFutQ1Head;
extern EBLK *peblkFutQ1Tail;
extern void scheduleuna(UB *e, U t);
extern void scheduleuna_mp(EBLK *e, unsigned t);
extern void schedule(UB *e, U t);
extern void sched_hsopt(struct dummyq_struct * pQ, EBLK *e, U t);
extern void sched_millenium(struct dummyq_struct * pQ, void *e, U thigh, U t);
extern void schedule_1(EBLK *e);
extern void sched0(UB *e);
extern void sched0lq(EBLK *e);
extern void sched0lqnc(EBLK *e);
extern void sched0una(UB *e);
extern void sched0una_th(struct dummyq_struct *pq, UB *e);
extern void hsopt_sched0u_th(struct dummyq_struct *pq, UB *e);
extern void scheduleuna_mp_th(struct dummyq_struct *pq, EBLK *e, unsigned t);
extern void scheduleuna_mp_front_th(struct dummyq_struct *pq, EBLK *e, unsigned t);
extern void schedal(UB *e);
extern void sched0_th(struct dummyq_struct * pQ, EBLK *e);
extern void sched0u(UB *e);
extern void sched0u_th(struct dummyq_struct *pq, UB *e);
extern void sched0_hsim_front_th(struct dummyq_struct * pQ, UB *e);
extern void sched0_hsim_frontlq_th(struct dummyq_struct * pQ, UB *e);
extern void sched0lq_th(struct dummyq_struct * pQ, UB *e);
extern void schedal_th(struct dummyq_struct * pQ, UB *e);
extern void scheduleuna_th(struct dummyq_struct * pQ, void *e, U t);
extern void sched64cuna_th(struct dummyq_struct * pQ, void *e, U h, U l);
extern void schedule_th(struct dummyq_struct * pQ, UB *e, U t);
extern void schedule_1_th(struct dummyq_struct * pQ, EBLK *peblk);
extern void schedule_1_front_th(struct dummyq_struct * pQ, EBLK *peblk);
extern void SetupLER_th(struct dummyq_struct * pQ, EBLK *e);
extern void FsdbReportClkGlitch(UB*,U);
extern void AddToClkGLitchArray(EBLK*);
extern void SchedSemiLer_th(struct dummyq_struct * pQ, EBLK *e);
extern void SchedSemiLerTXP_th(struct dummyq_struct * pQ, EBLK *e);
extern void SchedSemiLerTXPFreeVar_th(struct dummyq_struct * pQ, EBLK *e);
extern U getVcdFlags(UB *ip);
extern void VVrpNonEventNonRegdScalarForHsimOpt(void* ip, U nIndex);
extern void VVrpNonEventNonRegdScalarForHsimOpt2(void* ip, U nIndex);
extern void SchedSemiLerTBReactiveRegion(struct eblk* peblk);
extern void SchedSemiLerTBReactiveRegion_th(struct eblk* peblk, U partId);
extern void SchedSemiLerTr(UB* peblk, U partId);
extern void SchedSemiLerNBA(UB* peblk, U partId);
extern void NBA_Semiler(void *ip, void *pNBS);
extern void sched0sd_hsim(UB* peblk);
extern void vcs_sched0sd_hsim_udpclk(UB* peblk);
extern void vcs_sched0sd_hsim_udpclkopt(UB* peblk);
extern void sched0sd_hsim_PRL(UB* peblk);
extern void sched0lq_parallel_clk(EBLK* peblk);
extern void sched0lq_de(U* pe, void* ip, void* prout);
extern void sched0lq_hsopt_nonfgp_de(U* pe, void* ip, void* prout);
extern void sched0lq_hsopt_parallel_de(U* pe, void* ip, void* prout);
extern void sched0_de_inl(void* ip, void* prout);
extern void rmaPostDynamicSchedDeltaGate(UB* pcode);
extern U    isRtlClockScheduled(EBLK* peblk);
extern void doFgpRaceCheck(UB* pcode, UB* p, U flag);
extern void doSanityLvlCheck();
extern void sched0lq_dbsed_ova_jaguar(EBLK* peblk);
extern void sched0lq_nondbsed_ova_jaguar(EBLK* peblk);
extern void sched0lq_parallel_ova(EBLK* peblk);
extern void sched0lq_parallel_ova_precheck(EBLK* peblk);
extern void rmaDlpEvalSeqPrim(UB* peblk, UB val, UB preval);
extern void appendNtcEvent(UB* phdr, scalar s, U schedDelta);
extern void appendTransEventS(RmaTransEventHdr* phdr, scalar s, U schedDelta);
extern void schedRetainHsim(MPS* pMPS, scalar sv, scalar pv);
extern void updateRetainHsim(MPS* pMPS,scalar sv, scalar pv);
extern void hsimCountXEdges(void* record,  scalar s);
extern void hsimRegisterEdge(void* sm,  scalar s);
extern EBLK* pvcsGetLastEventEblk(U thid);
extern void insertTransEventIC(RmaTransEventHdr* phdr, scalar s, scalar pv, scalar resval, U schedDelta, int re, UB* predd, U fpdd);
extern void insertNtcEventRF(RmaTransEventHdr* phdr, scalar s, scalar pv, scalar resval, U schedDelta, U* delays);
extern U doTimingViolation(RmaTimeStamp ts,RP* pdata, U fskew, U limit, U floaded, U fcondopt, RmaTimeStamp tsNochange);
extern void sched_gate_hsim(EBLK* peblk, unsigned t, RP* offset, U gd_info, U encodeInPcode, void* propValue);
extern void sched_gate_hsim_front(EBLK* peblk, unsigned t, RP* offset, U gd_info, U encodeInPcode, void* propValue);
extern int getCurSchedRegion();
extern FP getRoutPtr(RP, U);
extern U rmaChangeCheckAndUpdateE(scalar* pvalDst, scalar* pvalSrc, U cbits);
extern void rmaUpdateE(scalar* pvalDst, scalar* pvalSrc, U cbits);
extern U rmaChangeCheckAndUpdateEFromW(scalar* pvalDst, vec32* pvalSrc, U cbits);
extern void rmaLhsPartSelUpdateE(scalar* pvalDst, scalar* pvalSrc, U index, U width);
extern void rmaUpdateWithForceSelectorE(scalar* pvalDst, scalar* pvalSrc, U cbits, U* pforceSelector);
extern void rmaUpdateWFromE(vec32* pvalDst, scalar* pvalSrc, U cbits);
extern void rmaUpdateWUFromE(U* pv, scalar* pval, U cbits);
extern U rmaLhsPartSelWithChangeCheckE(scalar* pvalDst, scalar* pvalSrc, U index, U width);
extern void rmaLhsPartSelWFromE(vec32* pvalDst, scalar* pvalSrc, U index,U width);
extern U rmaChangeCheckAndUpdateW(vec32* pvalDst, vec32* pvalSrc, U cbits);
extern void rmaUpdateW(vec32* pvalDst, vec32* pvalSrc, U cbits);
extern void rmaUpdateEFromW(scalar* pvalDst, vec32* pvalSrc, U cbits);
extern void *VCSCalloc(size_t size, size_t count);
extern void *VCSMalloc(size_t size);
extern void VCSFree(void *ptr);
extern U rmaLhsPartSelWithChangeCheckW(vec32* pvalDst, vec32* pvalSrc, U index,U width);
extern void rmaLhsPartSelEFromW(scalar* pvalDst, vec32* pvalSrc, U index,U width);
extern U rmaLhsPartSelWithChangeCheckEFromW(scalar* pvalDst, vec32* pvalSrc, U index,U width);
extern void rmaLhsPartSelUpdateW(vec32* pvalDst, vec32* pvalSrc, U index, U width);
extern void rmaEvalWunionW(vec32* dst, vec32* src, U cbits, U count);
extern void rmaEvalWorW(vec32* dst, vec32* src, U cbits, U count);
extern void rmaEvalWandW(vec32* dst, vec32* src, U cbits, U count);
extern void rmaEvalUnionE(scalar* dst, scalar* src, U cbits, U count, RP ptable);
typedef U RmaCgFunctionType;
extern RmaIbfPcode* rmaEvalPartSelectsW(vec32* pvec32, U startIndex, U onWidth, U offWidth, U count, RmaIbfPcode* pibfPcode, U fnonRootForce, UB* pevcdStatus);
extern RmaIbfPcode* rmaEvalPartSelectsWLe32(vec32* pvec32, U startIndex, U onWidth, U offWidth, U count, RmaIbfPcode* pibfPcode, U fnonRootForce, UB* pevcdStatus);
extern RmaIbfPcode* rmaEvalPartSelectsWToE(vec32* pvec32, U startIndex, U onWidth, U offWidth, U count, RmaIbfPcode* pibfPcode, U fnonRootForce);
extern RmaIbfPcode* rmaEvalPartSelectsEToE(scalar* pv, U startIndex, U onWidth, U offWidth, U count, RmaIbfPcode* pibfPcode, U fnonRootForce, UB* pevcdStatus);
extern RmaIbfPcode* rmaEvalPartSelectsEToW(scalar* pv, U startIndex, U onWidth, U offWidth, U count, RmaIbfPcode* pibfPcode, U fnonRootForce);
extern U rmaEvalBitPosEdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitNegEdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitChangeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U VcsForceVecVCg(UB* pcode, UB* pval, UB* pvDst, UB* pvCur, U fullcbits, U ibeginSrc, U ibeginDst, U width, U/*RmaValueConvType*/ convtype, U/*RmaForceType*/ frcType, UB* prhs, UB* prhsDst, U frhs, U* pforcedbits, U fisRoot);
extern U VcsReleaseVecVCg(UB* pcode, UB* pvDst, U fullcbits, U ibeginDst, U width, UB* prhsDst, U frhs, U* pforcedbits, U fisRoot);
extern U VcsForceVecWCg(UB* pcode, UB* pval, UB* pvDst, UB* pvCur, U fullcbits, U ibeginSrc, U ibeginDst, U width, U/*RmaValueConvType*/ convtype, U /*RmaForceType*/ frcType, UB* prhs, UB* prhsDst, U frhs, U* pforcedbits, U fisRoot);
extern U VcsReleaseVecWCg(UB* pcode, UB* pvDst, U fullcbits, U ibeginDst, U width, UB* prhsDst, U frhs, U* pforcedbits, U fisRoot);
extern U VcsForceVecECg(UB* pcode, UB* pval, UB* pvDst, UB* pvCur, U fullcbits, U ibeginSrc, U ibeginDst, U width, U /*RmaValueConvType*/ convtype, U /*RmaForceType*/ frcType,UB* prhs, UB* prhsDst, U frhs, U* pforcedbits, U fisRoot);
extern U VcsForceVecACg(UB* pcode, UB* pval, UB* pvDst, UB* pvCur, U fullcbits, U ibeginSrc, U ibeginDst, U width, U /*RmaValueConvType*/ convtype, U /*RmaForceType*/ frcType,UB* prhs, UB* prhsDst, U frhs, U* pforcedbits, U fisRoot);
extern U VcsReleaseVecCg(UB* pcode, UB* pvDst, U ibeginDst, U width, U /*RmaValueType*/ type,U fisRoot, UB* prhsDst, U frhs, U* pforcedbits);
extern U VcsDriveBitsAndDoChangeCheckV(vec32* pvSel, vec32* pvCur, U fullcbits, U* pforcedbits, U isRoot);
extern U VcsDriveBitsAndDoChangeCheckW(vec32* pvSel, vec32* pvCur, U fullcbits, U* pforcedbits, U isRoot);
extern U VcsDriveBitsAndDoChangeCheckE(scalar* pvSel, scalar* pvCur, U fullcbits, U* pforcedbits, U isRoot);
extern void cgvecDebug_Eblk(UB* pcode);
extern U rmaCmpW(vec32* pvalDst, vec32* pvalSrc, U index, U width);
extern void copyVec32ArrMask(vec32* pv1, vec32* pv2, U len, U* mask);
extern void* memcpy(void*, const void*, size_t);
extern int memcmp(const void*, const void*, size_t);
extern void propagateScanOptPathVal(EBLK *peblk);
extern UB* rmaProcessRtvsSwitches(UB* pcode, scalar val);
extern UB* rmaProcessScanSwitches(UB* pcode, scalar val);
extern UB* rmaProcessScanSwitchesV(UB* pcode, vec32 *pval);
extern UB* rmaProcessScanoptDump(UB* pcode, scalar val);
extern UB* rmaProcessScanoptDumpV(UB* pcode, vec32 *pval);
extern UB* rmaProcessScanChainOptSeqPrims(UB* pcode, scalar val);
extern void schedResetRecoveryDbs(U cedges, EBLK* peblkFirst);
extern UB* rmaEvalUnaryOpV(UB* pcode, vec32* pval);
extern UB* rmaEvalBinaryOpV(UB* pcode, vec32* pval);
extern UB* rmaEvalBinaryOpVOneFanoutCount(UB* pcode, vec32* pval);
extern UB* rmaEvalBinaryOpVLargeFanoutCount(UB* pcode, vec32* pval);
extern UB* rmaEvalAndOpVOneFanoutCount(UB* pcode, vec32* value);
extern UB* rmaEvalAndOpVLargeFanoutCount(UB* pcode, vec32* value);
extern UB* rmaEvalAndOpV(UB* pcode, vec32* value);
extern UB* rmaEvalOrOpVOneFanoutCount(UB* pcode, vec32* value);
extern UB* rmaEvalOrOpVLargeFanoutCount(UB* pcode, vec32* value);
extern UB* rmaEvalOrOpV(UB* pcode, vec32* value);
extern UB* rmaEvalTernaryOpV(UB* pcode, vec32* pval);
extern UB* rmaEvalUnaryOpW(UB* pcode, vec32* pval);
extern UB* rmaEvalBinaryOpW(UB* pcode, vec32* pval);
extern UB* rmaEvalTernaryOpW(UB* pcode, vec32* pval);
extern UB* rmaEvalUnaryOpE(UB* pcode, scalar* pv);
extern UB* rmaEvalBinaryOpE(UB* pcode, scalar* pv);
extern UB* rmaEvalTernaryOpE(UB* pcode, scalar* pv);
extern UB* rmaEvalTernaryOpS(UB* pcode, scalar val);
extern scalar rmaGetScalarFromWCg(vec32* pval, U index);
extern void rmaSetScalarInWCg(vec32* pval, U index, scalar s);
extern void rmaSetWInW(vec32* dst, vec32* src, U index, U indexSrc, U width);
extern void setHsimFunc(void* ip, U offset);
extern void vcs_updateHsimFunc(void* ip, U offset);
extern void unsetHsimFunc(void* ip);
extern U fProfEvtProp;
extern UB* getEvcdStatusByFlagsE(scalar* pscalar, UB* pevcdTBDriverFlags, U cdrivers, UB* table, U cbits);
extern UB* getEvcdStatusByFlagsV(vec32* pvec32, UB* pevcdTBDriverFlags, U cdrivers, UB* table, U cbits);
extern UB* getEvcdStatusByFlagsW(vec32* pvec32, UB* pevcdTBDriverFlags, U cdrivers, UB* table, U cbits);
extern UB* getEvcdStatusByFlagsS(scalar* pscalar, UB* pevcdTBDriverFlags, U cdrivers, UB* table);
extern UB* getSingleDrvEvcdStatusS(UB value, U fTBDriver);
extern UB* getSingleDrvEvcdStatusE(scalar* pscalars, U fTBDriver, U cbits);
extern UB* getSingleDrvEvcdStatusV(scalar* pscalars, U fTBDriver, U cbits);
extern UB* getSingleDrvEvcdStatusW(scalar* pscalars, U fTBDriver, U cbits);
extern UB* getEvcdStatusByDrvEvcdStatus(UB* pdrvevcdStatus, U cdrivers, UB* table, U cbits);
extern void evcdCallback(UP pcode, U cbits);
extern UB* getSavedEvcdStatus(void);
extern void saveEvcdStatus(UB*);
extern void mhdlMarkExport(void*, U);
extern void levelInsertQueue(int);
extern void levelInsertQueueGL(int);
extern void VcsRciRtl(RP pcode);
extern void VcsRciSCompiledLoad(scalar* pcode, scalar ibnVal);
extern U fLoopDetectMode;
extern int gFLoopDectCodeEna;
extern U fLoopReportRT;
extern void rtSched0LoopDectDumpProcess(void* e, void* rtn, void* PQ);
extern void pushHsimRtnCtxt(void* pcode);
extern void popHsimRtnCtxt();
extern EBLK* loopReportInlinedSched0Wrapper(EBLK *peblk);
extern void loopReportSched0Wrapper(EBLK *peblk, unsigned int sfType, unsigned int fTH, struct dummyq_struct* pq);
extern void loopReportSchedSemiLerWrapper(EBLK *peblk, int sfType);
extern void CallGraphPushNodeAndAddToGraph(UP flatNode, UP instNum, U dummy);
extern void CallGraphPopNode(void);
extern RP elabGetIpTpl(U in);
extern U rmaEvalBitBothEdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitEdgeQ1W(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitEdgeQXW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitEdgeQ0W(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEval01EdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEval0XEdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEval10EdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEval1XEdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalX1EdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalX0EdgeW(vec32* pvalCurr, vec32* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitPosEdgeE(scalar* pvalCurr, scalar* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitNegEdgeE(scalar* pvalCurr, scalar* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitBothEdgeE(scalar* pvalCurr, scalar* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitEdgeQ1E(scalar* pvalCurr, scalar* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitEdgeQ0E(scalar* pvalCurr, scalar* pvalPrev, U cbits, U* pedges);
extern U rmaEvalBitChangeE(scalar* pvalCurr, scalar* pvalPrev, U cbits, U* pedges);
extern void rmaScheduleNbaGate(RP pcode, scalar val);
extern void rmaEvalRtlEdgeLoads(RmaRtlEdgeBlockHdr *phdr, US clkEdge, scalar clkVal, scalar prevClkVal, scalar val4,   scalar prevval4, scalar master4val);
extern void rmaEvaluateDynamicGateLoadsCg(RP p, scalar s);
extern void rmaEvaluateFusedWithDynamicGateLoadsCg(RP p, scalar s);
extern void rmaScheduleGatedClockEdgeLoadNew(UB* p, US* ea, U flags, UB* plist, UB* pprevlist, scalar v, scalar pv);
extern void rmaScheduleGatedClockEdgeLoad(UB* p, US* ea, U flags, UB* plist, UB* pprevlist, scalar v);
extern void vcsCheckIfClkValValid(U val, U index);
extern void rmaRemoveNonEdgeLoads(UB* pcode);
extern void rmaRecordEvents(HsimNodeRecord *pnr);
extern void handlePCBs(UB* p, U i);
extern void markMasterClkOvaLists(U fdbs, RP p);
extern void rmaChildClockPropAfterWrite(UB* p);
extern void rmaSchedChildClockPropAfterWrite(UB* p, UB* pmasterList, UB val);
extern void rmaChangeListForMovedGates(UB clkVal, UB f10Edge, UB* subMasterVal, UB* plist, RP* p, U count);
extern void rmaEvalSeqPrimLoadsByteArray(UB* pcode, UB val, UB prevval4);
extern void rmaEvalSeqPrimLoadsByteArrayX(UB* pcode, UB val, UB prevval4);
extern void vcsRmaEvalSeqPrimLoadsByteArraySCT(UB* pcode, UB val, UB prevval4, U c);
extern void rmaEvalSimonCellLoadsByteArray(UB* pcode, UB val, UB prevval4);
extern void rmaEvalSimonCellLoadsByteArrayX(UB* pcode, UB val, UB prevval4);
extern void vcsAbortForBadEBlk(void);
extern scalar edgeChangeLookUp[4][4];
extern void Wsvvar_sched_virt_intf_eval(RP* ptr, char val);
extern void vcs_hwcosim_drive_dut_scalar(U id, char val);
extern void vcs_hwcosim_drive_dut_vector_4state(U id, vec32* val);
extern U vcs_rmaGetClkValForSeqUdpLayoutOnClkOpt(UB* poutput);
extern U vcs_rmaGetClkValForSeqUdpLayoutOnClk(UB* poutput);
extern U vcs_rmaGetClkValForSeqUdp(UB* poutput);
extern void vcs_rmaGetWFForSeqUdp(UB* phead);
extern U add_new_event(void* wfclock, unsigned char s);
extern U has_periodicity_started(void *wfclock);
extern void fire_scalarclock_start_cbk(void* wfclock, void* pdata);
extern void fire_scalarclock_end_cbk(void* wfclock);
extern U is_fsdb_periodic(void *wfclock);
extern void patchWfTs(RP _ppwf);
extern void patchWfWp(RP _ppwf);
extern void patchWfS(RP _ppwf);
extern void patchWfH(RP _ppwf);
extern U is_connected(void* wfclock, U idx);
extern U is_delay_connected(void* wfclock, U idx, scalar *out_prev_val);
extern void set_auto_sched_for_dbs_loads(void* wfclock, scalar edge);
extern U Fsim_handle_wreal_faults(void);
extern U is_event_record_needed(void *waveform);
extern scalar get_value_for_wf(void* ip);
extern U rmaIsS2State(scalar s);
extern U rmaIsV2State(vec32* pval, U cbits);
extern U rmaIsW2State(vec32* pval, U cbits);
extern U rmaIsE2State(scalar* pval, U cbits);
extern void rmaUpdateRecordFor2State(HsimNodeRecord* record, U f2state);
typedef void (*FuncPtr)();
static inline U asm_bsf (U in)
{
#if defined(linux)
    U out;
#if !defined(__aarch64__)
    asm ("movl %1, %%eax; bsf %%eax, %%eax; movl %%eax, %0;"
        :"=r"(out)
        :"r"(in)
        :"%eax"
        );
#else
    out = ffs(in) - 1;
#endif
    return out;
#else
    return 0;
#endif
}


#ifdef __cplusplus
extern "C" {
#endif
void  rmaPropagate0_p_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate0_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate0_f_simv_daidir (UB  * pcode, scalar  val, U  I621, scalar  * I1466, U  did);
void  rmaPropagate0_r_simv_daidir (UB  * pcode);
void  rmaPropagate0_t0_simv_daidir (UB  * pcode, UB  val);
void  rmaPropagate0_wn_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate1_p_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate1_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate1_f_simv_daidir (UB  * pcode, scalar  val, U  I621, scalar  * I1466, U  did);
void  rmaPropagate1_r_simv_daidir (UB  * pcode);
void  rmaPropagate1_t0_simv_daidir (UB  * pcode, UB  val);
void  rmaPropagate1_wn_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate2_p_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate2_simv_daidir (UB  * pcode, scalar  val);
void  rmaPropagate2_f_simv_daidir (UB  * pcode, scalar  val, U  I621, scalar  * I1466, U  did);
void  rmaPropagate2_r_simv_daidir (UB  * pcode);
void  rmaPropagate2_t0_simv_daidir (UB  * pcode, UB  val);
void  rmaPropagate2_wn_simv_daidir (UB  * pcode, scalar  val);
void  schedNewEvent (struct dummyq_struct * I1443, EBLK  * I1438, U  I628);
#ifdef __cplusplus
}
#endif

#ifdef __cplusplus
  }
#endif
#endif /*__DO_RMAHDR_*/

