

AR=ar
DOTLIBS=/opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvirsim.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/liberrorinf.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libsnpsmalloc.so /opt/synopsys/vcs/V-2023.12-SP2/linux64/lib/libvfs.so 

# This file is automatically generated by VCS. Any changes you make to it
# will be overwritten the next time VCS is run
DPI_ALLOW_MAIN_SO=
VCS_LIBEXT=
XTRN_OBJS=

DPI_WRAPPER_OBJS = 
DPI_STUB_OBJS = 
# filelist.dpi will populate DPI_WRAPPER_OBJS and DPI_STUB_OBJS
include filelist.dpi
PLI_STUB_OBJS = 
include filelist.pli

include filelist.hsopt

include filelist.cu

VCS_INCR_OBJS=


AUGDIR=
AUG_LDFLAGS=
SHARED_OBJ_SO=



VLOG_OBJS= $(VCS_OBJS) $(CU_OBJS) $(VCS_ARC0) $(XTRN_OBJS) $(DPI_WRAPPER_OBJS) $(VCS_INCR_OBJS) $(SHARED_OBJ_SO) $(HSOPT_OBJS)    
