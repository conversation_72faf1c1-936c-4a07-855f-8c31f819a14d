# Makefile generated by VCS to build rmapats.so for your model
VSRC=..

# Override TARGET_ARCH
TARGET_ARCH=

# Select your favorite compiler

# Linux:
# Compiler: 
VCS_CC=gcc
# Internal CC for gen_c flow:
CC_CG=gcc

# Loader
LD=g++
# Loader Flags
LDFLAGS=

# Default defines
SHELL=/bin/sh

VCSTMPSPECARG=
VCSTMPSPECENV=
# NOTE: if you have little space in $TMPDIR, but plenty in /foo,
#and you are using gcc, uncomment the next line
#VCSTMPSPECENV=SNPS_VCS_TMPDIR=/foo

TMPSPECARG=$(VCSTMPSPECARG)
TMPSPECENV=$(VCSTMPSPECENV)
CC=$(TMPSPECENV) $(VCS_CC) $(TMPSPECARG)

# C flags for compilation
CFLAGS=$(STDLIB)-w  -pipe -fPIC -g -I../../../nonblocking/src/cpp -I/opt/synopsys/vcs/V-2023.12-SP2/include    

CFLAGS_CG=-w  -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include -O  -fno-strict-aliasing -fno-optimize-sibling-calls   

CFLAGS_CG_CLIB=-w  -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include -O  -fno-strict-aliasing -fno-optimize-sibling-calls   

CFLAGS_RMAR=-w  -pipe -fPIC -I/opt/synopsys/vcs/V-2023.12-SP2/include -O  -fno-strict-aliasing -fno-optimize-sibling-calls   

ASFLAGS=(null)
LIBS=

include filelist.hsopt


rmapats.so: $(HSOPT_OBJS)
	@$(VCS_CC) $(LDFLAGS) $(LIBS) -shared -o ./../simv.daidir/rmapats.so $(HSOPT_OBJS)
