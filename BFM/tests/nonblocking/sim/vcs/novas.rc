@verdi rc file Version 1.0
[Library]
work = ./work
[Annotation]
3D_Active_Annotation = FALSE
[CommandSyntax.finsim]
InvokeCommand = 
FullFileName = TRUE
Separator = .
SimPromptSign = ">"
HierNameLevel = 1
RunContinue = "continue"
Finish = "quit"
UseAbsTime = FALSE
NextTime = "run 1"
NextNTime = "run ${SimBPTime}"
NextEvent = "run 1"
Reset = 
ObjPosBreak = "break posedge ${SimBPObj}"
ObjNegBreak = "break negedge ${SimBPObj}"
ObjAnyBreak = "break change ${SimBPObj}"
ObjLevelBreak = 
LineBreak = "breakline ${SimBPFile} ${SimBPLine}"
AbsTimeBreak = "break abstimeaf ${SimBPTime}"
RelTimeBreak = "break reltimeaf ${SimBPTime}"
EnableBP = "breakon ${SimBPId}"
DisableBP = "breakoff ${SimBPId}"
DeleteBP = "breakclr ${SimBPId}"
DeleteAllBP = "breakclr"
SimSetScope = "cd ${SimDmpObj}"
[CommandSyntax.ikos]
InvokeCommand = "setvar debussy true;elaborate -p ${SimTop} -s ${SimArch}; run until 0;fsdbInteractive; "
FullFileName = TRUE
NeedTimeUnit = TRUE
NormalizeTimeUnit = TRUE
Separator = /
HierNameLevel = 2
RunContinue = "run"
Finish = "exit"
NextTime = "run ${SimBPTime} ${SimTimeUnit}"
NextNTime = "run for ${SimBPTime} ${SimTimeUnit}"
NextEvent = "step 1"
Reset = "reset"
ObjPosBreak = "stop if ${SimBPObj} = \"'1'\""
ObjNegBreak = "stop if ${SimBPObj} = \"'0'\""
ObjAnyBreak = 
ObjLevelBreak = "stop if ${SimBPObj} = ${SimBPValue}"
LineBreak = "stop at ${SimBPFile}:${SimBPLine}"
AbsTimeBreak = 
RelTimeBreak = 
EnableBP = "enable ${SimBPId}"
DisableBP = "disable ${SimBPId}"
DeleteBP = "delete ${SimBPId}"
DeleteAllBP = "delete *"
[CommandSyntax.verisity]
InvokeCommand = 
FullFileName = FALSE
Separator = .
SimPromptSign = "> "
HierNameLevel = 1
RunContinue = "."
Finish = "$finish;"
NextTime = "$db_steptime(1);"
NextNTime = "$db_steptime(${SimBPTime});"
NextEvent = "$db_step;"
SimSetScope = "$scope(${SimDmpObj});"
Reset = "$reset;"
ObjPosBreak = "$db_breakonposedge(${SimBPObj});"
ObjNegBreak = "$db_breakonnegedge(${SimBPObj});"
ObjAnyBreak = "$db_breakwhen(${SimBPObj});"
ObjLevelBreak = "$db_breakwhen(${SimBPObj}, ${SimBPValue});"
LineBreak = "$db_breakatline(${SimBPLine}, ${SimBPScope}, \"${SimBPFile}\");"
AbsTimeBreak = "$db_breakbeforetime(${SimBPTime});"
RelTimeBreak = "$db_breakbeforetime(${SimBPTime});"
EnableBP = "$db_enablebreak(${SimBPId});"
DisableBP = "$db_disablebreak(${SimBPId});"
DeleteBP = "$db_deletebreak(${SimBPId});"
DeleteAllBP = "$db_deletebreak;"
FSDBInit = "$novasInteractive;"
FSDBDumpvars = "$novasDumpvars(0, ${SimDmpObj});"
FSDBDumpsingle = "$novasDumpsingle(${SimDmpObj});"
FSDBDumpvarsInFile = "$novasDumpvarsToFile(\"${SimDmpFile}\");"
FSDBDumpMem = "$novasDumpMemNow(${SimDmpObj}, ${SimDmpBegin}, ${SimDmpSize});"
[CoverageDetail]
cross_filter_limit = 1000
branch_limit_vector_display = 50
showgrid = TRUE
reuseFirst = TRUE
justify = TRUE
scrollbar_mode = per pane
test_combo_left_truncate = TRUE
instance_combo_left_truncate = TRUE
loop_navigation = TRUE
condSubExpr = 20
tglMda = 1000
linecoverable = 100000
lineuncovered = 50000
tglcoverable = 30000
tgluncovered = 30000
pendingMax = 1000
show_full_more = FALSE
max_load_items_one_time = 10000
[CoverageHier]
showgrid = FALSE
[CoverageWeight]
Assert = 1
Covergroup = 1
Line = 1
Condition = 1
Toggle = 1
FSM = 1
Branch = 1
[DesignTree]
IfShowModule = {TRUE, FALSE}
[DisabledMessages]
version = Verdi_V-2023.12-SP2
[Emacs]
EmacsFont = "Clean 14"
EmacsBG = white
EmacsFG = black
[Exclusion]
enableAsDefault = TRUE
saveAsDefault = TRUE
saveManually = TRUE
illegalBehavior = FALSE
displaySeedOnly = TRUE
DisplayExcludedItem = FALSE
adaptiveExclusion = TRUE
warningExcludeInstance = TRUE
propagateExclusion = FALSE
favorite_exclude_annotation = ""
[FSM]
viewport = 65 336 387 479
WndBk-FillColor = ID_GRAY3
Background-FillColor = ID_GRAY5
prefKey_Link-FillColor = ID_YELLOW4
prefKey_Link-TextColor = ID_BLACK
Trap = ID_RED3
Hilight = ID_BLUE4
Window = ID_GRAY3
Selected = ID_WHITE
Trans. = ID_GREEN2
State = ID_BLACK
Init. = ID_BLACK
SmartTips = TRUE
VectorFont = FALSE
StateAutomaticFont = TRUE
AnnotAutomaticFont = TRUE
ShowStateAction = FALSE
ShowTransAction = FALSE
ShowTransCond = FALSE
StateLable = NAME
StateValueRadix = ORIG
State-LineColor = ID_BLACK
State-LineWidth = 1
State-FillColor = ID_BLUE3
State-TextColor = ID_WHITE
Init_State-LineColor = ID_BLACK
Init_State-LineWidth = 2
Init_State-FillColor = ID_YELLOW3
Init_State-TextColor = ID_BLACK
Reset_State-LineColor = ID_BLACK
Reset_State-LineWidth = 2
Reset_State-FillColor = ID_YELLOW7
Reset_State-TextColor = ID_BLACK
Trap_State-LineColor = ID_RED3
Trap_State-LineWidth = 2
Trap_State-FillColor = ID_CYAN4
Trap_State-TextColor = ID_RED3
State_Action-LineColor = ID_BLACK
State_Action-LineWidth = 1
State_Action-FillColor = ID_WHITE
State_Action-TextColor = ID_BLACK
Junction-LineColor = ID_BLACK
Junction-LineWidth = 1
Junction-FillColor = ID_GREEN3
Junction-TextColor = ID_BLACK
Connection-LineColor = ID_BLACK
Connection-LineWidth = 1
Connection-FillColor = ID_GRAY5
Connection-TextColor = ID_BLACK
prefKey_Port-LineColor = ID_BLACK
prefKey_Port-LineWidth = 1
prefKey_Port-FillColor = ID_ORANGE5
prefKey_Port-TextColor = ID_YELLOW2
Transition-LineColor = ID_BLACK
Transition-LineWidth = 1
Transition-FillColor = ID_WHITE
Transition-TextColor = ID_BLACK
Trans_Condition-LineColor = ID_BLACK
Trans_Condition-LineWidth = 1
Trans_Condition-FillColor = ID_WHITE
Trans_Condition-TextColor = ID_ORANGE2
Trans_Action-LineColor = ID_BLACK
Trans_Action-LineWidth = 1
Trans_Action-FillColor = ID_WHITE
Trans_Action-TextColor = ID_GREEN2
SelectedSet-LineColor = ID_RED3
SelectedSet-LineWidth = 1
SelectedSet-FillColor = ID_RED3
SelectedSet-TextColor = ID_WHITE
StickSet-LineColor = ID_ORANGE4
StickSet-LineWidth = 1
StickSet-FillColor = ID_PURPLE6
StickSet-TextColor = ID_BLACK
HilightSet-LineColor = ID_RED4
HilightSet-LineWidth = 1
HilightSet-FillColor = ID_RED7
HilightSet-TextColor = ID_BLUE4
ControlPoint-LineColor = ID_BLACK
ControlPoint-LineWidth = 1
ControlPoint-FillColor = ID_WHITE
Bundle-LineColor = ID_BLACK
Bundle-LineWidth = 1
Bundle-FillColor = ID_WHITE
Bundle-TextColor = ID_BLUE4
QtBackground-FillColor = ID_GRAY6
prefKey_Link-LineColor = ID_ORANGE2
prefKey_Link-LineWidth = 1
Selection-LineColor = ID_BLUE3
Selection-LineWidth = 1
[FSM_Dlg-Print]
Orientation = Landscape
[Form]
version = Verdi_V-2023.12-SP2
[General]
autoSaveSession = FALSE
TclAutoSource = 
cmd_enter_form = FALSE
SyncBrowserDir = TRUE
ViewMouse13Swap = FALSE
ToolbarIconSize = "Large"
verdiAppFont = "DejaVu Sans,-1,18,5,50,0,0,0,0,0", ""
ColorTheme = classic
verdiMonoQFont = "DejaVu Sans Mono,-1,18,5,50,0,0,0,0,0", ""
version = Verdi_V-2023.12-SP2
recent_commands_Verdi = tbvDebugRunContinue,hbInvokeSignalPane,tbvRunDebugMode,tbvDebugRebuild,tbvAttachSim,hbRestoreSession,textCaretLineRight,hbConfigSource
recent_commands_nWave = zoomAll
prevColorTheme = classic
SignalCaseInSensitive = FALSE
[GlobalProp]
ErrWindow_Font = Helvetica_M_R_18
[Globals]
app_default_font = Bitstream Vera Sans,10,-1,5,50,0,0,0,0,0
app_fixed_width_font = Courier 10 Pitch,10,-1,5,50,0,0,0,0,0
text_encoding = Unicode(utf8)
smart_resize = TRUE
smart_resize_child_limit = 2000
tooltip_max_width = 200
tooltip_max_height = 20
tooltip_viewer_key = F3
tooltip_display_time = 1000
bookmark_name_length_limit = 12
disable_tooltip = FALSE
auto_load_source = TRUE
max_array_size = 4096
filter_when_typing = TRUE
filter_keep_children = TRUE
filter_syntax = Wildcards
filter_keystroke_interval = 800
filter_case_sensitive = FALSE
filter_full_path = FALSE
load_detail_for_funcov = FALSE
sort_limit = 100000
ignoreDBVersionChecking = FALSE
[HB]
ViewSchematic = FALSE
windowLayout = 0 0 *********** ***********
import_filter = *.v; *.vc; *.f
designTreeFont = *-adobe-courier-medium-r-*-*-12-*-*-*-*-*-iso8859-*
import_filter_vhdl = *.vhd; *.vhdl; *.f
import_default_language = Verilog
import_filter_verilog = *.v; *.vc; *.f
simulation_file_type = 
PrefetchViewableAnnot = TRUE
ProfileTime = FALSE
[Hier]
filterTimeout = 1500
[ImportLiberty]
bImportPowerInfo = False
CellNameToCase = 
PinNameToCase = 
SearchPriority = .lib++>.db>.lib
bSkipStateCell = False
bSkipFFCell = False
bScpecifyCellNameCase = False
bSpecifyPinNameCase = False
[InteractiveDebug]
tbvLocalWatchArrayLimit = 50
Watch_0 = 150 80 163 0 
Watch_1 = 150 80 80 535 
Watch_2 = 150 80 80 200 
Watch_3 = 150 80 80 200 
Watch_4 = 150 80 80 200 
Watch_5 = 150 80 80 200 
[Language]
EditWindow_Font = COURIER18
Background = ID_WHITE
Comment = ID_GRAY4
Keyword = ID_BLUE5
UserKeyword = ID_GREEN2
Text = ID_BLACK
SelText = ID_WHITE
SelBackground = ID_BLUE2
[Library.Ikos]
pack = ./work.lib++
vital = ./work.lib++
work = ./work.lib++
std = ${dls_std}.lib++
ieee = ${dls_ieee}.lib++
synopsys = ${dls_synopsys}.lib++
silc = ${dls_silc}.lib++
ikos = ${dls_ikos}.lib++
novas = ${VOYAGER_LIB_VHDL}/${VOYAGER_MACHINE}/novas.lib++
[MDT]
ART_RF_SP = spr[0-9]*bx[0-9]*
ART_RF_2P = dpr[0-9]*bx[0-9]*
ART_SRAM_SP = spm[0-9]*bx[0-9]*
ART_SRAM_DP = dpm[0-9]*bx[0-9]*
VIR_SRAM_SP = hdsd1_[0-9]*x[0-9]*cm4sw1
VIR_SRAM_DP = hdsd2_[0-9]*x[0-9]*cm4sw1
VIR_RF_SP = rfsd1_[0-9]*x[0-9]*cm2sw0
VIR_RF_DP = rfsd2_[0-9]*x[0-9]*cm2sw1
VIR_STAR_SRAM_SP = shsd1_[0-9]*x[0-9]*cm4sw0
[NPExpanding]
functiongroups = FALSE
modules = FALSE
[NPFilter]
showAssertion = TRUE
showCoverGroup = TRUE
showProperty = TRUE
showSequence = TRUE
showDollarUnit = TRUE
[OldFontRC]
HB_designTreeFont = *-adobe-courier-medium-r-*-*-12-*-*-*-*-*-iso8859-*
Text_font = COURIER12
nMemory_font = Fixed 14
Wave_legend_window_font = -f COURIER18 -c ID_CYAN5
Wave_value_window_font = -f COURIER18 -c ID_CYAN5
Wave_curve_window_font = -f COURIER18 -c ID_CYAN5
Wave_group_name_font = -f COURIER18 -c ID_GREEN5
Wave_ruler_value_font = -f COURIER18 -c ID_CYAN5
Wave_analog_ruler_value_font = -f COURIER18 -c ID_CYAN5
Wave_comment_string_font = -f COURIER18 -c ID_RED5
Wave_getsignal_form_font = -f COURIER18
Text_annotFont = FIXED12
schematics_fixFontSize = False
[OpenFile]
CFileType = Source files (*.v; *.vlg; *.vl; *.vr; *.vrh; *.sv; *.svh; *.ova; *.vhd; *.vhdl; *.psl; *.vu; *.e;*.c; *.cpp; *.cxx; *.cc; *.h; *.hpp; *.hxx; *.hh; *.tcl; *.tk; *.do; *.upf; *.cpf);;HDL files (*.v; *.vlg; *.vl; *.vr; *.vrh; *.sv; *.svh; *.ova; *.vhd; *.vhdl\
; *.psl; *.vu; *.e);;C/C++ files (*.c; *.cpp; *.cxx; *.cc; *.h; *.hpp; *.hxx; *.hh);;Tcl files (*.tcl; *.tk; *.do);;Power files (*.upf;*.cpf)
[OtherEditor]
cmd1 = "xterm -font 9x15 -fg black -bg gray -e"
name = "vi"
options = "+${CurLine} ${CurFullFileName}"
[Power]
PowerDownInstance = ID_GRAY1
RetentionSignal = ID_YELLOW2
IsolationSignal = ID_RED6
LevelShiftedSignal = ID_GREEN6
PowerSwitchObject = ID_ORANGE5
AlwaysOnObject = ID_GREEN5
PowerNet = ID_RED2
GroundNet = ID_RED2
SimulationOnly = ID_CYAN3
SRSN/SPA = ID_CYAN3
CNSSignal = ID_CYAN3
RPTRSignal = ID_CYAN3
AcknowledgeSignal = ID_CYAN3
BoundaryPort = ID_CYAN3
DisplayInstrumentedCell = TRUE
ShowCmdByFile = FALSE
ShowPstAnnot = FALSE
ShowIsoSymbol = TRUE
ExtractIsoSameNets = FALSE
AnnotateSignal = TRUE
HighlightPowerObject = TRUE
HighlightPowerDomain = TRUE
TraceThroughInstruLowPower = TRUE
BrightenPowerColorInSchematicWindow = FALSE
ShowAlias = FALSE
ShowVoltage = TRUE
MatchTreeNodesCaseInsensitive = FALSE
SearchHBNodeDynamically = FALSE
ContinueTracingSupplyOrLogicNet = FALSE
[Print]
PrinterName = lp
FileName = test.ps
PaperSize = A4 - 210x297 (mm)
ColorPrint = FALSE
[PropertyTools]
Dnd_Sig_To_Wave = FALSE
Dnd_Sig_To_Wave_With_Exp_Group = TRUE
Mask_Non_Active_Time_on_Wave = FALSE
Show_Cycle_Info = TRUE
saveWaveformStat = TRUE
savePropStat = FALSE
savePropDtl = TRUE
[QtDialog]
verdiNewFontDlg = 1020,625,500,360
monoQFontDlg = 1020,625,500,360
qPrefDlg = 912,593,716,424
FileWithNoteForm_base = 952,514,655,543
QwWarnMsgDlg = 970,926,600,250
tbvSimProcessDlgBase = 968,596,622,433
unknownSave = 1048,690,461,245
tbvRebuildRestartDlg = 851,589,856,447
QwUserAskDlg = 1062,738,436,151
[Relationship]
hideRecursiceNode = FALSE
[Safety]
RegisterObject = ID_RED4
CoreObject = ID_ORANGE5
SafeFSMObject = ID_YELLOW7
ErrorCorrectionObject = ID_GREEN5
ErrorObject = ID_GRAY5
AnnotateSignal = TRUE
HighlightSafetyObject = TRUE
[Session Cache]
2 = string (session file name)
3 = string (session file name)
4 = string (session file name)
5 = string (session file name)
1 = /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/verdiLog/novas_autosave.ses
[Signal List]
sigListBackgroundColor = ID_GRAY6
sigListForegroundColor = ID_BLACK
sigListHighlightColor = ID_BLUE3
signalNumberInOnePage = 500
ShowValueColumn = TRUE
[Simulation]
scsPath = scsim
scsOption = 
xlPath = verilog
xlOption = 
ncPath = ncsim
ncOption = -f ncsim.args
osciPath = gdb
osciOption = 
vcsPath = simv
vcsOption = 
mtiPath = vsim
mtiOption = 
vhncPath = ncsim
vhncOption = -log debussy.nc.log
mixncPath = ncsim
mixncOption = -log debussy.mixnc.log
speedsimPath = 
speedsimOption = 
mti_vlogPath = vsim
mti_vlogOption = novas_vlog
vcs_mixPath = simv
vcs_mixOption = -vhdlrun "-vhpi debussy:FSDBDumpCmd"
scs_mixPath = scsim
scs_mixOption = -vhpi debussy:FSDBDumpCmd
iscCmdSep = FALSE
thirdpartyIdx = -1
simType = vcssv
NoAppendOption = FALSE
ScsDebugAll = FALSE
bSVTBMode = TRUE
SimMode = TRUE
interactiveDebugging = TRUE
KeepBreakPoints = FALSE
[SimulationPlus]
xlPath = verilog
xlOption = 
ncPath = ncsim
ncOption = -f ncsim.args
vcsPath = simv
vcsOption = 
mti_vlogPath = vsim
mti_vlogOption = novas_vlog
mtiPath = vsim
mtiOption = 
vhncPath = ncsim
vhncOption = -log debussy.nc.log
speedsimPath = verilog
speedsimOption = 
mixncPath = ncsim
mixncOption = -log debussy.mixnc.log
scsPath = scsim
scsOption = 
vcs_mixPath = simv
vcs_mixOption = -vhdlrun "-vhpi debussy:FSDBDumpCmd"
scs_mixPath = scsim
scs_mixOption = -vhpi debussy:FSDBDumpCmd
iscCmdSep = FALSE
simXLMode = FALSE
interactive_fsdb_file = inter.fsdb
invokeSimPath = work
thirdpartyIdx = -1
NoAppendOption = FALSE
ScsDebugAll = FALSE
uvmAware = TRUE
simType = vcssv
bSVTBMode = TRUE
SimMode = TRUE
interactiveDebugging = TRUE
KeepBreakPoints = FALSE
vcs_svPath = /trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv
vcs_svOption = -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking
smartlog = FALSE
[SimulationPlus2]
eventDumpUnfinish = FALSE
dumpPowerRoot = FALSE
[Source]
wordWrapOn = TRUE
viewReuse = TRUE
lineNumberOn = TRUE
warnOutdatedDlg = TRUE
showEncrypt = FALSE
loadInclude = FALSE
showColorForActive = FALSE
tabWidth = 8
editor = vi
reload = Never
sync_active_to_source = TRUE
navigateAsColored = FALSE
navigateCovered = FALSE
navigateUncovered = TRUE
navigateExcluded = FALSE
not_ask_for_source_path = FALSE
expandMacroOn = TRUE
expandMacroInstancesThreshold = 10000
annotateOnStructInstDecl = FALSE
annotateFSMStateLimitNum = -1
annotateFSMLimitNum = -1
[SourceVHDL]
vhSimType = ModelSim
ohSimType = VCS
[TclShell]
nLineSize = 1024
[Test]
verbose_progress = FALSE
[TestBenchBrowser]
hideWatchView = TRUE
-showUVMDynamicHierTreeWin = FALSE
DataViewTooltip = TRUE
[Text]
hdlTypeName = blue4
hdlLibrary = blue4
viewport = 396 392 445 487
hdlOther = ID_BLACK
hdlComment = ID_GRAY1
hdlKeyword = ID_BLUE5
hdlEntity = ID_BLACK
hdlEntityInst = ID_BLACK
hdlSignal = ID_RED2
hdlInSignal = ID_RED2
hdlOutSignal = ID_RED2
hdlInOutSignal = ID_RED2
hdlOperator = ID_BLACK
hdlMinus = ID_BLACK
hdlSymbol = ID_BLACK
hdlString = ID_BLACK
hdlNumberBase = ID_BLACK
hdlNumber = ID_BLACK
hdlLiteral = ID_BLACK
hdlIdentifier = ID_BLACK
hdlSystemTask = ID_BLACK
hdlParameter = ID_BLACK
hdlIncFile = ID_BLACK
hdlDataFile = ID_BLACK
hdlCDSkipIf = ID_GRAY1
hdlMacro = ID_BLACK
hdlMacroValue = ID_BLACK
hdlPlainText = ID_BLACK
hdlOvaId = ID_PURPLE2
hdlPslId = ID_PURPLE2
HvlEId = ID_BLACK
HvlVERAId = ID_BLACK
hdlEscSignal = ID_BLACK
hdlEscInSignal = ID_BLACK
hdlEscOutSignal = ID_BLACK
hdlEscInOutSignal = ID_BLACK
textBackgroundColor = ID_GRAY6
textHiliteBK = ID_BLUE5
textHiliteText = ID_WHITE
textTracedMark = ID_GREEN2
textLineNo = ID_BLACK
textFoldedLineNo = ID_RED5
textUserKeyword = ID_GREEN2
textParaAnnotText = ID_BLACK
textFuncAnnotText = ID_BLUE2
textAnnotText = ID_BLACK
textUserDefAnnotText = ID_BLACK
ComputedSignal = ID_PURPLE5
textAnnotTextShadow = ID_WHITE
parenthesisBGColor = ID_YELLOW5
codeInParenthesis = ID_CYAN5
text3DLight = ID_WHITE
text3DShadow = ID_BLACK
textHvlDriver = ID_GREEN3
textHvlLoad = ID_YELLOW3
textHvlDriverLoad = ID_BLUE3
irOutline = ID_RED2
irDriver = ID_YELLOW5
irLoad = ID_BLACK
irBookMark = ID_YELLOW2
irIndicator = ID_WHITE
irBreakpoint = ID_GREEN5
irCurLine = ID_BLUE5
hdlVhEntity = ID_BLACK
hdlArchitecture = ID_BLACK
hdlPackage = ID_BLUE5
hdlRefPackage = ID_BLUE5
hdlAlias = ID_BLACK
hdlGeneric = ID_BLUE5
specialAnnotShadow = ID_BLUE1
hdlZeroInHead = ID_GREEN2
hdlZeroInComment = ID_GREEN2
hdlPslHead = ID_BLACK
hdlPslComment = ID_GRAY4
hdlSynopsysHead = ID_GREEN2
hdlSynopsysComment = ID_GREEN2
pdmlIdentifier = ID_BLACK
pdmlCommand = ID_BLACK
pdmlMacro = ID_BLACK
font = COURIER18
annotFont = FIXED12
[Text.1]
viewport = -1 27 2560 1497 45
[TextPrinter]
Orientation = Landscape
Indicator = FALSE
LineNum = TRUE
FontSize = 7
Column = 2
Annotation = TRUE
[Texteditor]
TexteditorFont = "Clean 14"
TexteditorBG = white
TexteditorFG = black
[ThirdParty]
ThirdPartySimTool = verisity surefire ikos finsim
[TurboEditor]
autoBackup = TRUE
[UserButton.mixnc]
Button1 = "Dump All Signals" "call fsdbDumpvars\n"
Button2 = "Next 1000 Time" "run 1000 -relative\n"
Button3 = "Next ? Time" "run ${Arg:Next Time} -relative\n"
Button4 = "Run Next" "run -next\n"
Button5 = "Run Step" "run -step\n"
Button6 = "Run Return" "run -return\n"
Button7 = "Show Variables" "value {${NCSelVars}}\n"
Button8 = "FSDB Ver" "call fsdbVersion"
Button9 = "Dump On" "call fsdbDumpon"
Button10 = "Dump Off" "call fsdbDumpoff"
Button11 = "All Tasks" "call"
Button12 = "Dump Selected Instance" "call fsdbDumpvars 1 ${SelInst}"
[UserButton.mti]
Button1 = "Dump All Signals" "fsdbDumpvars\n"
Button2 = "Next 1000 Time" "run 1000\n"
Button3 = "Next ? Time" "run ${Arg:Next Time}\n"
Button4 = "Show Variables" "exa ${SelVars}\n"
Button5 = "Force Variable" "force -freeze ${SelVar} ${Arg:New Value} 0\n"
Button6 = "Release Variable" "noforce ${SelVar}\n"
Button7 = "Deposit Variable" "force -deposit ${SelVar} ${Arg:New Value} 0\n"
[UserButton.mti_vlog]
Button1 = "Dump All Signals" "fsdbDumpvars\n"
Button2 = "Next 1000 Time" "run 1000\n"
Button3 = "Next ? Time" "run ${Arg:Next Time}\n"
Button4 = "Show Variables" "exa ${SelVars}\n"
Button5 = "Force Variable" "force -freeze ${SelVar} ${Arg:New Value} 0\n"
Button6 = "Release Variable" "noforce ${SelVar}\n"
Button7 = "Deposit Variable" "force -deposit ${SelVar} ${Arg:New Value} 0\n"
[UserButton.nc]
Button1 = "Dump All Signals" "call fsdbDumpvars\n"
Button2 = "Next 1000 Time" "run 1000 -relative\n"
Button3 = "Next ? Time" "run ${Arg:Next Time} -relative\n"
Button4 = "Run Next" "run -next\n"
Button5 = "Run Step" "run -step\n"
Button6 = "Run Return" "run -return\n"
Button7 = "Show Variables" "value {${NCSelVars}}\n"
[UserButton.scs]
Button1 = "Dump All Signals" "call fsdbDumpvars(0, \"${TopScope}\");\n"
Button2 = "Next 1000 Time" "run 1000 \n"
Button3 = "Next ? Time" "run ${Arg:Next Time} \n"
Button4 = "Run Step" "step\n"
Button5 = "Show Variables" "ls -v {${SelVars}}\n"
[UserButton.vhnc]
Button1 = "Dump All Signals" "call fsdbDumpvars\n"
Button2 = "Next 1000 Time" "run 1000 -relative\n"
Button3 = "Next ? Time" "run ${Arg:Next Time} -relative\n"
Button4 = "Run Next" "run -next\n"
Button5 = "Run Step" "run -step\n"
Button6 = "Run Return" "run -return\n"
Button7 = "Show Variables" "value {${NCSelVars}}\n"
[UserButton.xl]
Button13 = "Dump Off" "$fsdbDumpoff;\n"
Button12 = "Dump On" "$fsdbDumpon;\n"
Button11 = "Delete Focus" "$db_deletefocus(${treeSelScope});\n"
Button10 = "Set Focus" "$db_setfocus(${treeSelScope});\n"
Button9 = "Deposit Variable" "$deposit(${SelVar},${Arg:New Value});\n"
Button8 = "Release Variable" "release ${SelVar};\n"
Button7 = "Force Variable" "force ${SelVar} = ${Arg:New Value};\n"
Button6 = "Show Variables" "$showvars(${SelVars});\n"
Button5 = "Next ? Event" "$db_step(${Arg:Next Event});\n"
Button4 = "Next Event" "$db_step(1);\n"
Button3 = "Next ? Time" "#${Arg:Next Time} $stop;.\n"
Button2 = "Next 1000 Time" "#1000 $stop;.\n"
Button1 = "Dump All Signals" "$fsdbDumpvars;\n"
[VIA]
viaLogViewerDefaultRuleOneSearchForm = "share/VIA/Apps/PredefinedRules/Misc/Onesearch_rule.rc"
viaLogViewerDefaultRuleInterForm = "share/VIA/Apps/PredefinedRules/UVM_OVM_i_rule.rc"
[VIA.interactiveDebug.preference]
DefaultDisplayTimeUnit = "1.000000ns"
DefaultLogTimeUnit = "1.000000ns"
[VIA.interactiveDebug.preference.vgifColumnSettingRC]
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0]
parRuleSets = "/opt/synopsys/verdi/V-2023.12-SP2/share/VIA/Apps/PredefinedParRules/par_rule_OVM.rc /opt/synopsys/verdi/V-2023.12-SP2/share/VIA/Apps/PredefinedParRules/par_rule_UVM.rc /opt/synopsys/verdi/V-2023.12-SP2/share/VIA/Apps/PredefinedParRules/par_rule_LP.rc /opt\
/synopsys/verdi/V-2023.12-SP2/share/VIA/Apps/PredefinedParRules/par_rule_VCS.rc "
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0.column0]
name = "Block's First Line"
width = 120
visualIndex = 1
isHidden = TRUE
isUserChangeColumnVisible = FALSE
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0.column1]
name = "Severity"
width = 60
visualIndex = 2
isHidden = FALSE
isUserChangeColumnVisible = FALSE
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0.column2]
name = "Reporter"
width = 60
visualIndex = 3
isHidden = FALSE
isUserChangeColumnVisible = FALSE
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0.column3]
name = "Message"
width = 2000
visualIndex = 6
isHidden = FALSE
isUserChangeColumnVisible = FALSE
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0.column4]
name = "Time"
width = 60
visualIndex = 0
isHidden = FALSE
isUserChangeColumnVisible = FALSE
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0.column5]
name = "Code"
width = 60
visualIndex = 4
isHidden = TRUE
isUserChangeColumnVisible = FALSE
[VIA.interactiveDebug.preference.vgifColumnSettingRC.setting0.column6]
name = "Type"
width = 60
visualIndex = 5
isHidden = TRUE
isUserChangeColumnVisible = FALSE
[VIA.oneSearch.preference]
DefaultDisplayTimeUnit = "1.000000ns"
DefaultLogTimeUnit = "1.000000ns"
isOneSearchVisible = FALSE
[VIA.oneSearch.preference.vgifColumnSettingRC]
[VIA.oneSearch.preference.vgifColumnSettingRC.setting0]
parRuleSets = ""
[VIA.oneSearch.preference.vgifColumnSettingRC.setting0.column0]
name = "Severity"
width = 60
visualIndex = 2
isHidden = FALSE
isUserChangeColumnVisible = FALSE
[VIA.oneSearch.preference.vgifColumnSettingRC.setting0.column1]
name = "Block's First Line"
width = 120
visualIndex = 1
isHidden = TRUE
isUserChangeColumnVisible = FALSE
[VIA.oneSearch.preference.vgifColumnSettingRC.setting0.column2]
name = "Time"
width = 60
visualIndex = 0
isHidden = FALSE
isUserChangeColumnVisible = FALSE
[VIA.oneSearch.preference.vgifColumnSettingRC.setting0.column3]
name = "Code"
width = 60
visualIndex = 3
isHidden = TRUE
isUserChangeColumnVisible = FALSE
[VIA.oneSearch.preference.vgifColumnSettingRC.setting0.column4]
name = "Type"
width = 60
visualIndex = 4
isHidden = TRUE
isUserChangeColumnVisible = FALSE
[VIA.oneSearch.preference.vgifColumnSettingRC.setting0.column5]
name = "Message"
width = 2000
visualIndex = 5
isHidden = FALSE
isUserChangeColumnVisible = FALSE
[VIA.parRule]
parRulePathInterForm = ""
[Vi]
ViFont = "Clean 14"
ViBG = white
ViFG = black
[Wave]
signalSpacing = 5
digitalSignalHeight = 15
analogSignalHeight = 98
commentSignalHeight = 98
minCompErrWidth = 4
DragZoomTolerance = 4
WaveMaxPoint = 512
openDumpFilter = *.fsdb;*.vf;*.jf;*.zwd*
DumpFileFilter = *.vcd
RestoreSignalFilter = *.rc
SaveSignalFilter = *.rc
AddAliasFilter = *.alias
CompareSignalFilter = *.err
ConvertFFFilter = *.vcd;*.out;*.tr0;*.xp;*.raw;*.wfm
Scroll_Ratio = 1.000000
SigGroupRuleFile = 
hierarchyName = FALSE
snap = TRUE
displayLeadingZero = FALSE
displayCursorMarker = FALSE
restoreFromActiveFile = TRUE
anaAuto100VertFit = FALSE
centerCursor = FALSE
InOutSignal = FALSE
NetRegisterSignal = FALSE
VerilogVHDLSignal = FALSE
LabelMarker = TRUE
ResolveSymbolicLink = TRUE
TipInSignalWin = FALSE
TipInCurveWin = FALSE
DisplayLSBsFirst = FALSE
ModuleName = TRUE
TransitionMode = TRUE
SchemaX = FALSE
Hilight = TRUE
UseBeforeValue = FALSE
DisplayFileName = FALSE
DisplayValueSpace = TRUE
ReloadActiveFileOnly = FALSE
NormalizeEVCD = FALSE
OverwriteAliasWithRC = TRUE
vhdlVariableCalculate = TRUE
signal_vertical_scroll_bar = TRUE
waveform_vertical_scroll_bar = TRUE
ConvertAttr1 = -inc FALSE
ConvertAttr2 = -hier FALSE
ConvertAttr3 = -ucase FALSE
ConvertAttr4 = -lcase FALSE
ConvertAttr5 = -org FALSE
ConvertAttr7 = -deli .
ConvertAttr8 = -hier_scope FALSE
ConvertAttr9 = -inst_array FALSE
ConvertAttr10 = -vhdlnaming FALSE
ConvertAttr11 = -orgScope FALSE
analogFmtPrecision = Automatic 2
confirmOverwrite = TRUE
confirmGetAll = TRUE
hspiceIncOpenMode = TRUE
pcSelectMode = TRUE
hierarchyDelimiter = /
value_window_aligment = Right
signal_window_alignment = Auto
ShowDeltaTime = TRUE
WaveExtendLastTick = TRUE
displayMessageLabelOnly = TRUE
AutoApplySeverityColor = TRUE
scope_to_save_with_macro
open_file_dir
open_rc_file_dir
getSignalForm = 0 0 800 479 100 30 100 30
viewPort = 0 29 2560 687 468 65
legendBackground = -c ID_BLACK
valueBackground = -c ID_BLACK
curveBackground = -c ID_BLACK
getSignalSignalList_BackgroundColor = -c ID_GRAY6
glitchColor = -c ID_RED5
cursor = -c ID_YELLOW5 -lw 1 -ls long_dashed
marker = -c ID_WHITE -lw 1 -ls dash_dot_l
usermarker = -c ID_GREEN5 -lw 1 -ls long_dashed
trace = -c ID_GRAY5 -lw 1 -ls long_dashed
grid = -c ID_GRAY3 -lw 1 -ls dot_dashed
rulerBackground = -c ID_GRAY3
rulerForeground = -c ID_YELLOW5
busTextColor = -c ID_ORANGE8
legendForeground = -c ID_CYAN5
valueForeground = -c ID_CYAN5
curveForeground = -c ID_CYAN5
groupNameColor = -c ID_GREEN5
commentStringColor = -c ID_RED5
region(Active)Background = -c ID_YELLOW1
region(NBA)Background = -c ID_RED1
region(Re-Active)Background = -c ID_YELLOW3
region(Re-NBA)Background = -c ID_RED3
region(VHDL-Delta)Background = -c ID_ORANGE3
region(Dump-Off)Background = -c ID_GRAY4
High_Light = -c ID_GRAY2
Input_Signal = -c ID_RED5
Output_Signal = -c ID_GREEN5
InOut_Signal = -c ID_BLUE5
Net_Signal = -c ID_YELLOW5
Register_Signal = -c ID_PURPLE5
Verilog_Signal = -c ID_CYAN5
VHDL_Signal = -c ID_ORANGE5
SystemC_Signal = -c ID_BLUE7
Dump_Off_Color = -c ID_BLUE2
Compress_Bar_Color = -c ID_YELLOW4
Vector_Dense_Block_Color = -c ID_ORANGE8
Scalar_Dense_Block_Color = -c ID_GREEN6
Analog_Dense_Block_Color = -c ID_PURPLE2
Composite_Dense_Block_Color = -c ID_RED5
RPTR_Power_Off_Layer = -c ID_CYAN3 -stipple dots
DB_Power_Off_Layer = -c ID_BLUE4 -stipple dots
SPA_Driver_Power_Off_Layer = -c ID_ORANGE4 -stipple dots
SPA_Receiver_Power_Off_Layer = -c ID_GREEN5 -stipple dots
SRSN_Power_Off_Layer = -c ID_GREEN4 -stipple dots
Isolation_Power_Off_Layer = -c ID_RED4 -stipple dots
PD_Power_Off_Layer = -c ID_GRAY4 -stipple dots
Isolation_Layer = -c ID_RED4 -stipple vLine
Retention_Level_Trigger_Layer = -c ID_ORANGE1 -stipple fill_solid
Retention_Edge_Trigger_Layer = -c ID_YELLOW6 -stipple fill_solid
Driving_Power_Off_Layer = -c ID_YELLOW2 -stipple x
Toggle_Layer = -c ID_YELLOW4 -stipple slash
analogRealStyle = pwl
analogVoltageStyle = pwl
analogCurrentStyle = pwl
analogOthersStyle = pwl
busSignalLayer = -c ID_ORANGE8
busXLayer = -c ID_RED5
busZLayer = -c ID_ORANGE6
busMixedLayer = -c ID_GREEN5
busNotComputedLayer = -c ID_GRAY1
busNoValueLayer = -c ID_BLUE2
signalGridLayer = -c ID_GRAY3
analogGridLayer = -c ID_GRAY6
analogRulerLayer = -c ID_GRAY6
keywordLayer = -c ID_RED5
loadedLayer = -c ID_BLUE5
loadingLayer = -c ID_BLACK
qdsCurMarkerLayer = -c ID_BLUE5
qdsBrkMarkerLayer = -c ID_GREEN5
qdsTrgMarkerLayer = -c ID_RED5
arrowDefaultColor = -c ID_ORANGE6
startNodeArrowColor = -c ID_WHITE
endNodeArrowColor = -c ID_YELLOW5
propertyEventMatchColor = -c ID_GREEN5
propertyEventNoMatchColor = -c ID_RED5
propertyVacuousSuccessMatchColor = -c ID_YELLOW2
propertyStatusBoundaryColor = -c ID_WHITE
propertyBooleanSuccessColor = -c ID_CYAN5
propertyBooleanFailureColor = -c ID_RED5
propertyAssertSuccessColor = -c ID_GREEN5
propertyAssertFailureColor = -c ID_RED5
propertyForbidSuccessColor = -c ID_GREEN5
transactionForegroundColor = -c ID_YELLOW8
transactionBackgroundColor = -c ID_BLACK
transactionHighLightColor = -c ID_CYAN6
transactionRelationshipColor = -c ID_PURPLE6
transactionErrorTypeColor = -c ID_RED5
coverageFullyCoveredColor = -c ID_GREEN5
coverageNoCoverageColor = -c ID_RED5
coveragePartialCoverageColor = -c ID_RED5
coverageReferenceLineColor = -c ID_RED5
messageForegroundColor = -c ID_YELLOW4
messageBackgroundColor = -c ID_PURPLE1
messageHighLightColor = -c ID_CYAN6
messageInformationColor = -c ID_RED5
ComputedAnnotColor = -c ID_PURPLE5
fsvSecurityDataColor = -c ID_PURPLE3
qdsAutoBusGroup = TRUE
qdsTimeStampMode = FALSE
qdsVbfBusOrderAscending = FALSE
EventSequence_SyncCursorTime = TRUE
EventSequence_Sorting = FALSE
EventSequence_RemoveGrid = FALSE
EventSequence_IsGridMode = FALSE
SigSearchSignalMatchCase = FALSE
SigSearchSignalScopeOption = FALSE
SigSearchSignalSamenetInterface = FALSE
SigSearchSignalFullScope = FALSE
SigSearchSignalWithRegExp = FALSE
SigSearchDynamically = FALSE
SigDisplayBySelectionOrder = FALSE
SigDisplayRowMajor = FALSE
SigDragSelFollowColumn = FALSE
SigDisplayHierarchyBox = TRUE
SigDisplaySubscopeBox = TRUE
SigDisplayEmptyScope = TRUE
SigDisplaySignalNavigationBox = FALSE
SigDisplayFormBus = TRUE
SigShowSubProgram = TRUE
SigSearchScopeDynamically = TRUE
SigCollapseSubtreeNodes = FALSE
activeFileApplyToAnnotation = FALSE
GrpSelMode = TRUE
dispGridCount = FALSE
autoInsertDumpoffs = TRUE
allfileTimeRange = FALSE
fixDelta = FALSE
autoUpdate = FALSE
restoreToEnd = FALSE
dispCompErr = TRUE
showMsgDes = TRUE
anaAutoFit = FALSE
anaAutoPattn = FALSE
verticalFitWhenZoomAll = FALSE
analogToDigitalByInterpolation = FALSE
displayDeltaY = FALSE
signal_rc_abspath = TRUE
signal_rc_no_natural_bus_range = FALSE
save_scope_with_macro = FALSE
FitAnaByBusSize = FALSE
DisplayVerticalZoomTools = FALSE
DisplayIndicateBar = FALSE
overlay_added_analog_signals = FALSE
overlay_same_added_analog_signals = FALSE
case_insensitive = FALSE
resaveSignals = TRUE
adjustBusPrefix = adjustBus_
adjustBusBits = 1
adjustBusSettings = 69889
maskPowerOff = TRUE
maskIsolation = TRUE
maskRetention = TRUE
maskDrivingPowerOff = TRUE
maskToggle = TRUE
signal_rc_attribute = 65535
signal_rc_alias_attribute = 0
ConvertAttr6 = -mem 24
confirmExit = TRUE
printTimeRange = TRUE 0.000000 0.000000 0.000000
printPageRange = TRUE 1 1
printOption = 0
printBasic = 1 0 0 FALSE FALSE
printDest = -printer {}
printSignature = {%f %h %t} {}
curveWindow_Drag&Drop_Mode = TRUE
RecentFile1 = "\"/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/novas.fsdb\""
RecentFile2 = "\"/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/inter.fsdb\""
open_file_time_range = FALSE
legend_window_font = -f COURIER18 -c ID_CYAN5
value_window_font = -f COURIER18 -c ID_CYAN5
curve_window_font = -f COURIER18 -c ID_CYAN5
group_name_font = -f COURIER18 -c ID_GREEN5
ruler_value_font = -f COURIER18 -c ID_CYAN5
analog_ruler_value_font = -f COURIER18 -c ID_CYAN5
comment_string_font = -f COURIER18 -c ID_RED5
getsignal_form_font = -f COURIER18 
SigsCheckNum = on 1000
filter_synthesized_net = off n
filterOutNet = on
exclude_signal_with_prefix = off n
synthesizeNetOnly = on
filter_synthesized_instance = off 
filterOutInstance = on
showGroupTree = TRUE
hierGroupDelim = /
MsgSeverityColor = {y \"Severity\"==\"1\" ID_RED5} {y \"Severity\"==\"2\" ID_RED6} {y \"Severity\"==\"3\" ID_RED7} {y \"Severity\"==\"4\" ID_RED8} {y \"Severity\"==\"5\" ID_ORANGE5} {y \"Severity\"==\"6\" ID_ORANGE6} {y \"Severity\"==\"7\" ID_ORANGE7} {y \"Severity\"==\"8\" \
ID_GREEN7} {y \"Severity\"==\"9\" ID_GREEN6} {y \"Severity\"==\"10\" ID_GREEN5} 
waveDblClkActiveTrace = on
SEQShowComparisonIcon = TRUE
SEQAddDriverLoadInSameGroup = TRUE
displayVerticalRuler = FALSE
displayHorizontalRuler = FALSE
displayVerticalGrid = FALSE
displayHorizontalGrid = FALSE
maskFsaRegRule = TRUE
maskFsaCoreRule = TRUE
maskFsaSfsmRule = TRUE
maskFsaEcrRule = TRUE
maskTPropagation = TRUE
[cov_hier_name_column]
justify = TRUE
[coverageColors]
sou_uncov = TRUE
sou_pc = TRUE
sou_cov = TRUE
sou_exuncov = TRUE
sou_excov = TRUE
sou_unreach = TRUE
sou_unreachcon = TRUE
sou_incon = TRUE
sou_uncov_coi = TRUE
sou_non_active = TRUE
sou_non_fc = TRUE
sou_fillColor_uncov = red
sou_fillColor_pc = yellow
fca_sou_fillColor_pc = greenyellow
sou_fillColor_cov = green3
sou_fillColor_exuncov = darkgray
sou_fillColor_excov = #3C9371
sou_fillColor_unreach = gray
sou_fillColor_unreachcon = red
sou_fillColor_incon = yellow
sou_fillColor_uncov_coi = violet
sou_fillColor_non_active = goldenrod
sou_fillColor_non_fc = orange
numberOfBins = 6
rangeMin_0 = 0
rangeMax_0 = 20
fillColor_0 = #FF6464
rangeMin_1 = 20
rangeMax_1 = 40
fillColor_1 = #FF9999
rangeMin_2 = 40
rangeMax_2 = 60
fillColor_2 = #FF8040
rangeMin_3 = 60
rangeMax_3 = 80
fillColor_3 = #FFFF99
rangeMin_4 = 80
rangeMax_4 = 100
fillColor_4 = #99FF99
rangeMin_5 = 100
rangeMax_5 = 100
fillColor_5 = #64FF64
[coveragesetting]
geninfodumping = 0
geninfoColumnShown = 0
assertTopoMode = FALSE
urgAppendOptions = 
group_instance_new_format_name = TRUE
showvalue = FALSE
computeGroupsScoreByRatio = FALSE
computeGroupsScoreByInst = FALSE
showConditionId = FALSE
showfullhier = FALSE
nameLeftAlignment = TRUE
showAllInfoInTooltips = FALSE
showModuleNameInSummaryHierarchy = false
copyItemHvpName = TRUE
ignoreGroupWeight = FALSE
absTestName = FALSE
HvpMergeTool = 
ShowMergeMenuItem = FALSE
fsmScoreMode = transition
[eco]
NameRule = 
IsFreezeSilicon = FALSE
cellQuantityManagement = FALSE
ManageMode = INSTANCE_NAME
SpareCellsPinsManagement = TRUE
LogCommitReport = FALSE
InputPinStatus = 1
OutputPinStatus = 2
RevisedComponentColor = ID_BLUE5
SpareCellColor = ID_RED5
UserName = harriszh
CommentFormat = Novas ECO updated by ${UserName} ${Date} ${Time} 
PrefixN = eco_n
PrefixP = eco_p
PrefixI = eco_i
DefaultTieUpNet = 1'b1
DefaultTieDownNet = 1'b0
MultipleInstantiations = TRUE
KeepClockPinConnection = FALSE
KeepAsyncResetPinConnection = FALSE
AddCommentSummary = FALSE
AddCommentSummaryToDesign = TRUE
AddCommentSummaryToFile = eco_comment_summary.log
ScriptFileModeType = 1
MagmaScriptPower = VDD
MagmaScriptGround = GND
ShowModeMsg = TRUE
AstroScriptPower = VDD
AstroScriptGround = VSS
ICCScriptPower = VDD
ICCScriptGround = VSS
FEScriptPower = vdd!
FEScriptGround = gnd!
ClearFloatingPorts = FALSE
[eco_connection]
Port/NetIsUnique = TRUE
SerialNet = 0
SerialPort = 0
SerialInst = 0
[finsim]
TPLanguage = Verilog
TPName = Super-FinSim
TPPath = TOP.sim
TPOption = 
AddImportArgument = FALSE
LineBreakWithScope = FALSE
StopAfterCompileOption = -i
[fusa]
tooltip_summary = TRUE
Name_Show = TRUE
Coverage_Show = TRUE
Test_Cov_Show = TRUE
Fault_Cov_Show = TRUE
Undetected_Show = TRUE
Num_of_Fault_Show = TRUE
Num_of_Displayed_Fault_Show = FALSE
Num_of_Inst_Show = TRUE
[hvpsetting]
importExcelXMLOptions = 
use_test_loca_as_source = FALSE
autoTurnOffHideMeetGoalInit = FALSE
autoTurnOffHideMeetGoal = TRUE
autoTurnOffModifierInit = FALSE
autoTurnOffModifier = TRUE
enableNumbering = TRUE
autoSaveCheck = TRUE
autoSaveTime = 5
ShowMissingScore = TRUE
enableFeatureId = FALSE
enable_HVP_FEAT_ID = FALSE
enableMeasureConcealment = FALSE
HvpCloneHierShowMsgAgain = 1
HvpCloneHierType = tree
HvpCloneHierMetrics = Line,Cond,FSM,Toggle,Branch,Assert
autoRecalPlanAfterLoadingCovDBUserDataPlan = false
warnMeAutoRecalPlanAfterLoadingCovDBUserDataPlan = true
autoRecalExclWithPlan = false
warnMeAutoRecalExclWithPlan = true
autoRecalPlanWithExcl = false
warnMeAutoRecalPlanWithExcl = true
warnPopupWarnWhenMultiFilters = true
warnPopupWarnIfHvpReadOnly = true
unmappedObjsReportLevel = def_var_inst
unmappedObjsReportInst = true
unmappedObjsNumOfObjs = High
[hwswdebug]
eclipse = NULL
eclipse_options = NULL
debugger_type = Eclipse
[ikos]
TPLanguage = VHDL
TPName = Voyager
TPPath = vsh
TPOption = -X
AddImportArgument = FALSE
LineBreakWithScope = FALSE
StopAfterCompileOption = -i
[imp]
options = NULL
libPath = NULL
libDir = NULL
[nCompare]
ErrorViewport = 80 180 800 550
EditorViewport = 409 287 676 475
EditorHeightWidth = 802 380
WaveCommand = "novas"
WaveArgs = "-nWave"
EnableAutoCompleteHint = TRUE
ShowByBottomUpOrder = FALSE
[nCompare.Wnd0]
ViewByHier = FALSE
[nMemory]
dispMode = ADDR_HINT
addrColWidth = 120
valueColWidth = 100
showCellBitRangeWithAddr = TRUE
wordsShownInOneRow = 8
syncCursorTime = FALSE
fixCellColumnWidth = FALSE
font = Courier 18
[nRegister]
rectangle = 1
line = 1
ellipse = 1
arrow = 1
[planColors]
plan_fillColor_inactive = lightgray
plan_fillColor_warning = orange
plan_fillColor_error = red
plan_fillColor_invalid = #F0DCDB
plan_fillColor_subplan = lightgray
[rctSchema]
tsMaxForLoopToUnroll = 0
tsClkSkewSave = True
tsClkSkewSetting = 0
tsIPSave = True
tsIPSetting = 
tsMemApiFile = 
tsMemApiFileSave = True
tsContBAError = FALSE
tsMaxLeafNum = 32768
tsBBoxSave = True
tsBBoxSetting = 
tsBBoxEmptyModule = False
tsBBoxIgnoreProtected = False
tsSymLibSave = True
tsSymLibSetting = 
tsSimModelSetting = 
tsCellModelSetting = 0
[rtla]
RtlaDbPath = 
[schematics]
viewport = 178 262 638 516
ComputedAnnotColor = ID_PURPLE5
schBackgroundColor = ID_BLACK lineSolid 1
schBackgroundColor_qt = #000000 qt_solidLine 1
schBodyColor = ID_ORANGE6 lineSolid 1
schBodyColor_qt = #ffb973 qt_solidLine 1
schTriColor = ID_ORANGE6 lineSolid 1
schTriColor_qt = #ffb973 qt_solidLine 1
schRegColor = ID_ORANGE6 lineSolid 1
schRegColor_qt = #ffb973 qt_solidLine 1
schLatchColor = ID_ORANGE6 lineSolid 1
schLatchColor_qt = #ffb973 qt_solidLine 1
schAsmBodyColor = ID_BLUE7 lineSolid 1
schAsmBodyColor_qt = #a5a5ff qt_solidLine 1
schAsmTriColor = ID_BLUE7 lineSolid 1
schAsmTriColor_qt = #a5a5ff qt_solidLine 1
schAsmRegColor = ID_BLUE7 lineSolid 1
schAsmRegColor_qt = #a5a5ff qt_solidLine 1
schAsmLatchColor = ID_BLUE7 lineSolid 1
schAsmLatchColor_qt = #a5a5ff qt_solidLine 1
schPortColor = ID_ORANGE6 lineSolid 1
schPortColor_qt = #ffb973 qt_solidLine 1
schCellNameColor = ID_GRAY6 lineSolid 1
schCellNameColor_qt = #e0e0e0 qt_solidLine 1
schPortNameColor = ID_GRAY6 lineSolid 1
schPortNameColor_qt = #e0e0e0 qt_solidLine 1
schInstNameColor = ID_GRAY6 lineSolid 1
schInstNameColor_qt = #e0e0e0 qt_solidLine 1
schAnnotColor = ID_GRAY6 lineSolid 1
schAnnotColor_qt = #e0e0e0 qt_solidLine 1
schEdgeSensitiveColor = ID_ORANGE6 lineSolid 1
schEdgeSensitiveColor_qt = #ffb973 qt_solidLine 1
schCLKNetColor = ID_RED6 lineSolid 1
schCLKNetColor_qt = #ff7373 qt_solidLine 1
schPWRNetColor = ID_RED4 lineSolid 1
schPWRNetColor_qt = #be4242 qt_solidLine 1
schGNDNetColor = ID_CYAN4 lineSolid 1
schGNDNetColor_qt = #42bebe qt_solidLine 1
schSIGNetColor = ID_GREEN8 lineSolid 1
schSIGNetColor_qt = #cdffcd qt_solidLine 1
schBusNetColor = ID_GREEN8 lineSolid 2
schBusNetColor_qt = #cdffcd qt_solidLine 2
schCLKBusNetColor = ID_RED6 lineSolid 2
schCLKBusNetColor_qt = #ff7373 qt_solidLine 2
schPWRBusNetColor = ID_RED4 lineSolid 2
schPWRBusNetColor_qt = #be4242 qt_solidLine 2
schGNDBusNetColor = ID_CYAN4 lineSolid 2
schGNDBusNetColor_qt = #42bebe qt_solidLine 2
schGlobalSigNetColor = ID_GREEN2 lineSolid 1
schGlobalSigNetColor_qt = #008000 qt_solidLine 1
schGlobalSigBusNetColor = ID_GREEN2 lineSolid 2
schGlobalSigBusNetColor_qt = #008000 qt_solidLine 2
schHilightColor = ID_RED4 lineDash 2
schHilightColor_qt = #be4242 qt_dashLine 2
schTraceColor = ID_YELLOW4 lineSolid 2
schTraceColor_qt = #bebe42 qt_solidLine 2
schBackAnnotateColor = ID_WHITE lineSolid 1
schBackAnnotateColor_qt = #ffffff qt_solidLine 1
schValue0 = ID_YELLOW4 lineSolid 1
schValue0_qt = #bebe42 qt_solidLine 1
schValue1 = ID_GREEN3 lineSolid 1
schValue1_qt = #5ba55b qt_solidLine 1
schValueX = ID_RED4 lineSolid 1
schValueX_qt = #be4242 qt_solidLine 1
schValueZ = ID_PURPLE8 lineSolid 1
schValueZ_qt = #ffcdff qt_solidLine 1
netName = ID_PURPLE8 lineSolid 1
netName_qt = #ffcdff qt_solidLine 1
dimColor = ID_CYAN2 lineSolid 1
dimColor_qt = #008080 qt_solidLine 1
paramColor = ID_YELLOW5 lineSolid 1
paramColor_qt = #ffff01 qt_solidLine 1
ParamBackColor = ID_GRAY1 lineSolid 1
ParamBackColor_qt = #404040 qt_solidLine 1
schPreSelColor = ID_GREEN4 lineDash 2
schPreSelColor_qt = #42be42 qt_dashLine 2
schTipBackColor = ID_YELLOW8 lineSolid 1
schTipBackColor_qt = #ffffcd qt_solidLine 1
schRubberBandColor = ID_YELLOW5 lineSolid 1
schRubberBandColor_qt = #ffff01 qt_solidLine 1
schDenaliModule = ID_GREEN4 lineSolid 1
schDenaliModule_qt = #42be42 qt_solidLine 1
schFsmBlock = ID_BLUE7 lineSolid 1
schFsmBlock_qt = #a5a5ff qt_solidLine 1
schFFBlackBoxColor = ID_BLUE7 lineSolid 1
schFFBlackBoxColor_qt = #a5a5ff qt_solidLine 1
schLatchBlackBoxColor = ID_BLUE7 lineSolid 1
schLatchBlackBoxColor_qt = #a5a5ff qt_solidLine 1
schMacroBlackBoxColor = ID_BLUE7 lineSolid 1
schMacroBlackBoxColor_qt = #a5a5ff qt_solidLine 1
schValuePropagationConstantNet0Color = ID_BLUE4 lineSolid 1
schValuePropagationConstantNet0Color_qt = #4242be qt_solidLine 1
schValuePropagationConstantNet1Color = ID_RED4 lineSolid 1
schValuePropagationConstantNet1Color_qt = #be4242 qt_solidLine 1
schAutoMergeGroupNetColor = ID_GREEN5 lineSolid 1
schAutoMergeGroupNetColor_qt = #01ff01 qt_solidLine 1
schInstPowerAnnot = ID_GREEN5 lineSolid 1
schInstPowerAnnot_qt = #01ff01 qt_solidLine 1
schPdmlIsoNonCoverNetColor = ID_RED5 lineDash 1
schPdmlIsoNonCoverNetColor_qt = #ff0101 qt_dashLine 1
schPdmlLvsNonCoverNetColor = ID_GREEN5 lineDash 1
schPdmlLvsNonCoverNetColor_qt = #01ff01 qt_dashLine 1
schClockMapGroupColor = ID_GREEN5 lineSolid 1
schClockMapGroupColor_qt = #01ff01 qt_solidLine 1
schClockMapLastCommonInstColor = ID_RED5 lineSolid 1
schClockMapLastCommonInstColor_qt = #ff0101 qt_solidLine 1
schClockMapConvergenceInstColor = ID_YELLOW5 lineSolid 1
schClockMapConvergenceInstColor_qt = #ffff01 qt_solidLine 1
schPdmlLvsNetColor = ID_GRAY4 lineSolid 1
schPdmlLvsNetColor_qt = #a0a0a0 qt_solidLine 1
schHierFlattenScopeInstColor = ID_BLUE6 lineSolid 2
schHierFlattenScopeInstColor_qt = #7373ff qt_solidLine 2
schHierFlattenScopeInsidePinColor = ID_PURPLE5 lineSolid 1
schHierFlattenScopeInsidePinColor_qt = #ff01ff qt_solidLine 1
schSDCCreateClockColor = ID_RED5 lineSolid 1
schSDCCreateClockColor_qt = #ff0101 qt_solidLine 1
schSDCCreateGeneralClockColor = ID_GREEN5 lineSolid 1
schSDCCreateGeneralClockColor_qt = #01ff01 qt_solidLine 1
schSDCSetCaseAnalysis = ID_YELLOW5 lineSolid 1
schSDCSetCaseAnalysis_qt = #ffff01 qt_solidLine 1
schSDCSetDisbaleTiming = ID_PURPLE5 lineSolid 1
schSDCSetDisbaleTiming_qt = #ff01ff qt_solidLine 1
schSubcktCellColor = ID_GREEN5 lineSolid 1
schSubcktCellColor_qt = #01ff01 qt_solidLine 1
schCloudCellColor = ID_BLUE6 lineDashDot 2
schCloudCellColor_qt = #7373ff qt_dashDotLine 2
schPunchPortColor = ID_RED5 lineSolid 1
schPunchPortColor_qt = #ff0101 qt_solidLine 1
schPowerPadColor = ID_RED5 lineSolid 1
schPowerPadColor_qt = #ff0101 qt_solidLine 1
schZciSignal = ID_CYAN5 lineSolid 1
schZciSignal_qt = #00e3e3 qt_solidLine 1
schZOIXDivergeRangeStyle = ID_PURPLE6 lineSolid 1
schZOIXDivergeRangeStyle_qt = #ff62ff qt_solidLine 1
Other = ID_BLACK lineSolid 1
Other_qt = #000000 qt_solidLine 1
schSamBlock = ID_CYAN6 lineSolid 1
schSamBlock_qt = #73ffff qt_solidLine 1
schZTimeHighlightGroup = ID_RED5 lineSolid 1
schZTimeHighlightGroup_qt = #ff0101 qt_solidLine 1
schZTimeGroup = ID_GRAY4 lineSolid 1
schZTimeGroup_qt = #a0a0a0 qt_solidLine 1
schGridColor = ID_GRAY3 lineSolid 1
schGridColor_qt = #808080 qt_solidLine 1
schCtrlPointColor = ID_GREEN5 lineSolid 1
schCtrlPointColor_qt = #01ff01 qt_solidLine 1
schCtrlPointOutlineColor = ID_BLACK lineSolid 1
schCtrlPointOutlineColor_qt = #000000 qt_solidLine 1
schActiveWireSegColor = ID_RED5 lineSolid 2
schActiveWireSegColor_qt = #ff0101 qt_solidLine 2
schLockedObjColor = ID_RED5 lineSolid 1
schLockedObjColor_qt = #ff0101 qt_solidLine 1
schMixLvlBoundColor = ID_YELLOW5 lineDash 1
schMixLvlBoundColor_qt = #ffff01 qt_dashLine 1
schNameObjLinkLine = ID_RED5 lineDash 1
schNameObjLinkLine_qt = #ff0101 qt_dashLine 1
schWarning = ID_RED5 lineSolid 1
schWarning_qt = #ff0101 qt_solidLine 1
schPaperOutline = ID_WHITE lineSolid 1
schPaperOutline_qt = #ffffff qt_solidLine 1
schPaperInline = ID_WHITE lineDash 1
schPaperInline_qt = #ffffff qt_dashLine 1
schBundleNetColor = ID_BLUE4 lineSolid 4
schBundleNetColor_qt = #4242be qt_solidLine 4
schAbstractLink = ID_YELLOW5 lineDash 1
schAbstractLink_qt = #ffff01 qt_dashLine 1
schLinkLabel = ID_GREEN4 lineSolid 1
schLinkLabel_qt = #60be42 qt_solidLine 1
schCmtLinking = ID_YELLOW5 lineDash 2
schCmtLinking_qt = #ffff01 qt_dashLine 2
schInputPortColor = ID_ORANGE6 lineSolid 1
schInputPortColor_qt = #ffb973 qt_solidLine 1
schOutputPortColor = ID_ORANGE6 lineSolid 1
schOutputPortColor_qt = #ffb973 qt_solidLine 1
schInOutPortColor = ID_ORANGE6 lineSolid 1
schInOutPortColor_qt = #ffb973 qt_solidLine 1
schInputInstPortColor = ID_ORANGE6 lineSolid 1
schInputInstPortColor_qt = #ffb973 qt_solidLine 1
schOutputInstPortColor = ID_ORANGE6 lineSolid 1
schOutputInstPortColor_qt = #ffb973 qt_solidLine 1
schInOutInstPortColor = ID_ORANGE6 lineSolid 1
schInOutInstPortColor_qt = #ffb973 qt_solidLine 1
schZoomByAreaColor = ID_YELLOW5 lineSolid 1
schZoomByAreaColor_qt = #ffff01 qt_solidLine 1
schAreaSelectColor = ID_YELLOW5 lineSolid 1
schAreaSelectColor_qt = #ffff01 qt_solidLine 1
schCmtBgColor = ID_YELLOW4 lineSolid 1
schCmtBgColor_qt = #bebe42 qt_solidLine 1
schCmtGraphicsColor = ID_YELLOW4 lineSolid 2
schCmtGraphicsColor_qt = #bebe42 qt_solidLine 2
schRevisedObjColor = ID_PURPLE5 lineSolid 2
schRevisedObjColor_qt = #ff01ff qt_solidLine 2
schBlackBoxColor = ID_YELLOW4 lineSolid 1
schBlackBoxColor_qt = #bebe42 qt_solidLine 1
schFirstSelectedColor = ID_GREEN5 lineSolid 2
schFirstSelectedColor_qt = #01ff01 qt_solidLine 2
schFillBackgroundColor = ID_BLACK blank
schFillBodyColor = ID_ORANGE6 blank
schFillTriColor = ID_ORANGE6 blank
schFillRegColor = ID_ORANGE6 blank
schFillLatchColor = ID_ORANGE6 blank
schFillAsmBodyColor = ID_BLUE7 blank
schFillAsmTriColor = ID_BLUE7 blank
schFillAsmRegColor = ID_BLUE7 blank
schFillAsmLatchColor = ID_BLUE7 blank
schFillTipBackColor = ID_GRAY6 blank
schFillFsmBlock = ID_BLUE7 blank
schFillBlackBoxColor = ID_BLUE3 dots
schFillCmtColor = ID_YELLOW4 fill_solid
ConnectedPinColor = ID_YELLOW6
ConnectedPinColor_qt = #ffff73
fanInOutLevel = 1
schColorTheme = customized
schSwapDragBtn13 = False
schEnableLMBDnd = off
EntityName = True
ArchName = True
toolbar = True
msgLine = True
fixFontSize = False
autoSelectLineStyle = False
ClockAutomaticFont = FALSE
portName = False
pinName = False
pinNameOldLayout = False
boundaryCellNameDown = False
skipTopLevelNameWhenCopyHierPath = False
instName = False
pinNetName = False
localNetName = False
completeName = False
parameterList = False
highContrastMode = False
defaultBlockedCellNumber = 100
faultStatusAnnot = False
faultStatusBarOnFlopClass = False
faultStatusBarOnPinPort = False
faultStatusBarOnNet = False
annotate = False
showPropagatedValue = False
showConstantNet = False
annotateInColor = False
annotateLeadingZeros = False
Tip = False
ExpandRTL = False
pre_select = True
autoFit = False
schemaTip = False
modulePortName = False
stopOnModuleBoundary = False
stopOnFSM = True
stopOnMOSCell = FALSE
checkFloatingNet = False
TraceWithPolarity = False
AMSTraceEnabled = False
AMSTraceFullPath = False
AMSTraceDiodePropagating = Bidirectional
shortName = True
AutoMerge = False
AutoMergeSettingFile = 
ShowPowerTip = True
schModuleName = True
DisplayLengthwiseTransistor = False
FullHierTip = False
NetConnectedToPortTip = False
InferenceLibCell = False
DisplayPowerCell = False
ShowPGPin = True
ShowSelectionToolbar = False
ShowPreselectionToolbar = False
HiearchyFlattenMode = False
UpperPassThroughNets = False
ShowNetConnection = False
ShowAllHierPortsOnBoundary = False
LineupRegistersVertically = False
FixViewOnTraceStartingInst = False
HighlightTracedNet = False
fullHierPartialDis = False
ComboGLKFromSDC = False
ClkTreeListAllPorts = False
ShowPassThroughNet = False
ExpandToAllDL = True
PowerAwareCellColor = True
StickyLocator = False
KeepOnTop = False
DetailRTL = False
DetailMux = True
DetailLevel = 4
RecogFSM = True
ExpandGenBlock = True
RecogMem = True
PowerMapFile = 
GroundMapFile = 
CopyScopeNameDelimiter = 
sdfDelayScale = Default
sdfDelayType = Typical
sdfDelayPrecision = 0.01
RegNamingRule = 
MagnifierSize = Medium
MagnifierRatio = X4
HideUnusedPin = True
HideUnusedPinThreshold = 15
bMaxTraceCnt = False
MaxTraceCnt = 0
MultiProcTrace = True
MultiProcTimeOut = 5
MultiProcNum = 2
AdvanceTraceBus = FALSE
TurnOffMouseGesture = FALSE
QuickTrace2Point = FALSE
ShowLocOption = TRUE
InputPinLeft = True
InputPinRight = True
InputPinTop = True
InputPinBottom = True
OutputPinLeft = True
OutputPinRight = True
OutputPinTop = True
OutputPinBottom = True
InoutPinLeft = True
InoutPinRight = True
InoutPinTop = True
InoutPinBottom = True
PinIntervalGrids = 2
PageView = False
AutoCacualte = False
PageSize = A4
PageOrient = Landscape
PnRAutoFlipPort = True
PnRAutoFlipPortFourSide = False
IsShowConstantValue = True
moduleFontName = "Default"
moduleFontColor = ID_CYAN5
moduleFontAdjustedSize = 5
moduleFontBold = FALSE
moduleFontItalic = FALSE
moduleFontOverride = FALSE
moduleFontAutoAdjust = TRUE
instFontName = "Default"
instFontColor = ID_CYAN5
instFontAdjustedSize = 5
instFontBold = FALSE
instFontItalic = FALSE
instFontOverride = FALSE
instFontAutoAdjust = TRUE
portFontName = "Default"
portFontColor = ID_CYAN5
portFontAdjustedSize = 3
portFontBold = FALSE
portFontItalic = FALSE
portFontOverride = FALSE
portFontAutoAdjust = TRUE
instPortFontName = "Default"
instPortFontColor = ID_CYAN5
instPortFontAdjustedSize = 3
instPortFontBold = FALSE
instPortFontItalic = FALSE
instPortFontOverride = FALSE
instPortFontAutoAdjust = TRUE
netFontName = "Default"
netFontColor
netFontAdjustedSize = 3
netFontBold = FALSE
netFontItalic = FALSE
netFontOverride = FALSE
netFontAutoAdjust = TRUE
commentFontName = "Default"
commentFontColor = ID_BLUE3
commentFontAdjustedSize = 10
commentFontBold = FALSE
commentFontItalic = FALSE
commentFontOverride = FALSE
commentFontAutoAdjust = TRUE
SubScopeFontName = "Default"
SubScopeFontColor = ID_BLUE3
SubScopeFontAdjustedSize = 8
SubScopeFontBold = FALSE
SubScopeFontItalic = FALSE
SubScopeFontOverride = FALSE
SubScopeFontAutoAdjust = TRUE
clockTreeBrowser-MarkerColor = ID_YELLOW5
clockTreeBrowser-SelectedColor = ID_BLUE3
clockTreeBrowser-ViolationColor = ID_RED5
[schematics_print]
Signature = FALSE
DesignName = PCU
DesignerName = bai
SignatureLocation = LowerRight
MultiPage = TRUE
AutoSliver = FALSE
[sourceColors]
BackgroundActive = gray88
BackgroundInactive = lightgray
InactiveCode = dimgray
Selection = darkblue
Standard = black
Keyword = blue
Comment = gray25
Number = black
String = black
Identifier = darkred
Inline = green
colorIdentifier = green
Value = darkgreen
MacroBackground = white
Missing = #400040
[specColors]
top_plan_linked = #ADFFA6
top_plan_ignore = #D3D3D3
top_plan_todo = #EECBAD
sub_plan_ignore = #919191
sub_plan_todo = #EFAFAF
sub_plan_linked = darkorange
[spec_link_setting]
use_spline = true
goto_section = false
exclude_ignore = true
truncate_abstract = false
abstract_length = 999
compare_strategy = 2
auto_apply_margin = FALSE
margin_top = 0.80
margin_bottom = 0.80
margin_left = 0.50
margin_right = 0.50
margin_unit = inches
[spiceDebug]
ThroughNet = ID_YELLOW5
InstrumentSig = ID_GREEN5
InterfaceElement = ID_GREEN5
Run-timeInterfaceElement = ID_BLUE5
MVDSSCSig = ID_BLUE7
HighlightThroughNet = TRUE
HighlightInterfaceElement = TRUE
HighlightRuntimeInterfaceElement = TRUE
HighlightSameNet = TRUE
[surefire]
TPLanguage = Verilog
TPName = SureFire
TPPath = verilog
TPOption = 
AddImportArgument = TRUE
LineBreakWithScope = TRUE
StopAfterCompileOption = -tcl
[tAnalyzer]
pageSize = 30000
cellAlignLeft = TRUE
cellAlignCenter = FALSE
cellAlignRight = FALSE
leadingZeros = FALSE
[tRelation]
CellAlign = left
AlignAttByCol = FALSE
[tSchema]
tsDefaultSize = 0
tfgRegViewBG = ID_BLACK
tfgStmtViewBG = ID_GRAY1
tfgSchemaViewBG = ID_BLACK
tfgSelectedBG = ID_RED4
tfgFolderBG = ID_YELLOW4
tfgNormalValue = ID_CYAN4
tfgSetDirtyValue = ID_RED5
tfgSetCleanValue = ID_ORANGE5
tfgChangedValue = ID_YELLOW5
tfgActiveBG = ID_RED5
tfgDefaultBG = ID_GRAY4
tfgWinWaveWavefkrm = ID_GREEN4
tfgNWaveHLOutput = ID_YELLOW5
tfgNwaveHLActive = ID_YELLOW5
tfgNwaveHLNonActive = ID_GRAY3
tfgValueComputed = ID_GREEN5
tfgValueNonComputed = ID_CYAN4
tfgValue0 = ID_YELLOW5
tfgValue1 = ID_ORANGE5
tfgValueX = ID_PURPLE7
tfgValueZ = ID_ORANGE8
[turboSchema_Printer_Options]
Orientation = Landscape
[turbo_library]
bdb_load_scope = 
[uvmFactory]
FactQfOn = TRUE
FactOICOn = TRUE
FactTbOff = TRUE
FactOverrideAlignment = left
[uvmPhase]
PhaseObjLimit = 5000
PhaseTbOff = FALSE
PhaseObjectAlignment = left
[uvmRegister]
RegTreeDepth = 0
RegShowInHier = TRUE
RegShowDeclName = FALSE
RegShowNavTxtFields = FALSE
RegShowMoreAccessHistory = FALSE
RegTbOff = FALSE
[uvmResource]
RsrcAHOn = TRUE
RsrcQfOn = TRUE
RsrcTbOff = TRUE
RsrcScopeAlignment = left
RsrcValueRadix = Hex
[uvmSequence]
SeqTreeDepth = 1
SeqQFTimeRange = FALSE
SeqQfOn = TRUE
SeqTbOff = TRUE
SeqSequenceAlignment = left
[vdCovFilteringSearchesStrings]
keepLastUsedFiltersMaxNum = 10
[verdiRebuildDialog]
rebuildDesign = TRUE
rebuildFromCustomCmd = FALSE
rebuildReloadFromCustomCmd = FALSE
rebuildReloadFromCustomCmdStr = verdi -dbdir "/trunk/hj/sideband_out/BFM/tests/nonblocking/sim/vcs/simv.daidir"
rebuildRestartSim = TRUE
rebuildRestartSimOpt = -sv_lib ../../../nonblocking/pseudo_bridge_nonblocking
[verisity]
TPLanguage = Verilog
TPName = "Verisity SpeXsim"
TPPath = vlg
TPOption = 
AddImportArgument = FALSE
LineBreakWithScope = TRUE
StopAfterCompileOption = -s
[wave.0]
viewPort = 0 29 2560 687 468 65
[wave.1]
viewPort = 127 219 960 332 100 65
[wave.2]
viewPort = 38 314 686 205 100 65
[wave.3]
viewPort = 63 63 700 400 65 41
[wave.4]
viewPort = 84 84 700 400 65 41
[wave.5]
viewPort = 92 105 700 400 65 41
[wave.6]
viewPort = 0 0 700 400 65 41
[wave.7]
viewPort = 21 21 700 400 65 41
