#!/bin/bash

# Test runner script for sideband_out BFM
# This script runs all test cases and reports results

set -e  # Exit on any error

echo "=========================================="
echo "Sideband Out BFM Test Suite"
echo "=========================================="

# Check if VCS is available
if command -v vcs &> /dev/null; then
    SIMULATOR="vcs"
    echo "Using VCS simulator"
elif command -v xrun &> /dev/null; then
    SIMULATOR="xlm"
    echo "Using Xcelium simulator"
else
    echo "Error: No supported simulator found (VCS or Xcelium)"
    exit 1
fi

# Test configurations
TEST_SUITES=("nonblocking" "blocking")
TEST_CASES=("basic" "overflow")

# Results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a single test
run_test() {
    local suite=$1
    local case=$2
    local test_name="${suite}_${case}"
    
    echo ""
    echo "----------------------------------------"
    echo "Running test: $test_name"
    echo "----------------------------------------"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # Clean previous results
    make clean TEST_SUITE=$suite TEST_CASE=$case > /dev/null 2>&1 || true
    
    # Run the test
    if make $SIMULATOR TEST_SUITE=$suite TEST_CASE=$case; then
        echo "✓ PASSED: $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "✗ FAILED: $test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Run all test combinations
for suite in "${TEST_SUITES[@]}"; do
    for case in "${TEST_CASES[@]}"; do
        # Skip overflow test for blocking suite (not implemented)
        if [[ "$suite" == "blocking" && "$case" == "overflow" ]]; then
            continue
        fi
        run_test $suite $case
    done
done

# Print summary
echo ""
echo "=========================================="
echo "Test Summary"
echo "=========================================="
echo "Total tests:  $TOTAL_TESTS"
echo "Passed tests: $PASSED_TESTS"
echo "Failed tests: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo ""
    echo "🎉 All tests passed!"
    exit 0
else
    echo ""
    echo "❌ Some tests failed!"
    exit 1
fi
