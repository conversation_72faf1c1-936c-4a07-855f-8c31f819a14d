# Sideband Out BFM Test Suite

This directory contains comprehensive tests for the sideband_out BFM implementation.

## Overview

The test suite validates the updated BFM design with the following key features:
- No `sideband_valid` signals (simplified interface)
- Vector-based notifications (complete sideband_in vector sent to software)
- Software-side bit change detection and analysis
- Unified delay configuration for all signals
- Circular FIFO with overwrite capability
- Both blocking and non-blocking DPI-C modes

## Test Structure

```
BFM/tests/
├── Makefile                    # Main build system
├── run_tests.sh               # Automated test runner
├── README.md                  # This file
└── nonblocking/               # Non-blocking test suite
    ├── src/
    │   ├── cpp/
    │   │   ├── pseudo_bridge.cpp          # Basic test bridge
    │   │   ├── pseudo_bridge_overflow.cpp # Overflow test bridge
    │   │   └── sideband_out_dpi.h         # DPI-C interface header
    │   └── sv/
    │       ├── tb_sideband_out.sv         # Basic functionality test
    │       └── tb_fifo_overflow.sv        # FIFO overflow test
    └── sim/                   # Generated simulation files
```

## Test Cases

### 1. Basic Functionality Test (`TEST_CASE=basic`)
- **File**: `tb_sideband_out.sv`
- **Purpose**: Validates core BFM functionality
- **Features tested**:
  - Edge detection on sideband_in signals
  - Unified delay configuration
  - DPI-C notification mechanism
  - Multiple signal handling

### 2. FIFO Overflow Test (`TEST_CASE=overflow`)
- **File**: `tb_fifo_overflow.sv`
- **Purpose**: Validates circular FIFO behavior under stress
- **Features tested**:
  - FIFO overflow with overwrite policy
  - High-frequency signal changes
  - Multiple simultaneous signal changes
  - Event ordering and loss behavior

## Usage

### Prerequisites
- VCS or Xcelium simulator
- GCC compiler with C++17 support
- Make utility

### Running Tests

#### Quick Start - Run All Tests
```bash
cd BFM/tests
./run_tests.sh
```

#### Manual Test Execution

1. **Basic functionality test**:
```bash
make vcs TEST_SUITE=nonblocking TEST_CASE=basic
# or
make xlm TEST_SUITE=nonblocking TEST_CASE=basic
```

2. **FIFO overflow test**:
```bash
make vcs TEST_SUITE=nonblocking TEST_CASE=overflow
# or
make xlm TEST_SUITE=nonblocking TEST_CASE=overflow
```

#### Available Make Targets

- `make help` - Show all available options
- `make vcs` - Compile and run with VCS
- `make xlm` - Compile and run with Xcelium
- `make vcs_grun` - Run with VCS GUI
- `make xlm_grun` - Run with Xcelium GUI
- `make clean` - Clean current test artifacts
- `make cleanall` - Clean all generated files

### Configuration Options

- `TEST_SUITE`: `nonblocking` (blocking tests not yet implemented)
- `TEST_CASE`: `basic` or `overflow`
- `DUMP_FSDB`: `1` to enable FSDB dumping (default: 1)

## Expected Results

### Basic Test
- Should see signal edge detections
- Delayed notifications based on configured delay
- Clean simulation completion

### Overflow Test
- Should see FIFO write/read operations
- Evidence of circular overwrite when FIFO fills
- Multiple events processed correctly
- Summary statistics at end of simulation

## Debugging

### Enable Debug Output
The tests include comprehensive debug output showing:
- Signal edge detection
- FIFO operations (write/read)
- DPI-C notifications
- Event statistics

### Waveform Analysis
Use GUI mode for detailed waveform analysis:
```bash
make vcs_grun TEST_CASE=overflow
```

### Log Files
Simulation logs are stored in:
- `nonblocking/sim/vcs/vcs.log` (VCS)
- `nonblocking/sim/xlm/xrun_sim.log` (Xcelium)

## Key Design Validation

These tests validate the updated specification requirements:
1. ✅ No valid signals required
2. ✅ Unified delay for all channels
3. ✅ Circular FIFO with overwrite
4. ✅ No signal loss under normal conditions
5. ✅ Predictable behavior under overflow conditions
6. ✅ Both blocking and non-blocking modes supported

## Troubleshooting

### Common Issues
1. **Simulator not found**: Ensure VCS or Xcelium is in PATH
2. **Compilation errors**: Check GCC version (requires C++17)
3. **DPI-C linking issues**: Verify simulator DPI-C support

### Getting Help
Run `make help` for detailed usage information.
