# 通用软硬件协同交互模块(Xtor)设计准则

本文件旨在为软硬件协同的交互模块（Xtor）设计提供一套全面的规则和最佳实践。这些准则覆盖了从纯监视器（Monitor）到复杂的双向交互模块，确保其在仿真（Simulation）和硬件仿真（Emulation）环境中的健壮性、可重用性和可维护性。

## 1. 模块设计 (Module Design)

### 1.1. 支持多实例 (Multi-Instance Support)
- **准则**: 任何 Xtor 模块都必须支持在设计中被多次实例化。
- **实施**:
    - 模块必须包含一个 `ID` 或 `INSTANCE_ID` `parameter`，用于在实例化时分配一个唯一的标识符。
    - 这个 `ID` 必须通过 DPI-C 调用传递给软件侧，以便日志和数据能够被正确地归属到对应的硬件实例。

### 1.2. 参数化位宽 (Parameterized Widths)
- **准则**: 严禁对地址或数据总线的位宽进行硬编码。
- **实施**:
    - 使用 `parameter` 来定义所有可变的位宽，例如 `ADDR_WIDTH` 和 `DATA_WIDTH`。
    - 这确保了模块可以灵活地适应不同的设计需求。

## 2. DPI-C 接口设计 (DPI-C Interface Design)

### 2.1. 函数命名规范 (Function Naming Convention)
- **准则**: 遵循统一的函数命名规范来区分 `DPI-C` 调用的方向。
- **实施**:
    - **`h2s_` (Hardware-to-Software)**: 用于 `import` 的 `DPI-C` 函数，表示数据或控制流从硬件到软件。
    - **`s2h_` (Software-to-Hardware)**: 用于 `export` 的 `DPI-C` 函数，表示数据或控制流从软件到硬件。

### 2.2. 使用 `bit` 类型传递宽向量
- **准则**: 当通过 DPI-C 接口向 C++ 传递位宽较大（例如，超过32或64位）的向量时，必须使用 `bit` 类型。
- **原因**: SystemVerilog 的 `logic` 类型是四状态的 (0, 1, X, Z)，在传递给期望二状态 (`svBitVecVal`) 的 C++ 函数时，可能会导致数据损坏或截断。`bit` 类型是二状态的，可以确保数据在接口两侧的完整性。

### 2.3. 使用初始化调用传递静态信息
- **准则**: 使用一个一次性的初始化 DPI-C 调用来传递模块的静态配置信息。
- **实施**:
    - 在仿真开始时（例如，在复位之后），调用一个专门的 `init` 函数，如 `h2s_xtor_init(ID, ADDR_WIDTH, DATA_WIDTH)`。
    - 这避免了在每一次数据传输的 DPI-C 调用中都重复传递这些静态参数，从而简化了通信协议并提高了效率。

### 2.4. 正确处理任意位宽数据
- **准则**: C++ 侧的 DPI-C 函数必须能够处理任意位宽的数据。
- **实施**:
    - C++ 函数签名应使用 `svBitVecVal*` 指针来接收数据，并附带一个 `int` 类型的参数来指定数据的位宽（以比特为单位）。
    - 避免使用固定大小的 C++ 类型（如 `unsigned int` 或 `uint64_t`）来接收可能超过其容量的数据。

### 2.5. 不要在设计中使用logic


## 3. DPI-C 交互模式 (DPI-C Interaction Modes)

RTL 代码在运行时通过 DPI-C 调用 C/C++ 函数时，根据硬件时钟是否停止，分为两种交互模式：阻塞（Blocking）和非阻塞（Non-blocking）。

### 3.1. 阻塞模式 (Blocking Mode)
- **定义**: 在阻塞模式下，当硬件（RTL）调用一个 DPI-C `import` 函数时，硬件侧的时钟会暂停，直到该 C/C++ 函数执行完毕并返回。
- **行为**: 仿真器和模拟器均支持此行为。
- **适用场景**:
    - 当软件侧可以立即处理请求并返回值时。
    - 当硬件需要等待软件侧的响应才能继续执行后续逻辑时（例如，同步读操作）。
- **函数签名**: 可以包含 `input`, `output`, `inout` 参数以及 `return` 值。

### 3.2. 非阻塞模式 (Non-blocking Mode)
- **定义**: 在非阻塞模式下，当硬件调用一个 DPI-C `import` 函数时，硬件侧的时钟不会停止，会立即继续执行。通过在 `import` 声明前添加 `(* is_nonblocking_dpi = 1 *)` 属性来实现。
- **行为**: **此模式仅存在于硬件仿真器（Emulator）中**，传统的 RTL 仿真器（Simulator）无法模拟这种时钟不停止的行为。
- **关键限制**:
    - **非阻塞 DPI-C `import` 函数的参数只能是 `input` 方向**。不允许有 `output`、`inout` 或 `return` 值。这是因为硬件不会等待函数返回，因此无法接收任何形式的同步返回值。

### 3.3. 在非阻塞模式下实现“读”操作
由于非阻塞调用无法直接返回值，为了实现一个硬件从软件读取数据的操作，必须采用“**请求-轮询-异步返回**”的机制。

1.  **定义“请求”函数 (Request Function)**:
    - 硬件侧定义一个非阻塞的 `import` DPI-C 函数，例如 `h2s_read_request(input int address)`。
    - 当硬件需要读取数据时，调用此函数向软件侧发送一个读请求。

2.  **定义“轮询”函数 (Polling Function)**:
    - 硬件侧定义并周期性地调用另一个非阻塞的 `import` DPI-C 函数，例如 `h2s_read_poll()`。
    - 此函数的唯一目的是触发软件侧检查是否有数据准备好。

3.  **定义“数据返回”函数 (Data Return Function)**:
    - 软件侧 `export` 一个 `s2h_read_data(output bit [DATA_WIDTH-1:0] data)` 的 DPI-C 函数给硬件。
    - 当软件侧收到 `h2s_read_request` 请求并准备好数据后，它会**在 `h2s_read_poll` 函数的实现内部**调用 `s2h_read_data` 函数，将数据异步地发送回硬件。

4.  **硬件侧 FIFO 设计**:
    - **准则**: **硬件侧必须实现一个 FIFO** 来接收由 `s2h_read_data` 函数异步返回的数据。
    - **原因**: `export` 函数的调用是异步的，它与硬件侧的时钟域无关。数据可能在任何时候被推送到硬件侧。使用 FIFO 是处理这种异步数据流、避免数据丢失并安全地将数据同步到硬件时钟域的唯一可靠方法。

### 3.4. 只支持function, 不支持task

## 4. SystemVerilog 编码与通用可综合性要求

为了确保 Xtor 中的硬件逻辑能够被正确地综合为硬件电路，必须遵循以下严格的编码规范。

### 4.1. 信号驱动规则 (Signal Driving Rules)
- **单一驱动源**:
    - **准则**: 严禁在多个 `always` 块或 `function` 中对同一个变量进行赋值。每个信号必须有唯一的驱动源。
    - **原因**: 多重驱动在硬件上会导致冲突（contention），在仿真中会产生不可预测的 `X` 值。
- **避免在单个 `always` 块中多次赋值**:
    - **准则**: 在一个 `always` 块中，一个信号在任何一个执行路径上只能被赋值一次。
    - **原因**: 多次赋值会使综合工具难以推断出正确的硬件逻辑，通常只有最后一次赋值会生效，这可能与设计意图不符，并可能生成低效的硬件。

### 4.2. 时序逻辑设计 (Sequential Logic Design)
- **同步复位**:
    - **准则**: 任何包含 `DPI-C` 调用的 `always_ff` 块都必须使用同步复位。在一般设计中也强烈推荐使用同步复位。
    - **原因**: 异步复位会使 `always` 块在时钟边沿之外被触发，这可能导致 `DPI-C` 调用在非预期的时刻执行。同时，同步复位能生成更可靠、更易于时序分析的电路。
- **阻塞与非阻塞赋值**:
    - **准则**: 在 `always_ff` (时序逻辑) 中必须使用非阻塞赋值 (`<=`)。在 `always_comb` (组合逻辑) 中必须使用阻塞赋值 (`=`)。
    - **原因**: 这是标准的 RTL 设计实践，可以避免仿真和综合之间的不匹配，防止由于错误的赋值方式导致的竞争条件。

### 4.3. 数据类型与变量 (Data Types and Variables)
- **禁止使用动态和抽象数据类型**:
    - **准则**: 严禁在可综合代码中使用 SystemVerilog 的动态或抽象数据类型。
    - **禁止使用**: `queue`, `mailbox`, `dynamic array`, `associative array`, `class`, `event`, `string`。
    - **原因**: 这些结构是为验证环境设计的，它们没有直接对应的硬件实现，因此是不可综合的。
- **变量声明位置**:
    - **准则**: 避免在 `always` 块内部声明局部变量。
    - **原因**: 并非所有的综合和仿真工具都能很好地支持在 `always` 块中声明的局部变量。为了确保代码的可移植性和兼容性，所有变量都应在模块的顶层进行声明。

### 4.4. 采样与状态检测 (Sampling and State Detection)
- **使用影子变量来检测状态变化**:
    - **准则**: 当需要在 `DPI-C` 调用中对 `always` 块驱动的信号进行采样时，应使用“影子变量”模式。
    - **实施**: 在 `always` 块中驱动主信号。在 `DPI-C` 调用中，将主信号的值赋给一个“影子”变量。通过比较主信号和影子变量的差异，可以可靠地检测到状态的变化。

## 5. 验证 (Verification)

### 5.1. 设计全面的测试用例
- **准则**: Testbench 必须覆盖各种边界情况和复杂场景。
- **实施**:
    - 除了基本的功能验证外，测试用例必须包括：
        - 使用模块支持的最大位宽进行数据传输。
        - 对非对齐地址（例如，非4字节或8字节对齐）进行访问。
        - 在设计中实例化多个模块，并验证它们可以并发、正确地工作。
        - 针对阻塞和非阻塞两种模式分别进行测试。